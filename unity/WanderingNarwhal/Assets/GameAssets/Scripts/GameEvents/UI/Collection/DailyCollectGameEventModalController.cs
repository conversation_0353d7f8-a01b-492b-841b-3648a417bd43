using System;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB
{ 
    public class DailyCollectGameEventModalController : EventBaseModalsController<IDailyCollectGameEventViewPresenter>
    {
        private CollectionGameEvent _gameEvent;
        private Action<bool> _closeCallback;
        private bool _okayButtonClicked;

        private IPlayerManager _playerManager;

        protected override void OnInitializeByContext(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            base.OnInitializeByContext(context);
        }

        public void Setup(CollectionGameEvent gameEvent, Action<bool> closeCallback)
        {
            _gameEvent = gameEvent;
            EventUid = gameEvent?.Uid;
            _closeCallback = closeCallback;

            var viewModel = DailyCollectGameEventViewModel.CreateViewModel(gameEvent);
            if (viewModel == null)
            {
                BDebug.LogError(LogCat.Collection, $"GameEvent {gameEvent?.Uid} has no milestones");
                return;
            }

            if (IsReady())
            {
                View.Refresh(viewModel);
            }
            else
            {
                DoWhenReady(() => { View.Refresh(viewModel); });
            }
        }

        protected override void OnCloseButtonClicked()
        {
            _okayButtonClicked = false;
            base.OnCloseButtonClicked();
        }

        protected override void OnShow()
        {
            base.OnShow();
            _okayButtonClicked = false;
            _gameEvent.TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.SnapShot);
            Subscribe();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        protected override void OnPostHide()
        {
            base.OnPostHide();

            _closeCallback(_okayButtonClicked);
            _closeCallback = null;
            _okayButtonClicked = false;

            _gameEvent.MarkClean();

            _gameEvent = null;
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OkButtonPressedEvent += OkayButtonHandler;
        }

        private void Unsubscribe()
        {
            View.OkButtonPressedEvent -= OkayButtonHandler;
        }

        private void OkayButtonHandler(Transform floatingTextAnchor)
        {
            _okayButtonClicked = true;
            base.OnCloseClicked();
        }

        protected override void PlayOpenningSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.GameEventModalOpenning);
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
            _closeCallback = null;
        }
    }
}