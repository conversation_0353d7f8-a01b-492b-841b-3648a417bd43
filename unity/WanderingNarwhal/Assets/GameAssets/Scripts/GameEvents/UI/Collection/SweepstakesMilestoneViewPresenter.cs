using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BebopBee.Core.Audio;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using GameAssets.Scripts.GameEvents.UI.Collection;
using GameAssets.Scripts.Hud;
using GameAssets.Scripts.Promotions.Banners.Pagination;
using GameAssets.Scripts.UI;
using GameAssets.Scripts.UI.OverlayDialog;
using UnityEngine;
using Button = UnityEngine.UI.Button;

namespace BBB
{
    public class SweepstakesMilestoneViewPresenter : ModalsViewPresenter, ISweepstakesMilestoneViewPresenter,
        IDestroyable
    {
        [SerializeField] private Transform _visualRoot;
        [SerializeField] private SweepstakesProgressBarWidget _progressBar;
        [SerializeField] private HorizontalCarousel _horizontalCarousel;
        [SerializeField] private IPaginationView _paginationView;
        [SerializeField] private GameObject _paginationPrefab;
        [SerializeField] private Transform _previousWinnersContainer;
        [SerializeField] private Button _infoButton;
        [SerializeField] private Button _termsButton;
        [SerializeField] private Button _progressBarButton;
        [SerializeField] private VideoItemWidget _videoItemPrefab;
        [SerializeField] private List<string> _participantTitleIds;
        [SerializeField] private List<string> _noneParticipantTitleIds;
        [SerializeField] private List<string> _participantSubTitleIds;
        [SerializeField] private List<string> _noneParticipantSubTitleIds;
        [SerializeField] private GameObject _mainVideoFrame;
        [SerializeField] private float _mainVideoShowDuration;
        [SerializeField] private float _mainVideoHideDuration;
        [SerializeField] private WebViewController _webViewController;
        [SerializeField] private Button _mainVideoCloseButton;
        [SerializeField] private Button _mainVideoScrimButton;
        [SerializeField] private CanvasGroup _mainVideoScrim;

        [Header("Continue play")] 
        [SerializeField] private Button _playButton;
        [SerializeField] private Button _signupButton;
        [SerializeField] private GameObject _playButtonRoot;
        [SerializeField] private GameObject _signupButtonRoot;
        
        private ILocalizationManager _localizationManager;
        private IGameEventResourceManager _gameEventResourceManager;
        
        private readonly List<VideoItemWidget> _videoItems = new();
        private TweenerCore<Vector3, Vector3, VectorOptions> _videoPlayerShowTween;
        private Vector3 _videoPlayerScale;
        
        private readonly List<SweepstakesItemView> _sweepstakesItemViews = new();
        private TweenerCore<float, float, FloatOptions> _scrimTween;
        private IOverlayDialogManager _overlayDialogManager;

        public event Action PlayButtonPressedEvent;
        public event Action SignupButtonPressedEvent;
        public event Action OnInfoButtonPressedEvent;
        public event Action OnTermsButtonPressedEvent;
        public event Action OnBannerButtonPressedEvent;
        public event Action<VideoItemWidget, int> OnVideoPlayed;

        protected override void InitWithContextInternal(IContext context)
        {
            base.InitWithContextInternal(context);
            _localizationManager = context.Resolve<ILocalizationManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();

            _mainVideoCloseButton.ReplaceOnClick(HideMainVideoPlayer);
            _mainVideoScrimButton.ReplaceOnClick(HideMainVideoPlayer);
            _progressBarButton.ReplaceOnClick(BannerButtonPressedHandler);
            _signupButton.ReplaceOnClick(OnSignUpButtonPressed);
            _playButton.ReplaceOnClick(OnPlayButtonPressed);
            _infoButton.ReplaceOnClick(OnInfoButtonPressed);
            _termsButton.AddOnClick(OnTermsButtonPressed);//do not call ReplaceOnClick
                                                          //to not break the internal flow of terms button
        }

        protected override void Awake()
        {
            base.Awake();
            _videoPlayerScale = _mainVideoFrame.transform.localScale;
        }

        private void OnTermsButtonPressed()
        {
            OnTermsButtonPressedEvent.SafeInvoke();
        }

        private void OnInfoButtonPressed()
        {
            OnInfoButtonPressedEvent.SafeInvoke();
        }

        protected override void OnShow()
        {
            base.OnShow();
            _progressBar.OnShow();
            _mainVideoFrame.SetActive(false);
            _mainVideoFrame.transform.localScale = Vector3.zero;
        }

        protected override void OnHide()
        {
            base.OnHide();
            HideMainVideoPlayer();
            _mainVideoFrame.SetActive(false);
            _videoPlayerShowTween?.Kill();
            _progressBar.OnHide();
        }

        private void OnSignUpButtonPressed()
        {
            SignupButtonPressedEvent.SafeInvoke();
        }

        private void OnPlayButtonPressed()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            PlayButtonPressedEvent.SafeInvoke();
        }

        private void ShowVideoInMainPlayer(VideoItemWidget videoItem)
        {
            _mainVideoFrame.SetActive(true);
            
            _videoPlayerShowTween?.Kill();
            _videoPlayerShowTween = _mainVideoFrame.transform.DOScale(_videoPlayerScale, _mainVideoShowDuration);
            _scrimTween?.Kill(true);
            _scrimTween = _mainVideoScrim.DOFade(1, _mainVideoShowDuration).SetEase(Ease.InCubic);
            
            _webViewController.Show(videoItem.URL);
        }

        public void HideMainVideoPlayer()
        {
            if (!_mainVideoFrame.activeSelf)
                return; // Already hidden, do nothing

            _videoPlayerShowTween?.Kill();
            _videoPlayerShowTween = _mainVideoFrame.transform.DOScale(0, _mainVideoHideDuration).OnComplete(() => 
                _mainVideoFrame.gameObject.SetActive(false));
            
            _scrimTween?.Kill(true);
            _scrimTween = _mainVideoScrim.DOFade(0, _mainVideoShowDuration).SetEase(Ease.InCubic);
            
            _webViewController.Hide();
        }

        public void Refresh(SweepstakesMilestoneViewModel viewModel)
        {
            _progressBar.Refresh(viewModel.ProgressbarData, _localizationManager, _overlayDialogManager);
            AdjustButtons(viewModel);
            PopulateCarousel(viewModel);
            AdjustTitle(viewModel);
            PopulatePreviousWinners(viewModel);
        }

        private void AdjustButtons(SweepstakesMilestoneViewModel viewModel)
        {
            _playButtonRoot.gameObject.SetActive(viewModel.IsEmailProvided);
            _signupButtonRoot.SetActive(!viewModel.IsEmailProvided);
        }

        private void AdjustTitle(SweepstakesMilestoneViewModel viewModel)
        {
            var milestoneNumber = viewModel.MilestoneNumber;
            for (var i = 0; i < _sweepstakesItemViews.Count; i++)
            {
                var isParticipant = i == 0
                    ? milestoneNumber > i
                    : milestoneNumber >= i;

                var titleId = isParticipant
                    ? _participantTitleIds[i]
                    : _noneParticipantTitleIds[i];

                var subTitleId = isParticipant
                    ? _participantSubTitleIds[i]
                    : _noneParticipantSubTitleIds[i];

                var banner = _sweepstakesItemViews[i];
                banner.SubTitleId.SetTextId(subTitleId);
                banner.TitleId.SetTextId(titleId);
            }
        }
        
        private void PopulateCarousel(SweepstakesMilestoneViewModel viewModel)
        {
            if (_horizontalCarousel.ItemsCount > 0)
            {
                _horizontalCarousel.SetActivePage(viewModel.BannerIndex);
                return;
            }

            _horizontalCarousel.AutoScrollDelayTime = viewModel.BannerStayDuration;
            _horizontalCarousel.OverrideScrollDuration(viewModel.BannerScrollDuration);

            var bannersCount = viewModel.ProgressbarData.Milestones.Count + 1;//+1 for event banner
            _paginationView.Setup(bannersCount, _paginationPrefab);
            
            for (var i = 0; i < bannersCount; i++)
            {
                var milestoneBannerPrefab =
                    _gameEventResourceManager.GetGenericAsset<GameObject>(viewModel.EventUid,
                        $"{GameEventResKeys.EligibilityMilestoneBanner}_{i}");
                
                if (milestoneBannerPrefab == null)
                    continue;
                
                var bannerObject = Instantiate(milestoneBannerPrefab, transform);
                var sweepstakesItemView = bannerObject.GetComponent<SweepstakesItemView>();
                _sweepstakesItemViews.Add(sweepstakesItemView);
                sweepstakesItemView.BannerButton.ReplaceOnClick(BannerButtonPressedHandler);
                _horizontalCarousel.AddChild(bannerObject);
            }
            _horizontalCarousel.SetActivePage(viewModel.BannerIndex);
        }

        private void BannerButtonPressedHandler()
        {
            OnBannerButtonPressedEvent.SafeInvoke();
        }

        private void PopulatePreviousWinners(SweepstakesMilestoneViewModel viewModel)
        {
            if (_videoItems.Count > 0) return;
            
            foreach (var video in viewModel.WinnersVideos)
            {
                var videoItemWidget = Instantiate(_videoItemPrefab, _previousWinnersContainer);
                var viewPort = _previousWinnersContainer.transform.parent.RectTransform();
                videoItemWidget.Setup(video.Title, video.Url, video.Thumbnail, viewPort);
                _videoItems.Add(videoItemWidget);
                
                SubscribeVideoListeners(videoItemWidget);
            }
        }

        private void SubscribeVideoListeners(VideoItemWidget videoItemWidget)
        {
            UnsubscribeVideoListeners(videoItemWidget);
            videoItemWidget.OnVideoPlayed += OnVideoPlayedHandler;
        }

        private void UnsubscribeVideoListeners(VideoItemWidget videoItemWidget)
        {
            if (videoItemWidget == null) return;
            videoItemWidget.OnVideoPlayed -= OnVideoPlayedHandler;
        }
        
        private void OnVideoPlayedHandler(VideoItemWidget videoItemWidget)
        {
            _overlayDialogManager.HideAllOverlayDialogs();
            OnVideoPlayed?.SafeInvoke(videoItemWidget, _videoItems.IndexOf(videoItemWidget));
            ShowVideoInMainPlayer(videoItemWidget);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_termsButton != null)
            {
                _termsButton.onClick.RemoveListener(OnTermsButtonPressed);
            }

            if (_previousWinnersContainer != null)
            {
                for (var i = 0; i < _previousWinnersContainer.childCount; i++)
                {
                    UnsubscribeVideoListeners(_previousWinnersContainer.GetChild(i).GetComponent<VideoItemWidget>());
                }
            }
            _videoItems.Clear();
            _sweepstakesItemViews.Clear();
            _scrimTween?.Kill();
            _scrimTween = null;
            _videoPlayerShowTween?.Kill();
            _videoPlayerShowTween = null;
        }
    }
}