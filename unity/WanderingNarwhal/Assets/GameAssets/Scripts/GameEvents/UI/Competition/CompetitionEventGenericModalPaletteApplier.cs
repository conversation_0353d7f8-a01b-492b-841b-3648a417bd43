using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.UI.Extensions;

namespace BBB
{
    public class CompetitionEventGenericModalPaletteApplier : BbbMonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI[] _titles;
        [SerializeField] private Image[] _frameOutlines;
        [SerializeField] private Image[] _frameBGs;
        [SerializeField] private Gradient2[] _gradient2s;

        public void Setup(EventGenericModalPaletteSettings eventGenericModalPaletteSettings)
        {
            if (eventGenericModalPaletteSettings == null)
                return;

            foreach (var title in _titles)
            {
                if (title == null)
                    continue;

                title.color = eventGenericModalPaletteSettings.TitleColor;
            }


            foreach (var frameOutline in _frameOutlines)
            {
                if (frameOutline == null)
                    continue;

                frameOutline.color = eventGenericModalPaletteSettings.FrameOutlineColor;
            }

            foreach (var frameBG in _frameBGs)
            {
                if (frameBG == null)
                    continue;

                frameBG.color = eventGenericModalPaletteSettings.FrameBackgroundBaseColor;
            }

            foreach (var gradient2 in _gradient2s)
            {
                if (gradient2 == null)
                    continue;

                gradient2.EffectGradient = eventGenericModalPaletteSettings.FrameBackgroundGradient;
            }
        }
    }
}