using System;
using BBB.Core;

namespace BBB
{
    public enum LeagueChangeMessageType
    {
        None = 0,
        Promotion = 1,
        Demotion = 2,
        Restart = 3
    }

    public class CompetitionEventPromotionDemotionModalController : BaseModalsController<ICompetitionEventPromotionDemotionViewPresenter>
    {
        private Action _onCloseCallback;

        public void Setup(LeagueChangeMessageType leagueChangeType, GameEventBase gameEvent, Action onCloseCallback)
        {
            _onCloseCallback = onCloseCallback;
            if (IsReady())
            {
                View.Setup(leagueChangeType, gameEvent);
            }
            else
            {
                void WhenReady()
                {
                    View.Setup(leagueChangeType, gameEvent);
                }

                DoWhenReady(WhenReady);
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            _onCloseCallback?.Invoke();
            _onCloseCallback = null;
        }
    }
}