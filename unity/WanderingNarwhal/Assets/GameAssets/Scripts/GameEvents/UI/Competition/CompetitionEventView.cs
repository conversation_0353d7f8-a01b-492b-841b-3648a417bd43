using BBB.Core;
using BBB.Core.UI;
using BBB.DI;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class CompetitionEventView : BbbMonoBehaviour
    {
        [SerializeField] private Image _backgroundImage;
        [SerializeField] private GameEventReplaceableGo[] _compositionReplaceableGo;

        private IGameEventResourceManager _resourceManager;

        public void Init(IContext context)
        {
            _resourceManager = context.Resolve<IGameEventResourceManager>();
            foreach (var gameEventReplaceableGo in _compositionReplaceableGo)
            {
                gameEventReplaceableGo.Init(_resourceManager);
            }
        }

        public void Refresh(CompetitionEventViewModel viewModel)
        {
            _resourceManager
                .GetSpriteAsync(viewModel.GameEventUid, GameEventResKeys.MainBackgroundImage)
                .ContinueWith(sprite =>
                {
                    if (sprite == null)
                    {
                        BDebug.LogError(LogCat.Resources,$"Couldn't load MainBackgroundImage for event {viewModel.GameEventUid}");
                    }
                    _backgroundImage.sprite = sprite;
                });
            
            foreach (var gameEventReplaceableGo in _compositionReplaceableGo)
            {
                gameEventReplaceableGo.Refresh(viewModel.GameEventUid, GameEventResKeys.MainAnimatedComposition);
                gameEventReplaceableGo.InvokeOnGo(go =>
                {
                    var leagueViewApplier = go.GetComponent<ILeagueViewApplier>();
                    if (leagueViewApplier != null)
                    {
                        var leagueToApply = viewModel.ViewMode == CompetitionEventViewMode.League ? viewModel.League : League.Silver;
                        leagueViewApplier.Apply(leagueToApply);
                    }
                });
            }
        }
    }
}