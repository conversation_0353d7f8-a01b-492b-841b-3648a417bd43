using System;
using System.Collections.Generic;

namespace BBB
{
    public enum CompetitionEventViewMode
    {
        League = 0,
        Qualification = 1
    }

    public class CompetitionEventViewModel
    {
        //YA: Update this value when ever we change the number of leagues
        private const float OneLeagueRatio = 1f / 3f;
        private const string YouArePromotedNameLocText = "YOU_ARE_PROMOTED_NAME_LOC";
        private const string YouArePromotedDescLocText = "YOU_ARE_PROMOTED_DESC_LOC";
        private const string YouAreDemotedNameLocText = "YOU_ARE_DEMOTED_NAME_LOC";
        private const string YouAreDemotedDescLocText = "YOU_ARE_DEMOTED_DESC_LOC";
        private const string YourCurrentLeagueLocKey = "YOUR_CURRENT_LEAGUE_LOC";
        private const string YourGoalBeforeFireLocKey = "YOUR_LEAD_GOAL_LOC";
        private const string YourGoalInFireLocKey = "YOUR_LEAD_FIRE_GOAL_LOC"; 

        public CompetitionGameEvent CompetitionGameEvent { get; private set; }
        public string GameEventUid { get; private set; }
        public string NameText { get; private set; }
        public string DescriptionText { get; private set; }
        public string LongText { get; private set; }

        public League League { get; private set; }
        public string LeagueNameLoc { get; private set; }
        public float ProgressToSet { get; private set; }
        public float ProgressDeltaToAnimate { get; private set; }
        public int CurScoreToSet { get; private set; }
        public int TargetScoreToAnimate { get; private set; }
        public int QualificationTarget { get; private set; }
        public string BottomDescriptionText { get; private set; }

        public Func<string, TimeSpan> TimeLeftGetter { get; private set; }
        public CompetitionEventViewMode ViewMode { get; private set; }
        public float ForecastProgressToSet { get; private set; }
        public float ForecastProgressDeltaToAnimate { get; private set; }
        public Dictionary<string, int> QualificationReward { get; private set; }

        public static CompetitionEventViewModel CreateViewModel(CompetitionEventOpeningMode mode,
            CompetitionGameEvent gameEvent, ILocalizationManager localizationManager)
        {
            var viewModel = new CompetitionEventViewModel();

            viewModel.CompetitionGameEvent = gameEvent;
            viewModel.GameEventUid = gameEvent.Uid;
            viewModel.QualificationTarget = gameEvent.QualificationTarget;
            viewModel.QualificationReward = gameEvent.QualificationReward;

            var currentLeague = gameEvent.EventLeaderboard.CurrentLeague;
            
            viewModel.League = currentLeague;
            viewModel.LeagueNameLoc = currentLeague.ToNameLoc();

            var status = gameEvent.Status;

            if (status.ShouldRunTimer())
                viewModel.TimeLeftGetter = gameEvent.GetTimeLeft;
            else
                viewModel.TimeLeftGetter = GetZeroTime;

            switch (mode)
            {
                case CompetitionEventOpeningMode.Qualification:
                {
                    viewModel.NameText = gameEvent.NameText;
                    viewModel.DescriptionText = gameEvent.DescriptionText;
                    viewModel.LongText = gameEvent.DetailsText;

                    viewModel.ProgressToSet = gameEvent.ProgressRatio - gameEvent.ProgressRatioLastDelta;
                    viewModel.ProgressDeltaToAnimate = gameEvent.ProgressRatioLastDelta;
                    
                    viewModel.CurScoreToSet = gameEvent.CurrentScore-gameEvent.LastAddedScoreDelta;
                    viewModel.TargetScoreToAnimate = Math.Min(gameEvent.CurrentScore, gameEvent.QualificationTarget);

                    viewModel.ViewMode = CompetitionEventViewMode.Qualification;
                    break;
                }
                case CompetitionEventOpeningMode.Intro:
                {
                    viewModel.NameText = gameEvent.NameText;
                    viewModel.DescriptionText = gameEvent.DescriptionText;
                    viewModel.LongText = localizationManager.getLocalizedText(YourCurrentLeagueLocKey);

                    if (currentLeague == League.Fire)
                    {
                        viewModel.BottomDescriptionText = localizationManager.getLocalizedText(YourGoalInFireLocKey);
                    }
                    else
                    {
                        var leagueShortName =
                            localizationManager.getLocalizedText(currentLeague.GetNextLeague().ToShortNameLoc());
                        viewModel.BottomDescriptionText = localizationManager
                            .getLocalizedTextWithArgs(YourGoalBeforeFireLocKey, leagueShortName);
                    }

                    viewModel.ProgressToSet = (int)currentLeague * OneLeagueRatio;
                    viewModel.CurScoreToSet = 0;
                    viewModel.TargetScoreToAnimate = gameEvent.CurrentScore;

                    viewModel.ForecastProgressToSet = (int)currentLeague * OneLeagueRatio;
                    viewModel.ForecastProgressDeltaToAnimate = OneLeagueRatio;

                    viewModel.ViewMode = CompetitionEventViewMode.League;
                    break;
                }
                case CompetitionEventOpeningMode.Promotion:
                {
                    viewModel.NameText = localizationManager.getLocalizedText(YouArePromotedNameLocText);
                    viewModel.DescriptionText = localizationManager.getLocalizedText(YouArePromotedDescLocText);
                    viewModel.LongText = localizationManager.getLocalizedText(YourCurrentLeagueLocKey);

                    var currentLeagueIndex = (int)currentLeague;

                    viewModel.ProgressToSet = currentLeagueIndex * OneLeagueRatio;
                    viewModel.ProgressDeltaToAnimate = OneLeagueRatio;
                    viewModel.CurScoreToSet = gameEvent.CurrentScore;


                    viewModel.ViewMode = CompetitionEventViewMode.League;
                    break;
                }

                case CompetitionEventOpeningMode.Demotion:
                {
                    viewModel.NameText = localizationManager.getLocalizedText(YouAreDemotedNameLocText);
                    viewModel.DescriptionText = localizationManager.getLocalizedText(YouAreDemotedDescLocText);
                    viewModel.LongText = localizationManager.getLocalizedText(YourCurrentLeagueLocKey);

                    var currentLeagueIndex = (int)currentLeague;

                    viewModel.ProgressToSet = currentLeagueIndex * OneLeagueRatio;
                    viewModel.ProgressDeltaToAnimate = -OneLeagueRatio;
                    viewModel.CurScoreToSet = gameEvent.CurrentScore;

                    break;
                }
                case CompetitionEventOpeningMode.Information:
                {
                    viewModel.NameText = gameEvent.NameText;
                    viewModel.DescriptionText = gameEvent.DescriptionText;
                    viewModel.LongText = localizationManager.getLocalizedText(YourCurrentLeagueLocKey);

                    if (currentLeague == League.Fire)
                    {
                        viewModel.BottomDescriptionText = localizationManager.getLocalizedText(YourGoalInFireLocKey);
                    }
                    else
                    {
                        var leagueShortName =
                            localizationManager.getLocalizedText(currentLeague.GetNextLeague().ToShortNameLoc());
                        viewModel.BottomDescriptionText = localizationManager
                            .getLocalizedTextWithArgs(YourGoalBeforeFireLocKey, leagueShortName);
                    }
                    
                    
                    viewModel.ProgressToSet = (int)currentLeague * OneLeagueRatio;
                    viewModel.CurScoreToSet = gameEvent.CurrentScore;

                    viewModel.ForecastProgressToSet = (int) (currentLeague + 1) * OneLeagueRatio;
                    
                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException(nameof(mode), mode, null);
            }
            

            return viewModel;
        }

        private static TimeSpan GetZeroTime(string arg = null)
        {
            return TimeSpan.Zero;
        }
    }
}