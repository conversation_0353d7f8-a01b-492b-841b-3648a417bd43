using System.Collections.Generic;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.UI;
using BBB.UI.Core;
using BBB.Wallet;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class CompetitionInfoLayoutGenericInitializable : ContextedUiBehaviour
    {
        [SerializeField] private UIRewardComponent[] _fireLeagueTopRewards;
        [SerializeField] private Image _topRewardImage;

        private IGameEventManager _gameEventManager;
        private CurrencyIconsLoader _currencyIconsLoader;

        protected override void InitWithContextInternal(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();

            foreach (var uiRewardComponent in _fireLeagueTopRewards)
            {
                uiRewardComponent.Init(context);
            }
        }

        public void Init(string gameEventUid)
        {
            LazyInit();
            InitPlacesRewards(gameEventUid);
        }

        private void InitPlacesRewards(string gameEventUid)
        {
            var gameEvent = _gameEventManager.FindGameEventByUid(gameEventUid) as CompetitionGameEvent;
            if (gameEvent == null)
                return;

            var prizePlaceCount = gameEvent.GetPrizePlaceCount();
            for (var i = 0; i < prizePlaceCount; i++)
            {
                var rewardDictionary = gameEvent.GetOrderedRewardForPlace(League.Fire, i, out var ordering);
                var rewardDictionaryClone = new Dictionary<string, int>(rewardDictionary);
                if (gameEvent is not SideMapGameEvent)
                {
                    foreach (var currencyUid in rewardDictionary.Keys)
                    {
                        if (InventoryItems.OrderOfPhysicalRewards.Contains(currencyUid))
                            rewardDictionaryClone.Remove(currencyUid);
                    }
                }

                if (i < _fireLeagueTopRewards.Length && _fireLeagueTopRewards[i] != null)
                {
                    _fireLeagueTopRewards[i].SetupReward(rewardDictionaryClone, ordering);
                }

                if (i == 0 && _topRewardImage != null)
                {
                    _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(ordering[0]).ContinueWith(sprite => _topRewardImage.sprite = sprite);
                }
            }
        }

        protected override void OnDestroy()
        {
            if (_topRewardImage != null)
            {
                _topRewardImage.sprite = null;
            }

            base.OnDestroy();
        }
    }
}