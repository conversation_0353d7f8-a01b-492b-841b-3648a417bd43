using BBB.UI.LoopScrollRect;
using UnityEngine;

namespace BBB
{
    public class EventLeaderboardDemotionItemView : LoopScrollListItem
    {
        private const string DefaultLoc = "N_PLACE_TO_DEMOTE_LOC";

        [SerializeField] private LocalizedTextPro _localizedText;

        public void Setup(int numberOfPlayers, EventLeaderboardCommonViewData commonViewData)
        {
            if(commonViewData.IsEventEnded && commonViewData.AmIinDemotionZone.Invoke())
            {
                _localizedText.Text.text = "";
            }
            else
            {
                _localizedText.Text.spriteAsset = commonViewData.SpriteAsset;
                _localizedText.SetTextId(DefaultLoc);
                _localizedText.FormatSetArgs(numberOfPlayers);
            }

            _localizedText.gameObject.SetActive(!commonViewData.IsEventEnded);
        }

        private void ResetLocalizedTextAsset()
        {
            _localizedText.Text.spriteAsset = null;

        }
        public override void OnRelease()
        {
            ResetLocalizedTextAsset();
            base.OnRelease();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ResetLocalizedTextAsset();
        }
    }
}