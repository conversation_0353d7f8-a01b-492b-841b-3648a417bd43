using BBB.UI.LoopScrollRect;
using TMPro;
using UnityEngine;

namespace BBB
{
    public class EventLeaderboardFireLeagueItemView : LoopScrollListItem
    {
        private const string EventEndedLoc = "EVENT_ENDED_PROMOTION_LOC";
        private const string DefaultLoc = "N_PLACE_TO_BEAT_FIRE_LOC";
    
        [SerializeField] private LocalizedTextPro _localizedText;

        public void Setup(int numberOfPlayers, EventLeaderboardCommonViewData commonViewData)
        {
            if(commonViewData.IsEventEnded)
            {
                _localizedText.SetTextId(EventEndedLoc);
            }
            else
            {
                _localizedText.Text.spriteAsset = commonViewData.SpriteAsset;        
                _localizedText.SetTextId(DefaultLoc);          
                _localizedText.FormatSetArgs(numberOfPlayers);
            }
        }

        public override void OnRelease()
        {
            _localizedText.Text.spriteAsset = null;
            base.OnRelease();
        }

        
    }
}