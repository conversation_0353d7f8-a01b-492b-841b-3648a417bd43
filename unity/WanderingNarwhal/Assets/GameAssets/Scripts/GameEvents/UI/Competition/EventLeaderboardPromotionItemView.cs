using BBB.UI.LoopScrollRect;
using TMPro;
using UnityEngine;

namespace BBB
{
    public class EventLeaderboardPromotionItemView : LoopScrollListItem
    {
        private const string EventEndedLoc = "EVENT_ENDED_PROMOTION_LOC";
        private const string DefaultLoc = "N_PLACE_TO_PROMOTE_LOC";
    
        [SerializeField] private LocalizedTextPro _localizedText;

        public void Setup(int numberOfPlayers, EventLeaderboardCommonViewData commonViewData)
        {
            if(commonViewData.IsEventEnded && commonViewData.AmIinPromotionZone.Invoke())
            {
                _localizedText.SetTextId(EventEndedLoc);
            }
            else
            {
                _localizedText.Text.spriteAsset = commonViewData.SpriteAsset;        
                _localizedText.SetTextId(DefaultLoc);          
                _localizedText.FormatSetArgs(numberOfPlayers);
            }
        }

        private void ResetLocalizedTextAsset()
        {
            if (_localizedText != null && _localizedText.Text != null)
            {
                _localizedText.Text.spriteAsset = null;
            }
        }
        public override void OnRelease()
        {
            ResetLocalizedTextAsset();
            base.OnRelease();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ResetLocalizedTextAsset();
        }
    }
}