using System;
using BBB.UI.Transitions;
using GameAssets.Scripts.UI.Palettes;
using UnityEngine;

namespace BBB
{
    [CreateAssetMenu(menuName = "BBB/Events/GameEventSettings", fileName = "_game_event_settings", order = 1)]
    public class GameEventSettings : ScriptableObject
    {
        public string MainCharacterNameLocalizationUid = string.Empty;
        public string PromotionAnimationName = string.Empty;
        public string DemotionAnimationName = string.Empty;
        
        /// <summary>
        /// Tile to sprite mapping.
        /// When level starts it will select one tile from this list and replace it's sprite (and will collect this tile as event score).
        /// If this list is empty then event will not replace normal tiles and will collect boosters score instead.
        /// </summary>
        /// <remarks>
        /// Competition game event can collect boosters as a score, or normal tiles.
        /// If normal tiles mapping is not defined, then boosters score collection type is used, otherwise - normal tile score collection. -VK 
        /// </remarks>
        public TileKindSpritePair[] normalTileReskin;

        [Serializable]
        public struct TileKindSpritePair
        {
            public TileKinds tileKind;
            public string sprite;
        }

        public EventGenericModalPaletteSettings EventGenericModalPaletteSettings;
        
        [field: SerializeField]
        public PaletteSettings Palette { get; private set; }
        
        [field: SerializeField]
        public Sprite[] CellBgs { get; private set; }
        
        [field: SerializeField]
        public TransitionPrefab TransitionPrefab { get; private set; }
    }
}