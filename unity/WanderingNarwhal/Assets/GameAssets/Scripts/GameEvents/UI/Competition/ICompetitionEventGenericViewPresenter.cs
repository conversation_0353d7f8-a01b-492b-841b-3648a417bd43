using System;
using BBB.UI.Core;
using UnityEngine;

namespace BBB
{
    public interface ICompetitionEventGenericViewPresenter : IViewPresenter
    {  
        event Action<Transform> OkayButtonPressedEvent;
        event Action CollectButtonPressedEvent;
        event Action RewardButtonPressedEvent;
        event Action LeagueRewardButtonPressedEvent;
        event Action LotteryButtonPressedEvent;
        void Refresh(CompetitionEventViewModel viewModel);
    }
}