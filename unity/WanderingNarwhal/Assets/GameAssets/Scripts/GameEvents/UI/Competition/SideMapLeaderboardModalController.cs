using BBB.DI;
using BBB.EndGameEvents;
using BBB.UI;

namespace BBB
{
    public class SideMapLeaderboardModalController : EventLeaderboardModalController
    {
        private IScreensManager _screensManager;
        private IScreensBuilder _screensBuilder;
        
        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);

            _screensManager = context.Resolve<IScreensManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
        }

        protected override void OnOkayButtonPressed()
        {
            if (_screensManager.GetCurrentController() is SideMapController sideMapController)
            {
                sideMapController.TryToOpenStartRoundModal();
            }
            else if (GameEvent is SideMapGameEvent { Status: GameEventStatus.Active } sideMapGameEvent)
            {
                if (_screensBuilder.CurrentScreenType != sideMapGameEvent.HomeScreen)
                {
                    LoadingProcessTracker.LogShowScreen(sideMapGameEvent.HomeScreen.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "SideMapLeaderboard");
                    _screensBuilder.ShowScreen(sideMapGameEvent.HomeScreen);
                }
            }
            
            base.OnOkayButtonPressed();
        }
    }
}