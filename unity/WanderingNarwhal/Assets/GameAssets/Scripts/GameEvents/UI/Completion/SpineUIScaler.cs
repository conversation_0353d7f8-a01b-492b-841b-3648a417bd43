using System.Collections;
using Spine.Unity;
using UnityEngine;

namespace BBB
{
    public class SpineUIScaler : BbbMonoBehaviour
    {
        [SerializeField] private SkeletonGraphic _spine;
        [SerializeField] private RectTransform _targetSizeRect;

        protected override void OnEnable()
        {
            if (_spine == null || _targetSizeRect == null)
                return;

            if (!Refresh())
            {
                StartCoroutine(RefreshCoroutine());
            }
        }

        private IEnumerator RefreshCoroutine()
        {
            while (_spine.GetLastMesh() == null)
                yield return null;

            Refresh();
        }

        private bool Refresh()
        {
            var spineMesh = _spine.GetLastMesh();
            if (spineMesh == null)
                return false;

            var rectCorners = new Vector3 [4];
            _targetSizeRect.GetWorldCorners(rectCorners);
            
            var rectSize = rectCorners[2] - rectCorners[0];
            var spineSize = spineMesh.bounds.size;
            
            _spine.transform.localScale = new Vector3(rectSize.x / spineSize.x, rectSize.y / spineSize.y, 1f);

            return true;
        }
    }
}