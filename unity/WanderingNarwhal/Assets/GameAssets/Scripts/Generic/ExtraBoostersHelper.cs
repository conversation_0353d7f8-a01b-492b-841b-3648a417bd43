using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Tutorial.Core;
using UnityEngine;

namespace BBB
{
    public class ExtraBoostersHelper: IContextInitializable
    {
        public class PendingBoosters
        {
            public readonly Action<string, Coords?, object> BoosterAddedCallback;
            public List<AutoBoostInstance> Boosters { get; }

            public PendingBoosters(List<AutoBoostInstance> boosters, Action<string, Coords?, object> boosterAddedCallback)
            {
                Boosters = boosters;
                BoosterAddedCallback = boosterAddedCallback;
            }
        }

        private LevelHolder _levelHolder;
        private IAutoBoostInputFactory _autoBoostInputFactory;
        private Match3SimulationPlayer _match3SimulationPlayer;
        private GameController _gameController;
        private TileResourceSelector _tileResources;
        private M3SpawnSystem _spawnSystem;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;
        private ITutorialPlaybackController _tutorialPlaybackController;

        public void InitializeByContext(IContext context)
        {
            _levelHolder = context.Resolve<LevelHolder>();
            _gameController = context.Resolve<GameController>();
            _autoBoostInputFactory = context.Resolve<IAutoBoostInputFactory>();
            _match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            _tileResources = context.Resolve<TileResourceSelector>();
            _spawnSystem = context.Resolve<M3SpawnSystem>();
            _levelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            _tutorialPlaybackController = context.Resolve<ITutorialPlaybackController>();
        }

        public bool CanPlaceAllBoosters(List<AutoBoostInstance> boostInstances)
        {
            var level = _levelHolder.level;
            if (_tutorialPlaybackController.IsLevelBusyWithTutorial(level.LevelUid, level.Stage)) return false;
            var canPlaceAllExtraBoosters = AutoBoosterInputFactory.CanPlaceAllExtraBoosters(level.Grid, boostInstances.Count);
            return canPlaceAllExtraBoosters;
        }

        public bool CanPlaceBoosterToRandomCell()
        {
            var level = _levelHolder?.level;
            if (level == null || _match3SimulationPlayer == null) return false;
            return !_match3SimulationPlayer.IsPlayingSimulationFor(level.Grid);
        }

        public void PlaceBoosterToRandomCell(AutoBoostInstance booster, int remainingMoves, ref List<AutoBoostInstance> result)
        {
            var level = _levelHolder.level;
            foreach (var autoBoostInput in _autoBoostInputFactory.GetInputForAutoBoost(new[]{booster}, level.Grid, 
                         false, true))
            {
                var input = autoBoostInput.Input;
                if (autoBoostInput.AutoBoostUid.IsNullOrEmpty())
                {
                    Debug.LogError($"Trying to apply equipped autoBoost, but boost uid is empty. Input type={input.GetType().Name}");
                    continue;
                }

                //Simulation is force completed if already playing within the function
                _match3SimulationPlayer.SimulateSync(_gameController.OriginalGrid, level.Grid, level, input, remainingMoves);

                var singleCellInput = (PlayerInputItemSingleCell)input;
                if (result == null) continue;
                
                object dir = autoBoostInput.Input switch
                {
                    PlayerInputItemCreateLineBreaker lineBreaker => lineBreaker.Direction,
                    PlayerInputItemCreateLineBreakerButler lineBreakerButler => lineBreakerButler.Direction,
                    _ => null
                };
                
                result.Add(new AutoBoostInstance(autoBoostInput.AutoBoostUid, singleCellInput.TargetCoord, dir));
            }
        }

        public Coords? GetCellPosForBooster(AutoBoostInstance booster, int remainingMoves)
        {
            var level = _levelHolder.level;
            foreach (var autoBoostInput in _autoBoostInputFactory.GetInputForAutoBoost(new[]{booster}, level.Grid,
                         true, false))
            {
                var input = autoBoostInput.Input;
                if (autoBoostInput.AutoBoostUid.IsNullOrEmpty())
                {
                    Debug.LogError($"Trying to apply equipped autoBoost, but boost uid is empty. Input type={input.GetType().Name}");
                    continue;
                }

                var simParams = new SimulationInputParams
                {
                    UsedKinds = _gameController.Level.UsedKinds,
                    Settings = _gameController.Settings,
                    SimulateLoopException = _gameController.SimulateLoopException,
                    TurnsLimit = _gameController.Level.TurnsLimit,
                    OriginalGrid = _gameController.OriginalGrid.Clone(),
                };

                var spawnSystem = _spawnSystem.Clone();
                spawnSystem.Setup(level);
                var gravitySystem = new GravitySystem(simParams, _gameController.GoalsSystem, _tileResources, null, _levelAnalyticsReporter);
                var simulation = gravitySystem.CreateSimulationSync(level.Grid.Clone(), input, remainingMoves, null, spawnSystem);
                if (simulation.Result is SimulationResult.Success or SimulationResult.SuccessWin)
                {
                    var singleCellInput = (PlayerInputItemSingleCell)input;
                    return singleCellInput.TargetCoord;
                }
            }
            return null;
        }

    }
}
