using System.Collections.Generic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB
{
    /// <summary>
    /// Simulation main parameters for the level.
    /// </summary>
    public class SimulationInputParams
    {
        public bool SimulateLoopException;
        public M3Settings Settings;
        public List<TileKinds> UsedKinds;
        public int TurnsLimit;
        public bool IsBatchMode;
        public bool InitialLoop;
        public Grid OriginalGrid;
        public ExtraBoostersHelper.PendingBoosters PendingBoosters;
        public bool GoalsCompleted;
    }
}