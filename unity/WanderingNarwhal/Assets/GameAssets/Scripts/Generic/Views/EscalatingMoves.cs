using System;
using BBB;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EscalatingMoves : BbbMonoBehaviour
{
    [SerializeField] private TextMeshProUGUI _movesCount;
    [SerializeField] private Animator _animator;
    [SerializeField] private Image[] _badgeItemsImages;
    [SerializeField] private TextMeshProUGUI[] _badgeItemsTexts;
    public Action EscalatingMovesAnimationEnded;
    private static readonly int MovesPlusBoosters = Animator.StringToHash("PlayMovesPlusBoosters");
    private static readonly int JustMoves = Animator.StringToHash("PlayJustMoves");

    public void PlayMovesPlusBoosters(int movesCount)
    {
        _movesCount.text = movesCount.ToString();
        _animator.SetTrigger(MovesPlusBoosters);
    }

    public void PlayJustMoves(int movesCount)
    {
        _movesCount.text = movesCount.ToString();
        _animator.SetTrigger(JustMoves);
    }

    public void EnableBadge()
    {
        foreach (var badgeItem in _badgeItemsImages)
        {
            badgeItem.enabled = true;
        }
        
        foreach (var badgeItem in _badgeItemsTexts)
        {
            badgeItem.enabled = true;
        }
    }

    public void DisableBadge()
    {
        foreach (var badgeItem in _badgeItemsImages)
        {
            badgeItem.enabled = false;
        }
        
        foreach (var badgeItem in _badgeItemsTexts)
        {
            badgeItem.enabled = false;
        }
    }

    public void AnimationEnded()
    {
        EscalatingMovesAnimationEnded?.Invoke();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        EscalatingMovesAnimationEnded = null;
    }
}
