using System;
using System.Collections.Generic;
using BBB.UI.Core;
using GameAssets.Scripts.Generic.Carrots;

namespace BBB.UI.Level
{
    public interface ILevelSuccessViewPresenter : IViewPresenter
    {
        event Action OnExitButtonClicked;
        event Action OnShowDidiEvent;
        event Action OnHidden; 

        void Setup(GameEventMatch3ManagersCollection gameEventCollection,
            IRaceEventMatch3Manager raceEventMatch3Manager,
            Match3ResourceProvider resourceProvider,
            CurrencyIconsLoader iconsLoader, ILevel level, int score, int initialStage, bool canSendChallenge,
            LevelSuccessAdData levelSuccessAdData, List<Match3CharacterAnim> fennec<PERSON>nimations, CarrotsData carrotsData);
    }
}