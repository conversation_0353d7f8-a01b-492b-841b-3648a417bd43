using System;
using BBB.UI.Core;
using FBConfig;
using UnityEngine;

namespace BBB.UI.Level.Views
{
    public interface IOutOfMovesViewPresenter : IViewPresenter
    {
        event Action TriedToPurchase;
        event Action OpenedGacha;
        event Action ShowAnimCompleted;
        event Action<string> TriedToUseInventory;
        event Action ShowMatch3Board;
        event Action HideMatch3Board;
        float DelayBeforeWalletHide { get; }
        void SetupOfferConfig(OfferConfig offerConfig, int defaultMovesOffered, int movesOffered);
        void SetupFreeMove(bool haveFreeMove);
        void SetupSkipBanner(bool skipBanner, ILevel level);
        void SetupGridVerticalPositions(Tuple<float, float> positions, Camera levelCamera, GoalResourcesPanel goalResourcesPanel);
        void UpdateContent(OutOfMovesViewContentMode contentMode, OutOfMovesContentData outOfMovesViewContentData, bool skipOutro = false);
        string GetCurrentScreenInfoForDauAnalytics();
    }
}