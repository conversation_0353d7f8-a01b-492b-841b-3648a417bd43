using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Screens;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using FBConfig;
using GameAssets.Scripts.Map;
using UnityEngine;

namespace BBB.UI.Level.Views
{
    public interface ILevelViewPresenter : IViewPresenter
    {
        bool IsLevelLoaded();
    }

    /// <summary>
    /// View presenter for Level screen and for HelpingHands screen.
    /// </summary>
    public class LevelScreenCombinedViewPresenter : ScreensViewPresenter, ILevelViewPresenter
    {
        [SerializeField]
        private Transform _levelScreen;

        private Match3ResourceProvider _resourceProvider;
        private LevelControllerBase _currentLevelController;
        private LevelController _levelController;
        private SideMapEventLevelController _levelControllerSideMap;
        private ScreenType _currentScreen;
        private IConfig _config;
        private IPlayerManager _playerManager;
        private IScreensManager _screensManager;
        private readonly LevelHolder _levelHolder = new LevelHolder();
        private Match3ResourceProvider _match3ResourceProvider;

        public LevelControllerBase CurrentLevelController => _currentLevelController;

        protected override void OnShow()
        {
            base.OnShow();
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(false);

            _currentLevelController.OnShow();
        }

        protected override void OnHide()
        {
            _currentLevelController.OnHide();
        }

        protected override void OnTransitionCompleted()
        {
            var scenesDict = _config.Get<ScenesConfig>();
            var scenesConfig = scenesDict[IEpisodicScenesManager.SceneToLoad];

            if (!scenesConfig.IsNull() && !scenesConfig.LevelMusicId.IsNullOrEmpty())
            {
                AudioProxy.TryPlayMusic(scenesConfig.LevelMusicId);
            }
        }

        protected override void OnContextInitialization(UnityContext unityContext)
        {
            if (_match3ResourceProvider == null)
            {
                _match3ResourceProvider = new Match3ResourceProvider();
            }

            unityContext.AddServiceToRegister<Match3ResourceProvider>(_match3ResourceProvider);
            unityContext.AddServiceToRegister<LevelHolder>(_levelHolder);
        }

        protected override void OnContextInitialized(IContext context)
        {
            if (_levelScreen == null)
            {
                _levelScreen = transform.Find("LevelScreen");
            }

            _levelScreen.gameObject.SetActive(false);

            if (_resourceProvider == null)
            {
                _resourceProvider = context.Resolve<Match3ResourceProvider>();
                _screensManager = context.Resolve<IScreensManager>();
                _currentScreen = _screensManager.GetCurrentScreenType();
                _levelController = _levelScreen.gameObject.AddComponent<LevelController>();
                _levelControllerSideMap = _levelScreen.gameObject.AddComponent<SideMapEventLevelController>();
            }

            _config = context.Resolve<IConfig>();
            _playerManager = context.Resolve<IPlayerManager>();
            
            _currentScreen = _screensManager.IsTransitionInProgress
                ? _screensManager.GetTransitionTargetScreenType()
                : _screensManager.GetCurrentScreenType();

            base.OnContextInitialized(context);

            _currentLevelController = _currentScreen switch
            {
                ScreenType.SideMapLevelScreen => _levelControllerSideMap,
                _ => _levelController
            };

            _currentLevelController.Init(context);
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            _levelControllerSideMap?.DisposeContext();
            _levelController?.DisposeContext();
        }

        public bool IsLevelLoaded()
        {
            return _currentLevelController.IsLevelInitialized();
        }

        public override void CacheResources(IResourceCache cache)
        {
            _resourceProvider.CacheResources(cache);
        }

        public override void DisposeResources()
        {
            _resourceProvider.DisposeResources();
        }
    }
}