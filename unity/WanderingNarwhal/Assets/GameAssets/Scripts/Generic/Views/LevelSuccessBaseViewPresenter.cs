using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.GameAssets.UI.Level.Scripts.Views.Panels;
using BBB.Generic.Modal;
using BBB.Map;
using BBB.Match3.Systems;
using BBB.Screens;
using BBB.Social;
using BBB.Wallet;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.UnityEngineExtensions;
using CustomLayout;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Generic.Carrots;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Level
{
    public class LevelSuccessBaseViewPresenter : ModalsViewPresenter, ILevelSuccessViewPresenter
    {
        public enum ViewState
        {
            None = 0,
            WasReplayRequested = 1,
            SharingSuggested = 2,
            Shared = 3,
            Didi = 4
        }

        private const string TitleTextKey = "LEVEL_COMPLETED";
        private const string RoundTitleTextKey = "ROUND_COMPLETED";
        private const string PreviewTextKey = "HUD_STAMPS_MENU_PREVIEW";
        private const string PlayNextTextKey = "PLAY_NEXT";
        private const string SendChallengeButtonTitleKey = "SEND_CHALLENGE";
        private const string ClaimScoreTextKey = "CLAIM_SCORE";
        private const string PlayTrivia = "PLAY_TRIVIA";
        private const string TriviaRequiresConnection = "TRIVIA_REQUIRES_CONNECTION";
        private const string MaxTriviaReached = "MAX_TRIVIA_REACHED";
        private const string CarrotsMainTextUId = "LEVEL_SUCCESS_CARROT_MAIN_TEXT";
        private const string LevelTitleTextId = "LEVEL_TITLE";

        private const string BounceTriggerName = "Bounce";
        private const string NormalTriggerName = "Normal";

        private const float AdWatchScoreMultiplier = 2;

        private const int SpineBaseTrackIndex = 0;

        public event Action OnExitButtonClicked;
        public event Action OnShowDidiEvent;
        public event Action OnHidden;

        private static readonly int SdbHiddenAnim = Animator.StringToHash("Hidden");
        private static readonly int SdbIntroAnim = Animator.StringToHash("Intro");
        private static readonly int SdbShowAnim = Animator.StringToHash("ShowSdb");
        private static readonly int SdbCompleteAnim = Animator.StringToHash("Complete");
        private static readonly int SdbShowBubbleAnim = Animator.StringToHash("ShowBubble");
        private static readonly int SdbCompletedAnim = Animator.StringToHash("Completed");

        [Header("Settings")]
        [SerializeField] private float _delayBeforeButtonAnimationStart = 5f;
        [SerializeField] private bool _useCurrencyInstances;

        [Header("References")]
        [SerializeField] private Animator _goButtonAnimator;
        [SerializeField] private Match3CharacterController _characterController;
        [SerializeField] private ItemsListPanel _rewardsList;
        [SerializeField] private CustomLayoutGroup _customLayoutGroup;
        [SerializeField] private RectTransform buttonsContainer;
        [SerializeField] private Button _backgroundButton;
        [SerializeField] private Button _goButton;
        [SerializeField] private Transform _goButtonScaleRoot;
        [SerializeField] private Vector3 _goButtonScaleWithoutAd = Vector3.one;
        [SerializeField] private Vector3 _goButtonScaleWithAd = Vector3.one;
        [SerializeField] private Button _adButton;
        [SerializeField] private LocalizedTextPro _adButtonText;
        [SerializeField] private Image _adButtonCurrencyIcon;
        [SerializeField] private TextMeshProUGUI _adMultiplier;
        [SerializeField] private TextMeshProUGUI _titleText;
        [SerializeField] private TextMeshProUGUI _subtitleText;
        [SerializeField] private LocalizedTextPro _okayTextLocalized;
        [SerializeField] private Image _okButtonCurrencyIcon;
        [SerializeField] private GameObject[] _fennecParts;
        [SerializeField] private LocalizedTextPro _didiSpeechBubbleText;
        [SerializeField] private VisitorIconController _visitorIconController;
        [SerializeField] private StagePaletteApplier _stagePaletteApplier;
        [SerializeField] private Button _noThanksButton;
        [SerializeField] private float _pauseBeforeStopRecording = 1f;
        [SerializeField] private Button _skipButton;
        [SerializeField] private bool _debugAutoskip = true;
        [SerializeField] private CompetitionEventMultiplierWidget _multiplierWidget;
        [SerializeField] private float _delayBeforeMultiplierWidgetAnimation = 0.3f;

        [Header("Super Disco Ball")]
        [SerializeField] private GameObject[] _sdbParts;
        [SerializeField] private Image _sdbProgressImage;
        [SerializeField] private TextMeshProUGUI _sdbProgressText;
        [SerializeField] private SkeletonGraphic _sdbSkeleton;
        [SerializeField] private string _sdbIdleAnimation;
        [SerializeField] private string _sdbActiveAnimation;
        [SerializeField] private SkeletonGraphic _mrWiggles;
        [SerializeField] private string _mrWigglesAppearAnimation;
        [SerializeField] private string _mrWigglesIdleAnimation;
        [SerializeField] private Animator _sdbAnimator;
        [SerializeField] private float _sdbAnimationStartDelay = 0.7f;
        [SerializeField] private float _sdbProgressChangeTime = 1.2f;
        [SerializeField] private Ease _sdbProgressChangeEaseType = Ease.Linear;
        [SerializeField] private float _sdbAnimationStepWaitTime = 0.5f;
        [SerializeField] private float _sdbIntroChangeTime = 0.7f;
        [SerializeField] private float _sdbCompleteChangeTime = 0.7f;

        [Header("Carrots")]
        [SerializeField] private CarrotItem _carrotItem;
        [SerializeField] private GameObject _carrotParent;

        private IGameEventResourceManager _gameEventResourceManager;
        private ILocalizationManager _localizationManager;
        private ChallengeTriviaManager _challengeTriviaManager;
        private SdbManager _sdbManager;
        private GameEventMatch3ManagersCollection _gameEventMatch3ManagerCollection;

        private readonly List<ItemListPanelViewData> _tempViewDataList = new();
        private ViewState _viewState = ViewState.None;
        private List<Match3CharacterAnim> _fennecAnimations;
        private Tweener _buttonsDelayTweener;
        private Tweener _multiplierWidgetTweener;
        private Tween _sdbProgressTween;
        private VideoAdManager _videoAdManager;
        private GenericModalFactory _genericModalFactory;
        private LevelSuccessAdData _levelSuccessAdData;
        private IUIWalletManager _uiWalletManager;

        public ViewState ViewStateField => _viewState;

        protected ILocationManager LocationManager { get; private set; }
        protected IScreensManager ScreensManager { get; private set; }
        protected IGameEventManager GameEventManager { get; private set; }

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            if (_gameEventResourceManager == null)
            {
                // This may be called multiple times,
                // Initialize global (unchangable) serives only once.
                InitOnce(previousContext);
            }

            _skipButton.gameObject.SetActive(false);
            _noThanksButton.gameObject.SetActive(false);
            _goButtonAnimator.gameObject.SetActive(false);
            _adButton.gameObject.SetActive(false);

            _okayTextLocalized.gameObject.SetActive(true);
        }

        protected virtual void InitOnce(IContext previousContext)
        {
            _gameEventResourceManager = previousContext.Resolve<IGameEventResourceManager>();
            _backgroundButton.ReplaceOnClick(BackgroundButtonHandler);
            _goButton.ReplaceOnClick(OnGoButton);
            _adButton.ReplaceOnClick(OnAdButton);
            _noThanksButton.ReplaceOnClick(OnNoThanksButton);
            _localizationManager = previousContext.Resolve<ILocalizationManager>();
            _challengeTriviaManager = previousContext.Resolve<ChallengeTriviaManager>();
            _sdbManager = previousContext.Resolve<SdbManager>();
            LocationManager = previousContext.Resolve<ILocationManager>();
            GameEventManager = previousContext.Resolve<IGameEventManager>();
            ScreensManager = previousContext.Resolve<IScreensManager>();
            _videoAdManager = previousContext.Resolve<VideoAdManager>();
            _genericModalFactory = previousContext.Resolve<GenericModalFactory>();
            _uiWalletManager = previousContext.Resolve<IUIWalletManager>();
            _characterController.InitializeByContext(previousContext);
        }

        public virtual void Setup(GameEventMatch3ManagersCollection gameEventMatch3ManagerCollection,
            IRaceEventMatch3Manager raceEventMatch3Manager,
            Match3ResourceProvider resourceProvider,
            CurrencyIconsLoader iconsLoader, ILevel level, int score, int initialStage, bool canSendChallenge,
            LevelSuccessAdData levelSuccessAdData, List<Match3CharacterAnim> fennecAnimations, CarrotsData carrotsData)
        {
            _gameEventMatch3ManagerCollection = gameEventMatch3ManagerCollection;
            _levelSuccessAdData = levelSuccessAdData;
            _fennecAnimations = fennecAnimations;
            SetupRewardView(level, iconsLoader, canSendChallenge);

            switch (_viewState)
            {
                case ViewState.Didi:
                    _skipButton.ReplaceOnClick(OnDidiSkipButton);
                    break;
                default:
                    _skipButton.ReplaceOnClick(OnDefaultSkipButton);
                    break;
            }

            var paletteStage = level.Config.GetPaletteStage();
            _subtitleText.text = _localizationManager.getLocalizedText(GameAssets.Scripts.Player.Level.DificultyNameISOByStage[paletteStage]);
            _stagePaletteApplier.Apply(paletteStage);
            var currentScreen = ScreensManager.GetCurrentScreenType();
            if ((currentScreen & ScreenType.SideMap) > 0)
            {
                if (GameEventManager.GetCurrentSideMapEvent() is SideMapGameEvent currentEvent)
                {
                    _titleText.text = _localizationManager.getLocalizedTextWithArgs(RoundTitleTextKey, currentEvent.CurrentRound);
                }

                _subtitleText.enabled = false;
            }
            else
            {
                _titleText.text = _localizationManager.getLocalizedTextWithArgs(TitleTextKey, level.LevelName);
                _subtitleText.enabled = true;
            }

            SetupCarrotsData(carrotsData);
            Load(resourceProvider, level, score, initialStage);
        }

        protected virtual void Load(Match3ResourceProvider resourceProvider, ILevel level, int score, int initialStage)
        {
        }

        private void SetupCarrotsData(CarrotsData carrotsData)
        {
            if (_carrotParent == null || _carrotItem == null)
                return;

            ResetCarrotsObject();

            if (carrotsData.IsNull() || carrotsData.IsEmpty())
                return;

            var carrotName = _localizationManager.getLocalizedText(carrotsData.NameTextUid);
            var levelName = $"{_localizationManager.getLocalizedText(LevelTitleTextId)} {carrotsData.LevelName}";
            _carrotItem.SetCarrotsImage(carrotsData.PrefabUid);
            _carrotItem.SetDescriptionText(CarrotsMainTextUId, carrotName, levelName);
            _carrotItem.enabled = true;
            _carrotParent.SetActive(true);
        }

        private void ResetCarrotsObject()
        {
            if (_carrotParent != null)
            {
                _carrotParent.SetActive(false);
            }

            if (_carrotItem != null)
            {
                _carrotItem.enabled = false;
            }
        }

        private struct Entry
        {
            public string CurrencyUid;
            public ItemListPanelViewData ViewData;
        }
        
        private void SetupRewardView(ILevel level, CurrencyIconsLoader iconsLoader, bool canSendChallenge)
        {
            _fennecParts.Enable(false);
            _sdbParts.Enable(false);

            var currentScreen = ScreensManager.GetCurrentScreenType();

            var stageCurrency = level.GetStageCurrency();
            var entries = new List<Entry>();

            if (!stageCurrency.IsNullOrEmpty() && (currentScreen & ScreenType.SideMap) == 0)
            {
                entries.Add(new Entry {
                    CurrencyUid = stageCurrency,
                    ViewData = new ItemListPanelViewData {
                        Uid = stageCurrency,
                        Sprite = null,
                        TotalCount = 1
                    }
                });
            }

            foreach (var kvp in level.RewardsDictionary)
            {
                entries.Add(new Entry {
                    CurrencyUid = kvp.Key,
                    ViewData = new ItemListPanelViewData {
                        Uid = kvp.Key,
                        Sprite = null,
                        TotalCount = kvp.Value
                    }
                });
            }
            
            foreach (var gem in _gameEventMatch3ManagerCollection)
            {
                var gameEvent = gem?.ActiveGameEvent;
                if (gameEvent is SweepstakesGameEvent)
                {
                    var entry = new Entry
                    {
                        CurrencyUid = InventoryItems.SweepstakesEventScore,
                        ViewData = new ItemListPanelViewData
                        {
                            Uid = InventoryItems.SweepstakesEventScore,
                            Sprite = null,
                            TotalCount = gameEvent.LastAddedScoreDelta,
                            SpecialItem = true
                        }
                    };
                    entries.Add(entry);
                    
                    _gameEventResourceManager
                        .GetSpriteAsync(gameEvent.Uid, gameEvent.GetScoreIconSpriteNameForCurrentMilestone())
                        .ContinueWith(sprite => entry.ViewData.Sprite = sprite);
                }
            }
            
            foreach (var entry in entries)
            {
                // we want to have exclusive stars animations, so we need uid remapping
                const string staSpriteArrayUid = "star";
                var spriteArrayUid = entry.ViewData.Uid == WalletResources.HardCurrency ? staSpriteArrayUid : entry.ViewData.Uid;
                iconsLoader.LoadAndGetCurrencySpriteSheetSpritesAsync(spriteArrayUid, false).ContinueWith(sprites =>
                {
                    if (!sprites.IsNullOrEmpty() && sprites.Length > 1)
                    {
                        entry.ViewData.SpriteArray = sprites;
                    }
                });
                
            }

            if (canSendChallenge)
            {
                _viewState = ViewState.Didi;
            }
            else
            {
                _viewState = ViewState.None;
                _fennecParts.Enable(true);
            }

            _skipButton.gameObject.SetActive(_viewState == ViewState.None);

            _tempViewDataList.Clear();
            foreach (var e in entries)
            {
                _tempViewDataList.Add(e.ViewData);
            }

            _uiWalletManager.ResetCurrencyInstances();
            _rewardsList.Setup(_tempViewDataList, (go, data) =>
            {
                if (_useCurrencyInstances && data.Uid.IsNullOrEmpty())
                {
                    _uiWalletManager.SetCurrencyInstance(data.Uid, go);
                }
            });

            _customLayoutGroup.RefreshEverything();

            var currenciesToLoad = new HashSet<string>();
            foreach (var e in entries)
            {
                if (!string.IsNullOrEmpty(e.CurrencyUid))
                {
                    currenciesToLoad.Add(e.CurrencyUid);
                }
            }
            
            var tasks = new List<UniTask>(currenciesToLoad.Count);
            foreach (var currency in currenciesToLoad)
            {
                tasks.Add(
                    iconsLoader.LoadAndGetCurrencySpriteAsync(currency)
                        .ContinueWith(sprite =>
                        {
                            for (var i = 0; i < entries.Count; i++)
                            {
                                if (entries[i].CurrencyUid != currency) 
                                    continue;

                                entries[i].ViewData.Sprite = sprite;
                                _tempViewDataList[i].Sprite = sprite;
                            }
                        })
                );
            }

            UniTask.WhenAll(tasks).ContinueWith(() =>
            {
                _rewardsList.Refresh();
                ClearTempViewDataList();
            });
        }

        private void ClearTempViewDataList()
        {
            foreach (var viewData in _tempViewDataList)
            {
                viewData.Sprite = null;
            }

            _tempViewDataList.Clear();
        }

        private void SetGoButtonSendChallengeView()
        {
            _sdbParts.Enable(true);
            _sdbAnimator.ResetAllParameters();
            SetDidiText();
            StartSdbAnimations();
        }

        private void SetSdbProgress(float progress, int totalWinsCount)
        {
            _sdbProgressImage.fillAmount = progress / totalWinsCount;
            _sdbProgressText.text = $"{Mathf.FloorToInt(progress)}/{totalWinsCount}";
        }

        private void StartSdbAnimations()
        {
            var max = _challengeTriviaManager.TotalWinsCount;
            var initial = Mathf.Clamp(_challengeTriviaManager.LastShownScoreWhenLevelStart, 0, max - 1);

            SetSdbProgress(initial, max);
            _sdbAnimator.SetTrigger(SdbHiddenAnim);
            _mrWiggles.AnimationState.ClearTracks();
            _mrWiggles.Skeleton.SetToSetupPose();

            var sequence = DOTween.Sequence();
            sequence.AppendInterval(_sdbAnimationStartDelay);
            sequence.AppendCallback(() =>
            {
                _sdbAnimator.SetTrigger(SdbIntroAnim);
                _mrWiggles.AnimationState.SetAnimation(SpineBaseTrackIndex, _mrWigglesAppearAnimation, false);
            });
            sequence.AppendInterval(_sdbIntroChangeTime);
            sequence.AppendCallback(() =>
            {
                _sdbAnimator.SetTrigger(SdbShowAnim);
                _mrWiggles.AnimationState.SetAnimation(SpineBaseTrackIndex, _mrWigglesIdleAnimation, true);
                _sdbSkeleton.AnimationState.SetAnimation(SpineBaseTrackIndex, _sdbIdleAnimation, true);
            });
            sequence.AppendInterval(_sdbAnimationStepWaitTime);
            sequence.Append(
                DOTween.To(() => initial, progress => { SetSdbProgress(progress, max); }, (float)max,
                    _sdbProgressChangeTime).SetEase(_sdbProgressChangeEaseType)
            );
            sequence.AppendCallback(() =>
            {
                SetSdbProgress(max, max);
                _sdbAnimator.SetTrigger(SdbCompleteAnim);
                _sdbSkeleton.AnimationState.SetAnimation(SpineBaseTrackIndex, _sdbActiveAnimation, true);
            });
            sequence.AppendInterval(_sdbCompleteChangeTime);
            sequence.OnComplete(() =>
            {
                _sdbAnimator.SetTrigger(SdbShowBubbleAnim);
                _sdbProgressTween = null;
            });
            _sdbProgressTween = sequence;
        }

        private void SetSdbCompleted()
        {
            var max = _challengeTriviaManager.TotalWinsCount;
            SetSdbProgress(max, max);
            _sdbAnimator.SetTrigger(SdbCompletedAnim);
            _mrWiggles.AnimationState.ClearTracks();
            _mrWiggles.Skeleton.SetToSetupPose();
            _mrWiggles.AnimationState.SetAnimation(SpineBaseTrackIndex, _mrWigglesIdleAnimation, true);
            _sdbSkeleton.AnimationState.SetAnimation(SpineBaseTrackIndex, _sdbActiveAnimation, true);
        }

        protected void OnWaitComplete()
        {
            if (_viewState == ViewState.Didi)
                _skipButton.gameObject.SetActive(true);

            Animate();
        }

        protected virtual void Animate()
        {
            OnAnimsCompleted();
        }

        protected void OnAnimsCompleted()
        {
            if (_viewState == ViewState.None)
            {
                _skipButton.gameObject.SetActive(false);
            }
        }

        protected override void OnShow()
        {
            _characterController.PrepareFennec();

            StartMainFlow();

            if (_viewState == ViewState.None || _viewState == ViewState.Didi)
            {
                _buttonsDelayTweener = Rx.Invoke(_delayBeforeButtonAnimationStart, InitButtonsState);
            }

            if (ScreensManager.GetCurrentScreenType() is ScreenType.LevelScreen or ScreenType.SideMapLevelScreen)
            {
                _multiplierWidgetTweener = Rx.Invoke(_delayBeforeMultiplierWidgetAnimation, _ =>
                {
                    _multiplierWidget.Refresh();
                    _multiplierWidgetTweener = null;
                });
            }
            else
            {
                _multiplierWidget.OnHide();
            }
        }

        protected virtual void StartMainFlow()
        {
            OnWaitComplete();
        }

        private void GoButtonAnimAction()
        {
            if (_goButtonAnimator == null)
                return;

            _goButtonAnimator.gameObject.SetActive(true);

            switch (_viewState)
            {
                case ViewState.Didi:
                    _skipButton.gameObject.SetActive(false);
                    SetGoButtonSendChallengeView();
                    break;
                default:
                    SetGoButtonDefaultText();
                    break;
            }
        }

        private void SetDidiText()
        {
            _okButtonCurrencyIcon.gameObject.SetActive(false);

            if (_challengeTriviaManager.ReachedMaxActiveChallengesLimit)
            {
                SetCharacterButtonAndBubbleText(PlayNextTextKey, MaxTriviaReached);
                return;
            }

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                SetCharacterButtonAndBubbleText(SendChallengeButtonTitleKey, TriviaRequiresConnection);
                return;
            }

            SetCharacterButtonAndBubbleText(SendChallengeButtonTitleKey, PlayTrivia);
        }

        private void SetCharacterButtonAndBubbleText(string buttonText, string bubbleText)
        {
            _okayTextLocalized.SetTextId(buttonText);
            _didiSpeechBubbleText.SetTextId(bubbleText);
        }

        private void SetGoButtonDefaultText()
        {
            PlayFennec();

            _okayTextLocalized.SetTextId(PlayNextTextKey);
            _okButtonCurrencyIcon.gameObject.SetActive(false);

            if (_levelSuccessAdData.ShouldShowAdButton(_videoAdManager))
            {
                // if we showing ad, we prioritize showing the same event currency as on ad button
                _levelSuccessAdData
                    .GetCurrencySpriteAsync(_gameEventResourceManager)
                    .ContinueWith(sprite =>
                    {
                        SetEventCurrency(_levelSuccessAdData.Score, sprite);
                    });
            }
            else
            {
                var gameEvent = GameEventManager.GetCurrentSideMapEvent();
                IGameEventMatch3Manager gameEventMatch3Manager = null;
                if (gameEvent != null)
                {
                    gameEventMatch3Manager = _gameEventMatch3ManagerCollection.GetFirstManager(manager => manager.ActiveGameEvent != null && manager.ActiveGameEvent.Uid == gameEvent.Uid);
                }

                gameEventMatch3Manager ??= _gameEventMatch3ManagerCollection.GetFirstManager(manager => manager.ActiveGameEvent != null && manager.LastCollectedScores > 0);

                gameEvent = gameEventMatch3Manager?.ActiveGameEvent;
                if (gameEventMatch3Manager != null && gameEvent is { ShowCurrencyOnLevelSuccessPlayNextButton: true })
                {
                    var score = gameEventMatch3Manager.LastCollectedScores;
                    _gameEventResourceManager
                        .GetSpriteAsync(gameEvent.Uid, gameEvent.GetScoreIconSpriteNameForCurrentMilestone())
                        .ContinueWith(sprite =>
                        {
                            SetEventCurrency(score, sprite);
                        });
                }
            }

            return;

            void SetEventCurrency(int score, Sprite sprite)
            {
                _okayTextLocalized.SetTextId(ClaimScoreTextKey, score);
                _okButtonCurrencyIcon.sprite = sprite;
                _okButtonCurrencyIcon.gameObject.SetActive(true);
            }
        }

        private void PlayFennec()
        {
            var animationToPlay = Match3CharacterAnim.Celebration_WinScreen;

            if (_fennecAnimations is { Count: > 0 })
            {
                var index = RandomSystem.Next(_fennecAnimations.Count);
                animationToPlay = _fennecAnimations[index];
            }

            if (!_characterController.HasAnimation(animationToPlay))
            {
                animationToPlay = Match3CharacterAnim.Idle;
                _characterController.FlipX();
            }

            _characterController.Lock(false);
            _characterController.SetCurrentIdleAnim(Match3CharacterAnim.Idle);
            _characterController.PlayAnimation(animationToPlay, true);
            _characterController.Freeze(false);
        }

        private void InitButtonsState(long _)
        {
            _buttonsDelayTweener = null;
            InitAdButtonState();
            _goButton.animationTriggers.normalTrigger = _adButton.gameObject.activeSelf ? NormalTriggerName : BounceTriggerName;

            GoButtonAnimAction();
            LayoutRebuilder.ForceRebuildLayoutImmediate(buttonsContainer);
        }

        private void InitAdButtonState()
        {
            _goButtonScaleRoot.localScale = _goButtonScaleWithoutAd;
            if (_viewState == ViewState.Didi || !_levelSuccessAdData.ShouldShowAdButton(_videoAdManager))
            {
                _adButton.gameObject.SetActive(false);
                return;
            }

            _adButtonText.SetTextId(ClaimScoreTextKey, _levelSuccessAdData.Score * AdWatchScoreMultiplier);
            _levelSuccessAdData.GetCurrencySpriteAsync(_gameEventResourceManager).ContinueWith(sprite => _adButtonCurrencyIcon.sprite = sprite);
            _adMultiplier.text = $"{AdWatchScoreMultiplier}x";
            _adButton.gameObject.SetActive(true);
            _goButtonScaleRoot.localScale = _goButtonScaleWithAd;
        }

        protected virtual void OnDefaultSkipButton()
        {
            KillTweens();
            InitButtonsState(0);
            _skipButton.gameObject.SetActive(false);
            OnGoButton();
        }

        private void OnDidiSkipButton()
        {
            KillTweens();
            ResetCarrotsObject();
            InitButtonsState(0);
        }

        private void KillTweens()
        {
            _buttonsDelayTweener?.Kill();
            _buttonsDelayTweener = null;
            _multiplierWidgetTweener?.Kill();
            _multiplierWidgetTweener = null;
            _sdbProgressTween?.Kill();
            _sdbProgressTween = null;
        }

        protected override void OnHide()
        {
            _viewState = ViewState.None;
            _goButtonAnimator.Rebind();
            _goButtonAnimator.gameObject.SetActive(false);
            _adButton.gameObject.SetActive(false);
            _noThanksButton.gameObject.SetActive(false);
            _skipButton.gameObject.SetActive(false);
            _visitorIconController.Hide();
            _okayTextLocalized.gameObject.SetActive(true);
            _multiplierWidget.OnHide();

            base.OnHide();
            OnHidden?.Invoke();
        }

        private void BackgroundButtonHandler()
        {
            // there is no safe state condition for interactibility, the only design rule that was requested is that it should work if go button is only one available to be clicked
            if (_skipButton.gameObject.activeSelf || _noThanksButton.gameObject.activeSelf)
                return;

            // || _adButton.gameObject.activeSelf - intentionally excluded by request from Rajeev

            if (!_goButton.gameObject.activeSelf)
                return;

            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            OnGoButton();
        }

        private void OnGoButton()
        {
            switch (_viewState)
            {
                case ViewState.Didi:
                    var sdbInProgress = _sdbProgressTween?.IsActive() ?? false;
                    KillTweens();
                    if (sdbInProgress)
                    {
                        SetSdbCompleted();
                    }

                    if (_challengeTriviaManager.ReachedMaxActiveChallengesLimit)
                    {
                        HideLevelSuccess();
                    }
                    else if (!ConnectivityStatusManager.ConnectivityReachable)
                    {
                        _genericModalFactory.ShowNoConnectionModal(_ => HideLevelSuccess());
                    }
                    else
                    {
                        _uiWalletManager.ResetCurrencyInstances();
                        OnShowDidiEvent.SafeInvoke();
                        OnCloseButton();
                    }

                    break;
                case ViewState.None:
                    HideLevelSuccess();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private void HideLevelSuccess()
        {
            if (_sdbManager.SdbUnlockedAfterLevel)
            {
                ShowSdbReward();
                return;
            }

            _rewardsList.TriggerSpriteAnimation();
            _viewState = ViewState.None;
            ExitButton();
        }

        private void ShowSdbReward()
        {
            _sdbManager.ShowSdbRewardModal(() =>
            {
                _viewState = ViewState.None;
                ExitButton();
            });
        }

        private void OnAdButton()
        {
            _adButton.interactable = false;

            float timeScale = Time.timeScale;
            _videoAdManager.TryToPlayVideoAd(_levelSuccessAdData.AdEngagementSource, _levelSuccessAdData.AdPlacementUid, () =>
            {
                Time.timeScale = timeScale;
                _adButton.interactable = true;
                _levelSuccessAdData.AdWatchedCallback?.Invoke();
            }, () =>
            {
                Time.timeScale = timeScale;
                _adButton.interactable = true;
            }, () =>
            {
                ExitButton();
                Time.timeScale = 0;
            });
        }

        private void ExitButton()
        {
            AudioProxy.PlaySound(Match3SoundIds.PlayNext);
            OnExitButtonClicked.SafeInvoke();
        }

        private void OnNoThanksButton()
        {
            _viewState = ViewState.None;
            ExitButton();
        }

        protected override void OnDestroy()
        {
            _okButtonCurrencyIcon.sprite = null;
            ClearTempViewDataList();
            base.OnDestroy();
        }
    }
}