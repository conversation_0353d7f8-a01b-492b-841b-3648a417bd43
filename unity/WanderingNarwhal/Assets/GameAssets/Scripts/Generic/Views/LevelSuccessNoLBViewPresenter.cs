using System.Collections.Generic;
using BBB;
using BBB.UI.Level;
using GameAssets.Scripts.Generic.Carrots;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.Generic.Views
{
    public sealed class LevelSuccessNoLBViewPresenter : LevelSuccessBaseViewPresenter
    {
        [SerializeField] private TextMeshProUGUI _locationName;
        [SerializeField] private AsyncLoadableImage _locationFlag;

        public override void Setup(GameEventMatch3ManagersCollection gameEventMatch3ManagerCollection,
            IRaceEventMatch3Manager raceEventMatch3Manager,
            Match3ResourceProvider resourceProvider,
            CurrencyIconsLoader iconsLoader, ILevel level, int score, int initialStage, bool canSendChallenge,
            LevelSuccessAdData levelSuccessAdData, List<Match3CharacterAnim> fennecAnimations, CarrotsData carrotsData)
        {
            base.Setup(gameEventMatch3ManagerCollection, raceEventMatch3Manager, resourceProvider,
                iconsLoader, level, score, initialStage, canSendChallenge, levelSuccessAdData, fennecAnimations, carrotsData);
            if (ScreensManager.GetCurrentScreenType() == ScreenType.SideMapLevelScreen)
            {
                var gameEvent = GameEventManager.GetCurrentSideMapEvent();
                if (gameEvent != null)
                {
                    _locationName.text = gameEvent.NameText;
                    _locationFlag.Show($"{gameEvent.Uid}_FlagSprite");
                    return;
                }
            }
            
            var location = LocationManager.MainProgressionLocation;
            _locationName.text = location.LocalizedName;
            _locationFlag.Show(location.Flag);
        }
    }
}