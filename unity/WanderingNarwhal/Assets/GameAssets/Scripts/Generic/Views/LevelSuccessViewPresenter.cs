using System.Collections;
using BBB.DI;
using BebopBee.Core;
using DG.Tweening;
using UnityEngine;

namespace BBB.UI.Level
{
    public class LevelSuccessViewPresenter : LevelSuccessBaseViewPresenter
    {
        [SerializeField] private float _delayBeforeCardAnimationStart = 3f;
        [SerializeField] private SocialCardsPanel _socialCardsPanel;
        
        private ILeaderboardResultFilter _leaderboardResultFilter;
        private ICoroutineExecutor _coroutineExecutor;

        private Tweener _cardAnimDelayTweener;
        private bool _skippedFully;

        protected override void InitOnce(IContext previousContext)
        {
            base.InitOnce(previousContext);

            _coroutineExecutor = previousContext.Resolve<ICoroutineExecutor>();
            _leaderboardResultFilter = new AfterM3LeaderboardResultFilter(previousContext);
            _socialCardsPanel.Init(previousContext);
            _socialCardsPanel.PrewarmPool(((AfterM3LeaderboardResultFilter)_leaderboardResultFilter).FilteredCount);
        }

        protected override void Load(Match3ResourceProvider resourceProvider, ILevel level, int score, int initialStage)
        {
            _skippedFully = false;

            var levelConfig = level.Config;
            var locationUid = levelConfig.LocationUid;
            var levelUid = levelConfig.Uid;

            _socialCardsPanel.gameObject.SetActive(false);
        }

        protected override void StartMainFlow()
        {
            void CardAnimAction(long _)
            {
                _coroutineExecutor.StartCoroutine(CardAnimActionRoutine());
                _cardAnimDelayTweener = null;
            }

            _cardAnimDelayTweener = Rx.Invoke(_delayBeforeCardAnimationStart, CardAnimAction);
        }

        private IEnumerator CardAnimActionRoutine()
        {
            yield return null;
            OnWaitComplete();
        }

        protected override void Animate()
        {
            _socialCardsPanel.StartAnimate(OnAnimsCompleted);
        }

        protected override void OnDefaultSkipButton()
        {
            _cardAnimDelayTweener?.Kill();
            _cardAnimDelayTweener = null;
            _socialCardsPanel.Skip();

            _skippedFully = true;

            base.OnDefaultSkipButton();
        }

        protected override void OnHide()
        {
            _socialCardsPanel.Clear();

            base.OnHide();
        }
    }
}
