using System;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Screens;
using Bebopbee.Core.Extensions.Unity;
using UnityEngine;

namespace BBB.UI.Level.Views
{
    [System.Obsolete("Replaced by LevelScreenCombinedViewPresenter", error: true)]
    public class LevelViewPresenterWrapper : ScreensViewPresenter, ILevelViewPresenter
    {
        private bool _isLevelLoaded;
        private Match3ResourceProvider _resourceProvider;
        private LevelController LevelController { get; set; }

        protected override void OnShow()
        {
            base.OnShow();
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(false);
            LevelController.OnShow();
//            Analytics.SetScreen(ScreenNames.Match3Screen);
        }

        protected override void OnHide()
        {
            LevelController.OnHide();
        }

        protected override void OnContextInitialization(UnityContext unityContext)
        {
            //unityContext.AddServiceToRegister<Match3ResourceProvider>(new Match3ResourceProvider());
        }

        protected override void OnContextInitialized(IContext context)
        {
            Transform levelScreen = null;
            var transforms = gameObject.transform.GetComponentsInChildren<Transform>();
            foreach (var childTransform in transforms)
            {
                if (childTransform.name != "LevelScreen") continue;
                
                levelScreen = childTransform;
                break;
            }

            if (levelScreen == null)
            {
                throw new InvalidOperationException("LevelScreen not found");
            }

            LevelController = levelScreen.GetOrAddComponent<LevelController>();
            base.OnContextInitialized(context);
            LevelController.Init(context);
            LevelController.gameObject.SetActive(false);
            _isLevelLoaded = true;
            _resourceProvider = context.Resolve<Match3ResourceProvider>();
            throw new InvalidOperationException("obsolete method call");
        }


        public bool IsLevelLoaded()
        {
            return _isLevelLoaded && LevelController.IsLevelInitialized();
        }

        public override void CacheResources(IResourceCache cache)
        {
            _resourceProvider.CacheResources(cache);
        }

        public override void DisposeResources()
        {
            _resourceProvider.DisposeResources();
        }
    }
}