using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Quests;
using BBB.Screens;
using BBB.Wallet;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using FBConfig;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Level.Views
{
    public class OutOfMovesBoosterOfferViewPresenter : ModalsViewPresenter, IOutOfMovesBoosterOfferViewPresenter
    {
        private const string HammerItem = "shovel";
        private const string Match3BoosterIapPopupTitle = "MATCH3_BOOSTERIAP_POPUP_TITLE"; // Tyr {0} pack!
        private const string PlusMovesLocKey = "OUT_OF_MOVES_BOOSTER_OFFER_PLUS_MOVES";
        [SerializeField] private LocalizedTextPro _title;
        [SerializeField] private LocalizedTextPro _description;
        [SerializeField] private LocalizedTextPro _additionalMovesText;
        [SerializeField] private LocalizedTextPro _additionalMovesBadgeText;
        [SerializeField] private GameObject _additionalMovesTextHolder;
        [SerializeField] private GameObject _stickerHolder;
        [SerializeField] private UICurrencyInsufficientComponent _priceComponent;
        [SerializeField] private MarketItemView _marketItemHammer;
        [SerializeField] private MarketItemView _marketItem;
        [SerializeField] private Button _buyButton;
        [SerializeField] private Image _topBarBg;
        [SerializeField] private Shadow _topBarShadow;
        [SerializeField] private List<StageColorPair> _stageColorPairs;

        private IConfig _config;
        private IWalletBalance WalletBalance => _walletManager.Balance;
        private IWalletManager _walletManager;
        private CurrencyIconsLoader _currencyIconsLoader;
        private ILocalizationManager _localization;

        private Action _onPurchase;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);

            _config = context.Resolve<IConfig>();

            _walletManager = context.Resolve<IWalletManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
            _localization = context.Resolve<ILocalizationManager>();
        }

        public void Setup(IAPStoreVirtualItemPackConfig itemPack, IAPStoreVirtualItemPackDescription pack, Stage paletteStage, Action onPurchase)
        {
            _onPurchase = onPurchase;

            var currentStagePalette = _stageColorPairs.Find(a => a.Stage == paletteStage);
            _topBarBg.color = currentStagePalette.MainBgColor;
            _topBarShadow.effectColor = currentStagePalette.SecondBgColor;
            var boosterConfig = _config.Get<BoosterConfig>();
            var boosterName = _localization.getLocalizedText(boosterConfig[itemPack.ItemUid].Name);
            _title.SetTextId(Match3BoosterIapPopupTitle, boosterName);
            _description.SetTextId(itemPack.Description);

            _marketItem.gameObject.SetActive(false);
            _marketItemHammer.gameObject.SetActive(false);

            var countText = RewardsAmountStringFormatDefinitions.LocalizeCurrencyCount(itemPack.ItemUid, pack.Amount, _localization, true, true);
            if (itemPack.ItemUid == HammerItem)
            {
                _marketItemHammer.SetupIcon(itemPack.Icon);
                _marketItemHammer.SetupCount(countText);
                _marketItemHammer.gameObject.SetActive(true);
            }
            else
            {
                _marketItem.SetupIcon(itemPack.Icon);
                _marketItem.SetupCount(countText);
                _marketItem.gameObject.SetActive(true);
            }

            string currencyUid = null;
            decimal priceAmount = 0;

            var price = pack.Price;
            
            if(!price.HasValue)
                return;
            
            for (var i = 0; i < price.Value.CurrenciesLength; i++)
            {
                var currency = price.Value.Currencies(i);
                
                if(!currency.HasValue)
                    continue;
                
                currencyUid = currency.Value.Key;
                priceAmount = currency.Value.Value;
                
            }

            _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(currencyUid).ContinueWith(sprite => _priceComponent.Setup(sprite, priceAmount.ToInt()));
            
            _priceComponent.SetupInsufficient(WalletBalance.GetBalance(), currencyUid);
            _buyButton.ReplaceOnClick(OnBuyOffer);
        }

        public void SetupAdditionalMoves(int movesToBeAdded)
        {
            SetupMovesText(movesToBeAdded);
        }

        private void SetupMovesText(int moves)
        {
            if (moves > 0)
            {
                _additionalMovesText.SetTextId(PlusMovesLocKey, moves);
                _additionalMovesBadgeText.SetRawText($"+{moves}");
                EnableAdditionalMovesObjects(true);
            }
            else
            {
                EnableAdditionalMovesObjects(false);
            }
        }

        private void EnableAdditionalMovesObjects(bool isEnabled)
        {
            _additionalMovesTextHolder.SetActive(isEnabled);
            _stickerHolder.SetActive(isEnabled);
        }

        private void OnBuyOffer()
        {
            AudioProxy.PlaySound(GenericSoundIds.ClaimPopupAppearing);
            _onPurchase?.Invoke();
        }

        private void OnBuyItem()
        {
            AudioProxy.PlaySound(GenericSoundIds.ClaimPopupAppearing);
            _onPurchase?.Invoke();
        }
    }
}