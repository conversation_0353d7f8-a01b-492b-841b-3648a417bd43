namespace BBB.UI.Level.Views
{
    public struct OutOfMovesContentData
    {
        public LevelOutcome CurrentOutcome;
        public bool IsRaceEventStreakBroken;
        public bool IsButlerStreakBroken;
        public bool SkipBanner;
        public bool SdbLost;
        public string LevelUid;
        public int PurchasePlus5Counter;
        public int GameEventScoreLost;
        public int CompetitionStreakLost;
        public int DiscoRushLostScore;
        public int RoyaleEventStreakScore;
        public int TeamEventLostScore;
        public int SweepstakesEventScoreLost;

        //Check if there is score/streak is to be lost
        public bool SweepstakesEventLoss => SweepstakesEventScoreLost > 0;
        public bool GameEventLoss => GameEventScoreLost > 0;
        public bool CompetitionEventStreakLoss => CompetitionStreakLost > 1;
        public bool DiscoRushEventLoss => DiscoRushLostScore > 0;
        public bool RoyaleEventStreakLoss => RoyaleEventStreakScore >= 0;
        public bool TeamCoopEventLoss => TeamEventLostScore >= 0;

    }
}