using System;
using BBB.Screens;
using BBB.UI.Core;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{

    public class UseBoostViewPresenter : ModalsViewPresenter, IUserBoostViewPresenter
    {
        [SerializeField] private Button _useButton;
        [SerializeField] private LocalizedTextPro _localizedBoostText;
        [SerializeField] private Image _boostImage;

        private Action _onUseClick;

        protected override void Awake()
        {
            base.Awake();

            _useButton.onClick.AddListener(UseClick);
        }

        private void UseClick()
        {
            var action = _onUseClick;
            _onUseClick = null;
            action?.Invoke();
        }

        public void Setup(string localizedBoostName, Sprite boostSprite, Action onUseClick)
        {
            _onUseClick = onUseClick;

            if (string.IsNullOrEmpty(localizedBoostName))
            {
                // This should never happen because this modal is displayed only when boost is available.
                TriggerOnCloseClicked();
            }

            _boostImage.sprite = boostSprite;
            _boostImage.preserveAspect = true;

            if (!string.IsNullOrEmpty(_localizedBoostText.TextId))
            {
                _localizedBoostText.FormatSetArgs(localizedBoostName);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _boostImage.sprite = null;
            _onUseClick = null;
        }
    }

    public interface IUserBoostViewPresenter : IViewPresenter
    {
        void Setup(string localizedBoostName, Sprite boostSprite, Action onUseClick);
    }
}