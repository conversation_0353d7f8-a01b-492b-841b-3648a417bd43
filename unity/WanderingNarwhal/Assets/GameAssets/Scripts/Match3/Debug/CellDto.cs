using System;
using BBB.CellTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Input;

namespace BBB.Match3
{
    [Serializable]
    public sealed class CellDto
    {
        public int X;
        public int Y;
        public TileDto TileDto;
        public int BackgroundCount;
        public int IvyCount;
        public bool IceBarStatus;
        public bool MetalBarStatus;
        public CellState State;
        public CellWallsDto Walls;
        public CellWallsDto InvisibleWalls;
        public DestructibleWalls DestructibleWalls;
        public int SizeX;
        public int SizeY;
        public int TntCount;
        public TileKinds TntKind;
        public TntTargetType TntTarget;

        public CellDto()
        {
        }

        [Obsolete("Replaced by protobuf binary dto", error: true)]
        public CellDto(int x, int y, TileDto tileDto, int backgroundCount, int ivyCount, bool iceBarStatus, bool metalBarStatus, CellState state, CellWalls walls, CellWalls invisibleWalls, DestructibleWalls destructibleWalls, int sizeX, int sizeY, int tntCount, TileKinds tntKind)
        {
            X = x;
            Y = y;
            TileDto = tileDto;
            BackgroundCount = backgroundCount;
            IvyCount = ivyCount;
            IceBarStatus = iceBarStatus;
            MetalBarStatus = metalBarStatus;
            State = state;
            Walls = walls?.ToDto();
            InvisibleWalls = invisibleWalls?.ToDto();
            DestructibleWalls = destructibleWalls;
            SizeX = sizeX;
            SizeY = sizeY;
            TntCount = tntCount;
            TntKind = tntKind;
        }

        public bool IntegrationEqualityCompare(CellDto other)
        {
            if (BackgroundCount != other.BackgroundCount) return false;
            if (IvyCount != other.IvyCount) return false;
            if (IceBarStatus != other.IceBarStatus) return false;
            if (MetalBarStatus != other.MetalBarStatus) return false;
            if (SizeX != other.SizeX) return false;
            if (SizeY != other.SizeY) return false;
            if (TntCount != other.TntCount) return false;
            if (TntKind != other.TntKind) return false;
            if (State != other.State) return false;
            if (X != other.X) return false;
            if (Y != other.Y) return false;
            bool isTileExist = TileDto != null && TileDto.hp > 0;
            bool isOtherTileExist = other.TileDto != null && other.TileDto.hp > 0;
            if ((isTileExist && !isOtherTileExist) || (isOtherTileExist && !isTileExist)) return false;
            if (isTileExist)
            {
                if (!TileDto.IntegrationEqualityCompare(other.TileDto))
                {
                    return false;
                }
            }

            return true;
        }
    }

    [Serializable]
    public class PlayerInputDto
    {
        public int x0;
        public int y0;
        public int x1;
        public int y1;
        public PlayerInputType inputType;
        public string itemType;
        public float percent;
        public bool orientation;

        public static PlayerInputDto ToDto(IPlayerInput input)
        {
            var dto = new PlayerInputDto();
            switch (input)
            {
                case PlayerInputSwap swap:
                    dto.x0 = swap.CoordsPair.FirstCoords.X;
                    dto.y0 = swap.CoordsPair.FirstCoords.Y;
                    dto.x1 = swap.CoordsPair.SecondCoords.X;
                    dto.y1 = swap.CoordsPair.SecondCoords.Y;
                    dto.inputType = PlayerInputType.Swap;
                    return dto;
                case PlayerInputDoubleTap dtap:
                    dto.x0 = dtap.TargetCoords.X;
                    dto.y0 = dtap.TargetCoords.Y;
                    dto.inputType = PlayerInputType.DTap;
                    return dto;
                case PlayerInputSingleTap tap:
                    dto.x0 = tap.TargetCoords.X;
                    dto.y0 = tap.TargetCoords.Y;
                    dto.inputType = PlayerInputType.Tap;
                    return dto;
                case PlayerInputItemShovel showel:
                    dto.x0 = showel.TargetCoord.X;
                    dto.y0 = showel.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "showel";
                    return dto;
                case PlayerInputItemBallon ballon:
                    dto.x0 = ballon.TargetCoord.X;
                    dto.y0 = ballon.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "ballon";
                    break;
                case PlayerInputItemCreateBomb createBomb:
                    dto.x0 = createBomb.TargetCoord.X;
                    dto.y0 = createBomb.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "createBomb";
                    break;
                case PlayerInputItemCreateColorBomb createColorBomb:
                    dto.x0 = createColorBomb.TargetCoord.X;
                    dto.y0 = createColorBomb.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "createColorBomb";
                    break;
                case PlayerInputItemCreateLineBreaker createLineBreaker:
                    dto.x0 = createLineBreaker.TargetCoord.X;
                    dto.y0 = createLineBreaker.TargetCoord.Y;
                    dto.orientation = createLineBreaker.Direction == SimplifiedDirections.Vertical;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "createLineBreaker";
                    break;
                case PlayerInputItemRain rain:
                    dto.percent = rain.Percent;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "rain";
                    break;
                case PlayerInputItemReshuffle reshuffle:
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "reshuffle";
                    break;
                case PlayerInputItemVerticalBooster verticalBooster:
                    dto.x0 = verticalBooster.TargetCoord.X;
                    dto.y0 = verticalBooster.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "verticalBooster";
                    return dto;
                case PlayerInputItemHorizontalBooster horizontalBooster:
                    dto.x0 = horizontalBooster.TargetCoord.X;
                    dto.y0 = horizontalBooster.TargetCoord.Y;
                    dto.inputType = PlayerInputType.Item;
                    dto.itemType = "horizontalBooster";
                    return dto;
            }

            return null;
        }

        public static IPlayerInput FromDto(PlayerInputDto dto)
        {
            switch (dto.inputType)
            {
                case PlayerInputType.Swap:
                    return new PlayerInputSwap(new EasyTouchInputController.CoordsPair(new Coords(dto.x0, dto.y0), new Coords(dto.x1, dto.y1)));
                case PlayerInputType.Tap:
                    return new PlayerInputSingleTap(new Coords(dto.x0, dto.y0));
                case PlayerInputType.DTap:
                    return new PlayerInputDoubleTap(new Coords(dto.x0, dto.y0));
                case PlayerInputType.Item:
                    switch (dto.itemType)
                    {
                        case "showel":
                            return new PlayerInputItemShovel(new Coords(dto.x0, dto.y0));
                        case "ballon":
                            return new PlayerInputItemBallon(new Coords(dto.x0, dto.y0));
                        case "createBomb":
                            return new PlayerInputItemCreateBomb(new Coords(dto.x0, dto.y0));
                        case "createColorBomb":
                            return new PlayerInputItemCreateColorBomb(new Coords(dto.x0, dto.y0));
                        case "createLineBreaker":
                            return new PlayerInputItemCreateLineBreaker(new Coords(dto.x0, dto.y0), dto.orientation ? SimplifiedDirections.Vertical : SimplifiedDirections.Horizontal);
                        case "rain":
                            return new PlayerInputItemRain(dto.percent);
                        case "reshuffle":
                            return new PlayerInputItemReshuffle();
                        case "verticalBooster":
                            return new PlayerInputItemVerticalBooster(new Coords(dto.x0, dto.y0));
                        case "horizontalBooster":
                            return new PlayerInputItemHorizontalBooster(new Coords(dto.x0, dto.y0));
                    }

                    break;
            }

            return null;
        }
    }
}
