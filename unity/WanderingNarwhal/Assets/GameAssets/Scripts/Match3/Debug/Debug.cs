using BBB.Core;
using BBB.Match3.Systems;

namespace BBB.Match3.Debug
{
    public static class M3Debug
    {
        public static bool Disabled { get; set; }
        private const int MaxSize = 16000;  // Truncation value for unity console

        public static void Log(string str)
        {
            if (Disabled)
                return;

            if (string.IsNullOrEmpty(str)) return;

            while (true)
            {
                if (str.Length <= MaxSize)
                {
                    BDebug.Log(LogCat.Match3, str);
                    break;
                }
                else
                {
                    var newString = str.Substring(0, MaxSize);
                    for (int i = MaxSize - 1; i >= 0; i--)
                    {
                        if (newString[i] == '\n')
                        {
                            BDebug.Log(LogCat.Match3, newString.Substring(0, i));
                            str = str.Remove(0, i);
                            break;
                        }
                    }
                }
            }
        }

        public static void LogWarning(string str)
        {
            if (Disabled)
                return;

            if (string.IsNullOrEmpty(str)) return;

            BDebug.LogWarning(LogCat.Match3, str);
        }

        public static void LogError(string str)
        {
            if (Disabled)
                return;

            if (string.IsNullOrEmpty(str)) return;

            var seedAndValues = RandomSystem.SeedToString();
            BDebug.LogError(LogCat.Match3,str + "\n" + seedAndValues + "\n");
        }
    }
}
