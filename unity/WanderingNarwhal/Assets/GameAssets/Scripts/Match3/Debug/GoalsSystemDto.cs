using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3
{
    [Serializable]
    public class GoalsSystemDto
    {
        public GoalStateDto goalsProgressLeft;
        public GoalStateDto assistGoalProgressLeft;
        public GoalStateDto originalGoals;
        public GoalStateDto originalAssistGoals;

        public GoalsSystemDto()
        {
        }

        public bool Equals(GoalsSystemDto other)
        {
            return IsGoalStateDtoMatch(assistGoalProgressLeft, other.assistGoalProgressLeft) 
                   && IsGoalStateDtoMatch(goalsProgressLeft, other.goalsProgressLeft)
                   && IsGoalStateDtoMatch(originalAssistGoals, other.originalAssistGoals) 
                   && IsGoalStateDtoMatch(originalGoals, other.originalGoals);

            // don't check score rules.
            bool IsGoalStateDtoMatch(GoalStateDto a, GoalStateDto b)
            {
                if (IsGoalStateNullOrEmpty(a) && !IsGoalStateNullOrEmpty(b) || !IsGoalStateNullOrEmpty(a) && IsGoalStateNullOrEmpty(b))
                    return false;
                
                return IsGoalStateNullOrEmpty(a) || a.Equals(b);
            }

            bool IsGoalStateNullOrEmpty(GoalStateDto g)
            {
                return g?.state == null || g.state.Count == 0;
            }
        }
    }

    [Serializable]
    public class GoalStateDto
    {
        public List<GoalRecord> state;

        public bool Equals(GoalStateDto other)
        {
            bool isStateExist = state != null && state.Count > 0;
            bool isOtherStateExist = other.state != null && other.state.Count > 0;

            if (isStateExist && !isOtherStateExist || isOtherStateExist && !isStateExist) return false;
            if (!isStateExist) return true;
            if (state.Count != other.state.Count) return false;

            foreach (var item in state)
            {
                bool isGoalMatchFound = false;
                foreach (var otherItem in other.state)
                {
                    if (item.goalType == otherItem.goalType)
                    {
                        if (item.value != otherItem.value && item.goalType != (long)GoalType.Score) return false;
                        isGoalMatchFound = true;
                        break;
                    }
                }

                if (!isGoalMatchFound) return false;
            }

            return true;
        }
    }

    [Serializable]
    public struct GoalRecord
    {
        public long goalType;
        public int value;

        public GoalRecord(GoalType goal, int num)
        {
            goalType = (long)goal;
            value = num;
        }
    }
}