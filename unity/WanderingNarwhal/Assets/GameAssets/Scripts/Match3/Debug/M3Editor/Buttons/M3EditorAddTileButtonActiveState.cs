using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public abstract class M3EditorRefreshableButton : BbbMonoBehaviour
    {
        public abstract void RefreshState(ILevel level);
    }

    [RequireComponent(typeof(Button))]
    public class M3EditorAddTileButtonActiveState : M3EditorRefreshableButton
    {
        [SerializeField] public TileKinds _color;
        private Button _button;

        public override void RefreshState(ILevel level)
        {
            if (level == null)
            {
                SetButtonState(true);
                return;
            }

            SetButtonState(level.UsedKinds.Contains(_color));
        }

        private void SetButtonState(bool active)
        {
            if (_button == null)
            {
                _button = GetComponent<Button>();
            }

            _button.interactable = active;
        }
    }
}