using System;
using System.Linq;
using BBB.Match3.Systems;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    /// <summary>
    /// Component that is used to configure max passes limit for the test levels tool.
    /// </summary>
    public class M3EditorTestLevelsLimitPanel : BbbMonoBehaviour
    {
        [SerializeField] private TMP_InputField _passesLimitInput;
        [SerializeField] private GameObject _limitPanel;
        [SerializeField] private Toggle _toggle;
        [SerializeField] private Toggle _onlyDropItemLevels;
        [SerializeField] private Dropdown _dropDownStatisticsType;

        public int LimitValue
        {
            get
            {
                int.TryParse(_passesLimitInput.text, out var result);
                return result;
            }
        }

        public PickLogic PickLogic
        {
            get
            {
                var pickLogic = Enum.GetValues(typeof(PickLogic))
                    .Cast<PickLogic>()
                    .ElementAt(_dropDownStatisticsType.value);

                return pickLogic;
            }
        }

        public bool AllLevelsToggleValue => _toggle.isOn;

        public bool OnlyDropItemLevelsToggleValue => _onlyDropItemLevels.isOn;

        private void Awake()
        {
            _dropDownStatisticsType.ClearOptions();
            _dropDownStatisticsType.AddOptions(Enum.GetNames(typeof(PickLogic)).ToList());
            _dropDownStatisticsType.value =  (int) PickLogic.UsefulMovesWin;
        }
    }
}