using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Serialization;

namespace BBB.M3Editor
{
    [RequireComponent(typeof(Button))]
    public class M3EditorUIActionButton : BbbMonoBehaviour
    {
        public string ToolAction;

        [SerializeField]
        [Head<PERSON>("Choose static parameter index or assign input field for dynamic prm input")]
        [Tooltip("Optional input field for additional numeric parameter.")]
        private InputField _numberInputField;

        [SerializeField]
        [Tooltip("Optional input field of custom type.")]
        private CustomInputField _customInputField;

        [SerializeField]
        [FormerlySerializedAs("Parameter")]
        [Tooltip("Used if input field is not assigned.")]
        private int _parameter;

        public int ParameterNumber
        {
            get
            {
                if (_numberInputField != null && int.TryParse(_numberInputField.text, out var overridePrm))
                {
                    return overridePrm;
                }

                return _customInputField != null ? _customInputField.GetCurrentValue() : _parameter;
            }
        }

        public Button GetButton()
        {
            return GetComponent<Button>();
        }

        [ContextMenu("Set default action")]
        private void DebugSetToolActionFromObjectName()
        {
            ToolAction = this.name;
        }
    }
}