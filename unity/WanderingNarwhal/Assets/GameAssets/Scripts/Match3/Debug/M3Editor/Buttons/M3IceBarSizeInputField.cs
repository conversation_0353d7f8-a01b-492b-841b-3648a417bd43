using BBB.Match3.Renderer;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    /// <summary>
    /// Special custom input field component for Ice Bar Tile m3 editor button.
    /// </summary>
    public class M3IceBarSizeInputField : CustomInputField
    {
        [SerializeField] private InputField _sizeXInput;
        [SerializeField] private InputField _sizeYInput;
        [SerializeField] private InputField _countInput;
        [SerializeField] private Toggle _isSingle;

        public override int GetCurrentValue()
        {
            int.TryParse(_sizeXInput.text, out var sizeX);
            int.TryParse(_sizeYInput.text, out var sizeY);
            int.TryParse(_countInput.text, out var count);
            var isSingle = _isSingle != null && _isSingle.isOn;

            sizeX = Mathf.Clamp(sizeX, 1, IceBarTileLayer.MAX_LENGTH);
            sizeY = Mathf.Clamp(sizeY, 1, IceBarTileLayer.MAX_LENGTH);
            count = Mathf.Clamp(count, 1, IceBarTileLayer.MAX_LENGTH);

            return SerializeParamsToInt(sizeX, sizeY, count, isSingle);
        }
    }
}