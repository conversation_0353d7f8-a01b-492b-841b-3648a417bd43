using BBB.Match3.Renderer;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3SodaColorInputField : CustomInputField
    {
        [SerializeField] private InputField colorInput;
        private readonly int[] _unusedColorList = {-3, -2, 0, 6};

        public override int GetCurrentValue()
        {
            var dataValue = colorInput.text.Split(',');
            var state = 0;
            var colorNum = 0;
            for (var i = 0; i < 4; i++)
            {
                if (i < dataValue.Length)
                {
                    int.TryParse(dataValue[i], out colorNum);
                    for (var index = 0; index < _unusedColorList.Length; index++)
                    {
                        var color = _unusedColorList[index];
                        if (color == colorNum)
                        {
                            colorNum = 3;
                        }
                    }
                }

                state = SodaTileLayer.SetColorNumInState(state, colorNum, i);
            }

            return state;
        }
    }
}
