using BBB.Match3.Renderer;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    /// <summary>
    /// Special custom input field component for Squid Tile m3 editor button.
    /// </summary>
    public class M3SquidSizeInputField : CustomInputField
    {
        [SerializeField] private InputField _sizeXInput;
        [SerializeField] private InputField _sizeYInput;
        [SerializeField] private InputField _countInput;
        [SerializeField] private Toggle _isSingle;

        public override int GetCurrentValue()
        {
            int.TryParse(_sizeXInput.text, out var sizeX);
            int.TryParse(_sizeYInput.text, out var sizeY);
            int.TryParse(_countInput.text, out var count);
            var isSingle = _isSingle != null && _isSingle.isOn;

            sizeX = Mathf.Clamp(sizeX, 1, SquidTileLayer.MAX_SUBITEMS_COUNT);
            sizeY = Mathf.Clamp(sizeY, 1, SquidTileLayer.MAX_SUBITEMS_COUNT);
            count = Mathf.Clamp(count, 1, SquidTileLayer.MAX_SUBITEMS_COUNT);

            return SerializeParamsToInt(sizeX, sizeY, count, isSingle);
        }
    }

    public abstract class CustomInputField : BbbMonoBehaviour
    {
        public abstract int GetCurrentValue();

        public static int SerializeParamsToInt(int sizeX, int sizeY, int count, bool isSingle = false)
        {
            // Editor contains only one input number parameter,
            // so we combine 3 numbers to pass it through
            // and to avoid refactoring.
            return sizeX + sizeY * 100 + count * 10000 + (isSingle ? 10000000 : 0);
        }

        public static void DeserializeParamsFromInt(int number, out int sizeX, out int sizeY, out int count, out bool isSingle)
        {
            count = number / 10000;
            sizeY = (number % 10000) / 100;
            sizeX = ((number % 10000) % 100);
            isSingle = (number / 10000000) == 1;
        }
    }
}