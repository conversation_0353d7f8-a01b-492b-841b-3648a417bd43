using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3TntSizeInputField : CustomInputField
    {
        [SerializeField] private InputField _sizeXInput;
        [SerializeField] private InputField _sizeYInput;
        [SerializeField] private InputField _countInput;

        public override int GetCurrentValue()
        {
            int.TryParse(_sizeXInput.text, out var sizeX);
            int.TryParse(_sizeYInput.text, out var sizeY);
            int.TryParse(_countInput.text, out var count);
            return SerializeParamsToInt(sizeX, sizeY, count, false);
        }
    }
}