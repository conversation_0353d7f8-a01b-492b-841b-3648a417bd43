using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public sealed class M3TukTukInputField : CustomInputField
    {
        [SerializeField] private InputField _sizeXInput;
        [SerializeField] private InputField _sizeYInput;
        [SerializeField] private InputField _countInput;
        [SerializeField] private Toggle _isMirror;

        public override int GetCurrentValue()
        {
            int.TryParse(_sizeXInput.text, out var sizeX);
            int.TryParse(_sizeYInput.text, out var sizeY);
            int.TryParse(_countInput.text, out var count);
            var isMirror = _isMirror != null && _isMirror.isOn;

            sizeX = Mathf.Clamp(sizeX, 1, 2);
            sizeY = Mathf.Clamp(sizeY, 1, 2);

            return Serialize(sizeX, sizeY, count, isMirror ? 1 : 0);
        }

        private static int Serialize(int sizeX, int sizeY, int count, int isMirror)
        {
            return (sizeX << 24) | (sizeY << 16) | (count << 8) | isMirror;
        }

        public static void DeSerialize(int input, out int sizeX, out int sizeY, out int count, out bool isMirror)
        {
            sizeX = (input >> 24) & 255;
            sizeY = (input >> 16) & 255;
            count = (input >> 8) & 255;
            isMirror = (input & 255) > 0;
        }
    }
}
