#if UNITY_EDITOR

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed partial class M3EditorCell : M3EditorBase
    {
        public M3EditorCell(M3Editor m3Editor) : base(m3Editor) {}

        public void ClearCell(Cell cell)
        {
            cell.HardRemoveTile(0);
            cell.ClearState();
            cell.BackgroundCount = 0;
            cell.TntCount = 0;
            cell.IvyCount = 0;
            cell.IceBarStatus = false;
            cell.MetalBarStatus = false;
            cell.DestructibleWalls = null;
            
            var grid = _m3Editor.GameController.Grid;
            var goals = _m3Editor.GameController.Level.Goals;
            var levelUid = _m3Editor.GameController.Level.Config.Uid;
            var optionalGoalTypes = _m3Editor.GameController.Level.OptionalGoalsEnabled;
            var globalSpawners = _m3Editor.GameController.DebugSpanwerSettingsManager.SpawnerSettings;
            goals.InitializeTargetGoalCountsIfNotManuallySpecified(grid, levelUid, optionalGoalTypes, globalSpawners);

            _m3Editor.GetSystem<M3EditorConfigs>().ApplyConfigs();
            _m3Editor.ApplyGrid(grid);
        }
    }
}
#endif
