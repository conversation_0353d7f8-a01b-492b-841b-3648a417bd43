#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorConfigsLitterSkin : M3EditorConfigsItemBase
    {
        private readonly Dictionary<string, Toggle> _litterSkinToggles;
        
        public M3EditorConfigsLitterSkin(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs) : base(m3Editor, m3EditorConfigs)
        {
            _litterSkinToggles = new Dictionary<string, Toggle>();
        }

        public override void SetUpUi()
        {
            foreach (var tf in M3Editor.GetComponentsInChildren<Transform>(true))
            {
                if (tf.name == "LitterSkinGroup")
                {
                    for (int i = 0; i < tf.childCount; i++)
                    {
                        var child = tf.GetChild(i);
                        var toggle = child.GetComponent<Toggle>();
                        toggle.onValueChanged.RemoveAllListeners();
                        toggle.onValueChanged.AddListener(OnToggleValueChanged);
                        _litterSkinToggles.Add(toggle.name, toggle);
                    }
                }
            }
        }

        private void OnToggleValueChanged(bool value)
        {
            if (!value)
                return;
            
            var firstToggleOn = _litterSkinToggles.Values.FirstOrDefault(t => t.isOn);

            if (firstToggleOn != null)
            {
                var name = firstToggleOn.name;
                M3Editor.GameController.Level.LitterSkin = name.TryParseToEnum<LitterSkin>();
            }
            else
                Debug.LogError("No litter skin toggle on is found");
            
            RefreshToggleColors();
            M3EditorConfigs.ApplyConfigs();
        }

        private void RefreshToggleColors()
        {
            foreach (var toggleKvp in _litterSkinToggles)
            {
                var toggle = toggleKvp.Value;
                var cb = toggle.colors;
                bool isOn = toggle.isOn;
                cb.normalColor = isOn ? Color.black : Color.white;
                cb.highlightedColor = isOn ? Color.black : Color.white;
                toggle.colors = cb;
            }
        }

        public override void LoadConfigsFrom(ILevel level)
        {
            foreach (var kvp in _litterSkinToggles)
            {
                var toggle = kvp.Value;
                toggle.isOn = level.LitterSkin.ToString().Equals(kvp.Key);
            }
            
            RefreshToggleColors();
        }
    }
}
#endif