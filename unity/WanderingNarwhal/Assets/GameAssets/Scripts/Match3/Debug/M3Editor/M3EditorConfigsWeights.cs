#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using BBB.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3EditorConfigsWeights : M3EditorConfigsWeightsBase
    {
        private GameObject _weightOriginal;

        private readonly List<TileAsset> _weightOptions = new List<TileAsset>
        {
            TileAsset.Simple,
            TileAsset.Litter
        };

        public M3EditorConfigsWeights(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs) : base(m3Editor, m3EditorConfigs) { }

        public override void SetUpUi()
        {
            base.SetUpUi();

            foreach (var child in M3Editor.GetComponentsInChildren<Button>(true))
            {
                if (child.name == "AddSpawnWeight")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(AddSpawnWeight);
                }
            }

            foreach (var child in M3Editor.GetComponentsInChildren<Transform>(true))
            {
                if (child.name == "SpawnWeight")
                {
                    _weightOriginal = child.gameObject;
                }
            }
        }

        public Dictionary<TileAsset, float> ApplyConfigsWeight()
        {
            var weights = new Dictionary<TileAsset, float>();
            foreach (var itemObject in ItemObjects)
            {
                var weightObject = (WeightObject)itemObject;
                float weightNumber;
                if (!float.TryParse(weightObject.InputField.text, out weightNumber)) continue;

                TileAsset asset = _weightOptions[weightObject.Dropdown.value];

                if (weights.ContainsKey(asset))
                    continue;
                
                weights.Add(asset, weightNumber);
            }

            if (weights.Count == 0)
            {
                weights.Add(TileAsset.Simple, 1f);
            }
            
            return weights;
        }

        public override void LoadConfigsFrom(ILevel level)
        { 
           
        }

        private void ConfigureWeightObject(WeightObject weightObject)
        {
            weightObject.GameObject.GetComponentInChildren<Button>().onClick.AddListener(() => RemoveItem(weightObject.GameObject));

            weightObject.InputField = weightObject.GameObject.GetComponentInChildren<InputField>();
            weightObject.Dropdown = weightObject.GameObject.GetComponentInChildren<Dropdown>();
            weightObject.Dropdown.AddOptions(_weightOptions.Select(w => w.ToString<TileAsset>()).ToList());
            weightObject.Dropdown.value = 0;

            weightObject.TextPercentage = weightObject.GameObject.GetComponentsInChildren<Text>().First(c => c.name == "WeightPercent");
        }

        private void AddSpawnWeight()
        {
            if(_weightOptions.Count == ItemObjects.Count) return;
            
            var newPrefab = M3Editor.InstantiateGo(_weightOriginal);
            newPrefab.transform.SetParent(_weightOriginal.transform.parent);
            newPrefab.SetActive(true);

            var weightObject = new WeightObject(newPrefab);
            ItemObjects.Add(weightObject);
            ConfigureWeightObject(weightObject);

            weightObject.InputField.text = "0";
            weightObject.InputField.onEndEdit.RemoveAllListeners();
            weightObject.InputField.onEndEdit.AddListener(delegate { PreApplyWeightConfigCheck(weightObject); });

            weightObject.Dropdown.onValueChanged.RemoveAllListeners();
            weightObject.Dropdown.onValueChanged.AddListener(delegate { PreApplyWeightConfigCheck(weightObject); });
        }

    }
    
}
#endif
