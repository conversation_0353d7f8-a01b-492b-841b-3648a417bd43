using System.Linq;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_EDITOR

namespace BBB.M3Editor
{
    public abstract class M3EditorConfigsWeightsBase: M3EditorConfigsItemBase
    {
        protected class WeightObject : ItemObject
        {
            public Dropdown Dropdown;
            public InputField InputField;
            public Text TextPercentage;
            public WeightObject(GameObject gameObject) : base(gameObject) { }
        }
        
        protected M3EditorConfigsWeightsBase(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs) : base(m3Editor, m3EditorConfigs)
        {}
        
        protected void PreApplyWeightConfigCheck(WeightObject weightObject)
        {
            float value;
            if (!float.TryParse(weightObject.InputField.text, out value))
            {
                weightObject.InputField.text = "0";
            }

            M3EditorConfigs.ApplyConfigs();

            RecalculateWeightPercentages();
        }

        protected void RecalculateWeightPercentages()
        {
            if (ItemObjects == null || ItemObjects.Count == 0) return;

            var totalWeight = ItemObjects.Cast<WeightObject>().Sum(w => float.Parse(w.InputField.text));

            foreach (var weightObject in ItemObjects.Cast<WeightObject>())
            {
                weightObject.TextPercentage.text = (100 * float.Parse(weightObject.InputField.text) / totalWeight).ToString("0.0") + "%";
            }
        }

        protected override void RemoveItem(GameObject go)
        {
            base.RemoveItem(go);

            RecalculateWeightPercentages();
        }
    }
}
#endif