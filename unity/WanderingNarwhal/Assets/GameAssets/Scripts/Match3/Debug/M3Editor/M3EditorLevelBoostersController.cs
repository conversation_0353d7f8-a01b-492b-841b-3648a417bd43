using System;
using System.Collections.Generic;
using BBB.Map;
using BBB.UI;
using Object = UnityEngine.Object;

#if UNITY_EDITOR
namespace BBB.M3Editor
{
    public sealed class M3EditorLevelBoostersController : M3EditorBase
    {
        private const string AddButtonLabel = "Add";
        private const string SetButtonLabel = "Set";
        private const string ButlerGiftStreakLabel = "Butler Gift Streak";
        private const string SuperDiscoBallLabel = "Super DiscoBall";

        private new readonly M3Editor _m3Editor;
        private EditorLevelControllerLauncher.ButlerGiftManagerEditorStub _butlerGiftManager;
        private IPlayerManager _playerManagerStub;
        private readonly List<AutoBoosterItems> _autoBoosterItems = new();

        public M3EditorLevelBoostersController(M3Editor m3Editor) : base(m3Editor)
        {
            _m3Editor = m3Editor;
        }

        public override void UpdateUi()
        {
            base.UpdateUi();
            _playerManagerStub = _m3Editor.Context.Resolve<IPlayerManager>();
            //Reinitialize butler manager data since we are rebuilding the player inventory when we switch levels
            _butlerGiftManager?.Init(_m3Editor.Context);

            foreach (var autoBooster in _autoBoosterItems)
            {
                if (autoBooster != null)
                {
                    autoBooster.ResetInputField();
                }
            }
        }

        public override void SetUpUi()
        {
            base.SetUpUi();
            _autoBoosterItems.Clear();
            var prefab = _m3Editor.GetComponentsInChildren<AutoBoosterItems>(true)[0];
            _butlerGiftManager = _m3Editor.Context.Resolve<IButlerGiftManager>() as EditorLevelControllerLauncher.ButlerGiftManagerEditorStub;
            _playerManagerStub = _m3Editor.Context.Resolve<IPlayerManager>();

            SpawnAutoBoosterInstance(prefab, ButlerGiftStreakLabel, SetButtonLabel,
                autoBoosterItem => { SetButlerGiftStreak((int)autoBoosterItem.Value); });

            var eligibleBoosters = _playerManagerStub.CurrentLevel.Config.TrueEligibleBoosts();
            if (eligibleBoosters is { Count: > 0 })
            {
                foreach (var boostUid in eligibleBoosters)
                {
                    SpawnAutoBoosterInstance(prefab, boostUid, AddButtonLabel,
                        autoBoosterItem => { SetCustomBoosterAmount(boostUid, (int)autoBoosterItem.Value); });
                }
            }

            SpawnAutoBoosterInstance(prefab, SuperDiscoBallLabel, SetButtonLabel,
                autoBoosterItem => { _m3Editor.LevelController.SetSuperDiscoBallValue(autoBoosterItem.Value); });
        }

        private void SpawnAutoBoosterInstance(AutoBoosterItems prefab, string boostUid, string buttonLabel,
            Action<AutoBoosterItems> onClick)
        {
            var boostItem = Object.Instantiate(prefab, prefab.transform.parent);
            boostItem.gameObject.SetActive(true);
            boostItem.Setup(boostUid, buttonLabel);
            boostItem.ActionButton.onClick.RemoveAllListeners();
            boostItem.ActionButton.onClick.AddListener(() => { onClick?.Invoke(boostItem); });
            _autoBoosterItems.Add(boostItem);
        }

        private void SetButlerGiftStreak(int value)
        {
            if (_butlerGiftManager.IsNull()) return;
            _butlerGiftManager.SetCurrentStreak(value);
        }

        private void SetCustomBoosterAmount(string boosterUid, int boosterCount)
        {
            if (_playerManagerStub.IsNull()) return;
            _playerManagerStub.PlayerInventory.AddBooster(boosterUid, boosterCount);
            _m3Editor.LevelController.BoostButtonsController.Refresh();
        }
    }
}
#endif