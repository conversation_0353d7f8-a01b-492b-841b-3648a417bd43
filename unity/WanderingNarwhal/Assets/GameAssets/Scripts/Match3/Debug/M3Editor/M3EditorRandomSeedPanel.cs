#if UNITY_EDITOR
using BBB.Match3.Systems;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorRandomSeedPanel : BbbMonoBehaviour
    {
        public TMP_InputField SeedInput;
        public Toggle SeedLockToggle;
        private uint _lastSeed;
        private bool _isRefreshing;

        private void Awake()
        {
#if BBB_DEBUG
            SeedInput.onEndEdit.AddListener((str) =>
            {
                if (_isRefreshing) 
                    return;
                
                long.TryParse(str, out var seed);
                var currentLock = RandomSystem.DebugLockSeed;
                RandomSystem.DebugLockSeed = false;
                _lastSeed = (uint)seed;
                RandomSystem.Reset(_lastSeed);
                RandomSystem.DebugLockSeed = currentLock;
            });

            SeedLockToggle.onValueChanged.AddListener((b) =>
            {
                if (_isRefreshing) return;
                RandomSystem.DebugLockSeed = b;
            });
#endif
        }

        private void Update()
        {
            _isRefreshing = true;
            if (_lastSeed != RandomSystem.Seed)
            {
                _lastSeed = RandomSystem.Seed;
                var seedStr = RandomSystem.Seed.ToString();
                if (SeedInput.text != seedStr)
                {
                    SeedInput.text = seedStr;
                }
            }
#if BBB_DEBUG
            if (SeedLockToggle.isOn != RandomSystem.DebugLockSeed)
            {
                SeedLockToggle.isOn = RandomSystem.DebugLockSeed;
            }
#endif
            _isRefreshing = false;
        }
    }
}
#endif