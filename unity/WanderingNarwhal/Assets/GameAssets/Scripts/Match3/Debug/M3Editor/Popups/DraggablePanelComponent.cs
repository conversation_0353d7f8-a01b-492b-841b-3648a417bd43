using UnityEngine;
using UnityEngine.EventSystems;

namespace BBB
{
    public class DraggablePanelComponent : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBeginDragHandler, IEndDragHandler, IDragHandler
    {
        [SerializeField] private RectTransform _draggedObject;

        private Vector2 _startDragPoint;
        private Vector3 _startPosition;

        public void OnBeginDrag(PointerEventData eventData)
        {
            _startPosition = _draggedObject.transform.position;
            _startDragPoint = eventData.position;
        }

        public void OnEndDrag(PointerEventData eventData)
        {
        }

        public void OnDrag(PointerEventData eventData)
        {
            _draggedObject.transform.position = _startPosition + (Vector3)(eventData.position - _startDragPoint);
        }
    }
}