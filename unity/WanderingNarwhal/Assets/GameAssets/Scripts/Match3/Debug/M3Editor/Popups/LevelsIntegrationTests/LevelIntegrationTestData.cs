#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB.Match3;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using UnityEngine;

namespace BBB.M3Editor.IntegrationTests
{
    public static class IntegrationTestTracker
    {
        public static bool IsIterationTestMode()
        {
            return M3EditorIntegrationTestsPopup.instance != null &&
                   M3EditorIntegrationTestsPopup.instance.isPopupDisplayed;
        }

        private static bool IsValidInput(IPlayerInput playerInput)
        {
            return playerInput.Type is not (PlayerInputType.Empty or PlayerInputType.None);
        }

        public static void RegisterPlayerInput(IPlayerInput playerInput, out bool isShouldSkipAssist)
        {
            isShouldSkipAssist = false;
            if (IsIterationTestMode() && IsValidInput(playerInput))
            {
                // Always skip assist if tests popup is active
                // to prevent intervention of assist system into test simulation.
                isShouldSkipAssist = true;
                M3EditorIntegrationTestsPopup.instance.OnPlayerInputRegistered(playerInput);
            }
        }

        public static void RegisterM3MoveEnded(Grid grid, GoalsSystem goalsSystem, IPlayerInput playerInput)
        {
            if (IsIterationTestMode() && IsValidInput(playerInput))
            {
                M3EditorIntegrationTestsPopup.instance.OnM3MoveEnded(grid, goalsSystem);
            }
        }
    }

    [Serializable]
    public class LevelSnapshot
    {
        public Grid.GridDto gridDto;
        public GoalsSystemDto goalsDto;
        public List<TileKinds> usedKinds;
        public int turnsLimit;
    }

    [Serializable]
    public class IntegrationTestRecord
    {
        public LevelSnapshot snapshotInitial;
        public LevelSnapshot snapshotAfterMoveExpected;

        /// <summary>
        /// Contains level shapshot in case if test failed.
        /// </summary>
        public LevelSnapshot snapshotAfterMoveActual;

        public PlayerInputDto playerInput;

        public string levelUid;
        public string name;

        public DateTime recordDateTime
        {
            get => new(recordDateTimeTicks);
            set => recordDateTimeTicks = value.Ticks;
        }

        public long recordDateTimeTicks;
        public uint randomSeed;
        public uint randomStateX;
        public uint randomStateY;
        public uint randomStateZ;
        public uint randomStateW;
        public TestStatusType currentStatus;

        public static string Serialize(IntegrationTestRecord record)
        {
            if (record?.snapshotInitial is null) return null;
            if (record.snapshotAfterMoveExpected is null) return null;
            if (record.playerInput is null) return null;

            var status = record.currentStatus;
            record.currentStatus = TestStatusType.none;
            var result = JsonUtility.ToJson(record);
            record.currentStatus = status;
            return result;
        }

        public static IntegrationTestRecord Deserialize(string str)
        {
            var data = JsonUtility.FromJson<IntegrationTestRecord>(str);
            if (data?.snapshotInitial is null) return null;
            if (data.snapshotAfterMoveExpected is null) return null;
            if (data.playerInput is null) return null;

            data.currentStatus = TestStatusType.none;
            return data;
        }
    }
}
#endif
