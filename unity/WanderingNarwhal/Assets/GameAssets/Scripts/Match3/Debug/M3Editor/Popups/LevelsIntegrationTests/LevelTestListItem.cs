#if UNITY_EDITOR
using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

namespace BBB.M3Editor.IntegrationTests
{
    public class LevelTestListItem : BbbMonoBehaviour, IPointerClickHandler
    {
        [SerializeField] private GameObject iconUnknown;
        [SerializeField] private GameObject iconPassed;
        [SerializeField] private GameObject iconFailed;
        [SerializeField] private GameObject iconException;
        [SerializeField] private GameObject _selectionObject;

        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private TMP_InputField _nameInput;
        [SerializeField] private TextMeshProUGUI _levelUidText;

        [SerializeField] private Color _defaultTextColor = new(0.93f, 0.93f, 0.93f, 1f);
        [SerializeField] private Color _unfinishedTextColor = new(0.962f, 0.935f, 0.095f, 1f);

        public bool IsInNameEditMode => _nameInput.gameObject.activeSelf;

        public event Action<LevelTestListItem> onSelectEvent;
        public event Action<IntegrationTestRecord, IntegrationTestRecord> onNameChangeEvent;
        private IntegrationTestRecord _record;

        private void Awake()
        {
            _nameInput.gameObject.SetActive(false);
            _nameInput.onEndEdit.AddListener(OnNameEdit);
        }

        public void Setup(IntegrationTestRecord record)
        {
            _record = record;
            _nameText.text = record.name;
            _nameText.color = record.snapshotAfterMoveExpected == null ? _unfinishedTextColor : _defaultTextColor;
            _levelUidText.text = record.levelUid;
        }

        private void OnNameEdit(string str)
        {
            EndNameEdit();
            if (_record.name != str)
            {
                _nameText.text = str;
                var oldRecord = _record.Clone();
                _record.name = str;
                onNameChangeEvent?.Invoke(_record, oldRecord);
            }
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            if (eventData.clickCount == 1)
            {
                onSelectEvent?.Invoke(this);
            }
            else if (eventData.clickCount == 2)
            {
                BeginNameEdit();
            }
        }

        public void BeginNameEdit()
        {
            _nameInput.text = _nameText.text;
            _nameInput.gameObject.SetActive(true);
        }

        private void EndNameEdit()
        {
            _nameInput.gameObject.SetActive(false);
        }

        public void SetSelectionView(bool active)
        {
            _selectionObject.SetActive(active);
            EndNameEdit();
        }

        public void SetStatus(TestStatusType status)
        {
            switch (status)
            {
                case TestStatusType.passed:
                    iconUnknown.SetActive(false);
                    iconFailed.SetActive(false);
                    iconPassed.SetActive(true);
                    iconException.SetActive(false);
                    break;
                case TestStatusType.failed:
                    iconUnknown.SetActive(false);
                    iconFailed.SetActive(true);
                    iconPassed.SetActive(false);
                    iconException.SetActive(false);
                    break;
                case TestStatusType.exception:
                    iconUnknown.SetActive(false);
                    iconFailed.SetActive(false);
                    iconPassed.SetActive(false);
                    iconException.SetActive(true);
                    break;
                default:
                    iconUnknown.SetActive(true);
                    iconFailed.SetActive(false);
                    iconPassed.SetActive(false);
                    iconException.SetActive(false);
                    break;
            }
        }
    }

    public enum TestStatusType
    {
        none,
        passed,
        failed,
        exception,
    }
}
#endif