#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using BBB.Core;
using BBB.DI;
using BBB.M3Editor.IntegrationTests;
using BBB.Match3;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI;
using BBB.UI.Level;
using BBB.UI.Level.Input;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.UI.Extensions;

namespace BBB.M3Editor
{
    public class M3EditorIntegrationTestsPopup : BbbMonoBehaviour, IContextInitializable
    {
        private enum OpenedSnapshotType
        {
            None,
            Before,
            After,
            Actual,
        }

        public class M3MoveEndedEvent : IResettableEvent
        {
            public Grid Grid;
            public GoalsSystem GoalSystem;
            public void Reset()
            {
                Grid = null;
                GoalSystem = null;
            }
        }

        private const string RELATIVE_FOLDER_PATH = "Levels/_tests/";
        private const string DOT_EXT = ".test.lzf";

        private string _currentFolderPath;

        [SerializeField] private Button _closeButton;
        [SerializeField] private Button _newRecordBtn;
        [SerializeField] private Button _rewriteRecordBtn;
        [SerializeField] private Button _exitRecordingBtn;
        [SerializeField] private Button _deleteRecordBtn;
        [SerializeField] private Button _openSnapshotBeforeMoveBtn;
        [SerializeField] private Button _openSnapshotExpectedBtn;
        [SerializeField] private Button _openSnapshotActualBtn;
        [SerializeField] private Button _runAllBtn;
        [SerializeField] private Button _runSelectedBtn;
        [SerializeField] private Button _makeMoveBtn;
        [SerializeField] private LevelTestListItem _itemTemplate;
        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private RectTransform _listContent;
        [SerializeField] private TextMeshProUGUI _overwriteText;
        [SerializeField] private Button _overwriteOpenedSnapshotInitial;
        [SerializeField] private Button _overwriteOpenedSnapshotExpected;

        [SerializeField] private TextMeshProUGUI _recordCounter;
        [SerializeField] private TMP_Dropdown _filterStatus;
        [SerializeField] private TMP_InputField _filterText;
        [SerializeField] private Button _applyFilterButton;
        [SerializeField] private Button _resetFilterButton;

        private readonly List<LevelTestListItem> _testListItems = new ();
        private List<IntegrationTestRecord> _allRecords = new ();
        private List<IntegrationTestRecord> _filteredRecords = new ();
        private readonly List<IntegrationTestRecord> _selectedRecords = new();
        private readonly List<IntegrationTestRecord> _pendingDeletion = new();
        private readonly List<IntegrationTestRecord> _pendingSave = new ();
        private IntegrationTestRecord _currentlyRecordingState;
        private int _lastClickedSelectedItemIndex;
        private IEventDispatcher _eventDispatcher;

        public M3Editor M3Editor { get; set; }

        public static M3EditorIntegrationTestsPopup instance;

        public bool isPopupDisplayed { get; private set; }

        public void InitializeByContext(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _eventDispatcher.RemoveListener<M3MoveEndedEvent>(OnM3MoveEndedEventHandler);
            _eventDispatcher.AddListener<M3MoveEndedEvent>(OnM3MoveEndedEventHandler);
        }

        private void Awake()
        {
            _currentFolderPath = Application.dataPath + "/" + RELATIVE_FOLDER_PATH;
            instance = this;
            _itemTemplate.gameObject.SetActive(false);
            _closeButton.ReplaceOnClick(OnCloseButton);
            _openSnapshotBeforeMoveBtn.onClick.AddListener(OpenSnapshotBeforeMove);
            _openSnapshotExpectedBtn.onClick.AddListener(OpenSnapshotExpected);
            _openSnapshotActualBtn.onClick.AddListener(OpenSnapshotActual);
            _newRecordBtn.onClick.AddListener(BeginNewRecord);
            _rewriteRecordBtn.onClick.AddListener(BeginRewriteRecord);
            _deleteRecordBtn.onClick.AddListener(DeleteRecordButtonHandler);
            _runAllBtn.onClick.AddListener(RunTestAll);
            _runSelectedBtn.onClick.AddListener(RunTestSelected);
            _makeMoveBtn.onClick.AddListener(MakeMove);
            _overwriteOpenedSnapshotInitial.onClick.AddListener(OverwriteOpenedSnapshotInitial);
            _overwriteOpenedSnapshotExpected.onClick.AddListener(OverwriteOpenedSnapshotExpected);

            _applyFilterButton.ReplaceOnClick(ApplyFilter);
            _resetFilterButton.ReplaceOnClick(ResetFilter);

            _newRecordBtn.interactable = false;
            _rewriteRecordBtn.interactable = false;
            
        }

        private void OverwriteOpenedSnapshotInitial()
        {
            if (_selectedRecords.Count != 1)
            {
                Debug.Log("Target record is not selected.");
                return;
            }

            var record = _selectedRecords[0];
            record.snapshotInitial = GenerateSnapshotFromCurrentState(M3Editor.Grid, M3Editor.GameController.GoalsSystem, M3Editor.GameController.Level.TurnsLimit);
            Debug.Log("Initial snapshot has been successfully overriden");
            _pendingSave.Add(record);
            SaveRecordsToDisk();
        }

        private void OverwriteOpenedSnapshotExpected()
        {
            if (_selectedRecords.Count != 1)
            {
                Debug.Log("Target record is not selected.");
                return;
            }

            var record = _selectedRecords[0];
            record.snapshotAfterMoveExpected = GenerateSnapshotFromCurrentState(M3Editor.Grid, M3Editor.GameController.GoalsSystem, M3Editor.GameController.Level.TurnsLimit);
            Debug.Log("Expected snapshot has been successfully overriden");
            _pendingSave.Add(record);
            SaveRecordsToDisk();
        }

        private void SetOpenedSnapshot(OpenedSnapshotType type)
        {
            _overwriteOpenedSnapshotInitial.interactable = type != OpenedSnapshotType.None;
            _overwriteOpenedSnapshotExpected.interactable = type != OpenedSnapshotType.None;
            _overwriteText.text = type == OpenedSnapshotType.None ? "" : "Overwrite level state in record";
        }

        public void OnPlayerInputRegistered(IPlayerInput input)
        {
            if (_currentlyRecordingState is null) return;

            _currentlyRecordingState.playerInput = PlayerInputDto.ToDto(input);
        }

        private void OnM3MoveEndedEventHandler(M3MoveEndedEvent ev)
        {
            var grid = ev.Grid;
            var goalsSystem = ev.GoalSystem;
            
            if (_currentlyRecordingState is null)
                return;

            if (_currentlyRecordingState.playerInput is null)
            {
                _currentlyRecordingState = null;
                RefreshButtons();
                return;
            }

            var prevTurnsLimit = _currentlyRecordingState.snapshotInitial.turnsLimit - 1;
            bool isSwapMove = _currentlyRecordingState.playerInput.inputType == PlayerInputType.Swap;
            var turnsLimit = isSwapMove ? prevTurnsLimit - 1 : prevTurnsLimit;
            _currentlyRecordingState.snapshotAfterMoveExpected = GenerateSnapshotFromCurrentState(grid, goalsSystem, turnsLimit);
            var index = _allRecords.IndexOf(_currentlyRecordingState);
            _currentlyRecordingState = null;

            if (index < 0 || index >= _testListItems.Count )
                return;

            var recordToSave = _allRecords[index];
            _pendingSave.Add(recordToSave);
            SaveRecordsToDisk();
            _testListItems[index].Setup(recordToSave);
        }

        public void OnM3MoveEnded(Grid grid, GoalsSystem goalsSystem)
        {
            var m3MoveEndedEvent = _eventDispatcher.GetMessage<M3MoveEndedEvent>();
            m3MoveEndedEvent.Grid = grid;
            m3MoveEndedEvent.GoalSystem = goalsSystem;
            _eventDispatcher.TriggerEventNextFrame(m3MoveEndedEvent);
        }

        private void RunTestSelected()
        {
            foreach (var record in _selectedRecords)
            {
                ExecuteTest(record);
            }

            RefreshButtons();
        }

        private void RunTestAll()
        {
            ResetFilter();
            foreach (var record in _allRecords)
            {
                ExecuteTest(record);
            }
            RefreshButtons();
        }

        private void MakeMove()
        {
            if (_selectedRecords.Count != 1) return;
            if (_selectedRecords[0].playerInput == null) return;


            var inputController = M3Editor.Context.Resolve<IInputController>();
            var input = _selectedRecords[0].playerInput;

            switch (input.inputType)
            {
                case PlayerInputType.None:
                    return;
                
                case PlayerInputType.Tap:
                case PlayerInputType.DTap:
                    inputController.AutoDoubleTap(new Coords(input.x0, input.y0));
                    break;
                
                case PlayerInputType.Swap:
                    inputController.AutoSwap(new Coords(input.x0, input.y0), new Coords(input.x1, input.y1));
                    break;

                default:
                    Debug.LogError($"{input.inputType} is not supported. coords0={new Coords(input.x0, input.y0)} coords1={new Coords(input.x1, input.y1)}");
                    return;
            }
            SetOpenedSnapshot(OpenedSnapshotType.After);
        }

        private void ExecuteTest(IntegrationTestRecord record)
        {
            try
            {
                var grid = new Grid(1, 1);
                grid.FromDto(record.snapshotInitial.gridDto);
                var goalSystem = M3Editor.GameController.GoalsSystem.Clone();
                goalSystem.FromDto(record.snapshotInitial.goalsDto);
                RandomSystem.DebugLockSeed = false;
                RandomSystem.Reset(record.randomSeed);
                RandomSystem.DebugLockSeed = true;

                var playerInput = PlayerInputDto.FromDto(record.playerInput);
                var simParams = new SimulationInputParams
                {
                    UsedKinds = record.snapshotInitial.usedKinds,
                    Settings = M3Editor.GameController.Settings,
                    SimulateLoopException = M3Editor.GameController.SimulateLoopException,
                    TurnsLimit = record.snapshotInitial.turnsLimit,
                    IsBatchMode = true,
                };

                var tileResources = M3Editor.Context.Resolve<TileResourceSelector>();
                var spawnSystem = M3Editor.Context.Resolve<M3SpawnSystem>().Clone();

                var gs = new GravitySystem(simParams, goalSystem, tileResources, null, null);
                var sim = gs.CreateSimulationSync(grid: grid, playerInput: playerInput, remainingMoves: record.snapshotInitial.turnsLimit, assistParams: null, spawnSystem: spawnSystem);

                var simulatedMoveSnapshot = new LevelSnapshot
                {
                    gridDto = grid.ToDto(),
                    goalsDto = goalSystem.ToDto(),
                    usedKinds = new List<TileKinds>(simParams.UsedKinds),
                    turnsLimit = simParams.TurnsLimit,
                };

                Debug.Log("Sim result: " + sim.Result);
                var isPassed = CompareSnapshots(simulatedMoveSnapshot, "actual", record.snapshotAfterMoveExpected, "expected");
                record.snapshotAfterMoveActual = simulatedMoveSnapshot;
                record.currentStatus = isPassed ? TestStatusType.passed : TestStatusType.failed;
                RefreshVisualStatusOfRecord(record);
            }
            catch (Exception ex)
            {
                record.currentStatus = TestStatusType.exception;
                RefreshVisualStatusOfRecord(record);
                Debug.LogException(ex);
            }
        }

        private void RefreshVisualStatusOfRecord(IntegrationTestRecord record)
        {
            var index = _allRecords.IndexOf(record);
            if (index >= 0 && index < _testListItems.Count)
            {
                _testListItems[index].SetStatus(record.currentStatus);
            }
        }

        private void BeginNewRecord()
        {
            if (!M3Editor.PlayState)
            {
                Debug.LogError("Can't start recording: it's possible in play mode only.");
                return;
            }
            
            var randomState = RandomSystem.GetRandomGenerationValues();
            var newRecord = new IntegrationTestRecord
            {
                snapshotInitial = GenerateSnapshotFromCurrentState(M3Editor.Grid, M3Editor.GameController.GoalsSystem, M3Editor.GameController.Level.TurnsLimit),
                currentStatus = TestStatusType.none,
                levelUid = M3Editor.GameController.Level.Config.Uid,
                name = M3Editor.GameController.Level.Config.Uid + "_test",
                recordDateTime = DateTime.UtcNow,
                randomSeed = RandomSystem.Seed,
                randomStateX = randomState.MX,
                randomStateY = randomState.MY,
                randomStateZ = randomState.MZ,
                randomStateW = randomState.MW
            };
            
            _currentlyRecordingState = newRecord;
            _allRecords.Add(newRecord);
            _pendingSave.Add(newRecord);
            _selectedRecords.Clear();
            _selectedRecords.Add(newRecord);
            RefreshList();
            _scrollRect.verticalNormalizedPosition = -1.0f;
            RefreshButtons();
        }

        private void BeginRewriteRecord()
        {
            if (!M3Editor.PlayState)
            {
                Debug.LogError("Can't start recording: it's possible in play mode only.");
                return;
            }

            if (_selectedRecords != null && _selectedRecords.Count == 1)
            {
                _currentlyRecordingState = _selectedRecords[0];
                _selectedRecords.Clear();
                _selectedRecords.Add(_currentlyRecordingState);
                RefreshButtons();
            }
            else
            {
                Debug.LogError("In order to rewrite you need to make sure only one record is selected");
            }
        }

        private void DeleteRecordButtonHandler()
        {
            if (_selectedRecords is { Count: 1 })
            {
                var record = _selectedRecords[0];
                Delete(record);
            }
            else
            {
                Debug.LogError("In order to delete you need to make sure only one record is selected");
            }
        }

        private LevelSnapshot GenerateSnapshotFromCurrentState(Grid grid, GoalsSystem goalsSystem, int turnsLimit)
        {
            return new LevelSnapshot
            {
                gridDto = grid.ToDto(),
                goalsDto = goalsSystem.ToDto(),
                usedKinds = new List<TileKinds>(M3Editor.GameController.Level.UsedKinds),
                turnsLimit = turnsLimit,
            };
        }

        private void OpenSnapshotActual()
        {
            if (_selectedRecords.Count != 1)
            {
                Debug.Log("Select single snapshot first.");
                return;
            }

            var record = _selectedRecords[0];
            if (record.snapshotAfterMoveActual is null)
            {
                Debug.Log("No data found in snapshot.");
                return;
            }

            SetOpenedSnapshot(OpenedSnapshotType.Actual);
            ApplySnapshotToCurrentLevel(record.snapshotAfterMoveActual, record).Forget();
        }

        private void OpenSnapshotExpected()
        {
            if (_selectedRecords.Count != 1)
            {
                Debug.Log("Select single snapshot first.");
                return;
            }

            var record = _selectedRecords[0];
            if (record.snapshotAfterMoveExpected is null)
            {
                Debug.Log("No data found in snapshot.");
                return;
            }

            SetOpenedSnapshot(OpenedSnapshotType.After);
            ApplySnapshotToCurrentLevel(record.snapshotAfterMoveExpected, record).Forget();
        }

        private void OpenSnapshotBeforeMove()
        {
            if (_selectedRecords.Count != 1)
            {
                Debug.Log("Select single snapshot first.");
                return;
            }

            var record = _selectedRecords[0];
            if (record.snapshotInitial is null)
            {
                Debug.Log("No data found in snapshot.");
                return;
            }

            SetOpenedSnapshot(OpenedSnapshotType.Before);
            ApplySnapshotToCurrentLevel(record.snapshotInitial, record).Forget();
        }

        private async UniTask ApplySnapshotToCurrentLevel(LevelSnapshot snapshot, IntegrationTestRecord record)
        {
            M3Editor.Grid.FromDto(snapshot.gridDto);
            var goalsSystem = M3Editor.GameController.GoalsSystem;
            goalsSystem.FromDto(snapshot.goalsDto);
            var level = M3Editor.Context.Resolve<LevelHolder>();
            var initialGoalsState = new GoalState();
            initialGoalsState.FromDto(snapshot.goalsDto.originalGoals);
            level.level.Goals = initialGoalsState;
            M3Editor.ApplyGrid(M3Editor.Grid);
            M3Editor.GameController.GridController.ForceUpdatePerimeter();
            var goalPanel = M3Editor.Context.Resolve<GoalResourcesPanel>();
            await goalPanel.FillGoalsAsync(level.level);
            M3Editor.GameController.GridController.SetupTransformSizes();

            RandomSystem.DebugLockSeed = false;
            RandomSystem.Reset(record.randomSeed);
            RandomSystem.DebugLockSeed = true;
        }

        private void OnCloseButton()
        {
            gameObject.SetActive(false);
        }

        protected override void OnEnable()
        {
            LoadRecordsFromDisk();
            RefreshList();
            isPopupDisplayed = true;
        }

        protected override void OnDisable()
        {
            SetOpenedSnapshot(OpenedSnapshotType.None);
            isPopupDisplayed = false;
            SaveRecordsToDisk();
            
            _currentlyRecordingState = null;
            _lastClickedSelectedItemIndex = 0;
            _testListItems.Clear();
            _allRecords.Clear();
            _selectedRecords.Clear();
            _pendingDeletion.Clear();
            _pendingSave.Clear();
            RefreshButtons();
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F2))
            {
                if (_selectedRecords.Count == 1)
                {
                    var index = _allRecords.IndexOf(_selectedRecords[0]);
                    if (index < 0 || index >= _testListItems.Count) return;
                    _testListItems[index].BeginNameEdit();
                }
            }
            else if (Input.GetKeyDown(KeyCode.Delete) || Input.GetKeyDown(KeyCode.Backspace))
            {
                if (_selectedRecords.Count == 1)
                {
                    Delete(_selectedRecords[0]);
                }
            }
        }

        private void Delete(IntegrationTestRecord record)
        {
            var index = _allRecords.IndexOf(record);
            if (index < 0) return;
            if (index >= _testListItems.Count) return;
            if (_testListItems[index].IsInNameEditMode) return;
            _pendingDeletion.Add(record);
            _allRecords.RemoveAt(index);
            _selectedRecords.Clear();
            RefreshList();
        }

        private void FilterRecords()
        {
            var tempFiltered = new List<IntegrationTestRecord>();
            var statusFilterIndex = _filterStatus.value;
            if (statusFilterIndex > 0)
            {
                // This cast is based on assumption that the order of combobox item is not changed and corresponds enum values
                var targetStatus = (TestStatusType)statusFilterIndex;

                foreach (var record in _allRecords)
                {
                    if (record.currentStatus == targetStatus)
                        tempFiltered.Add(record);
                }
            }
            else
            {
                tempFiltered.AddRange(_allRecords);
            }

            var filterText = _filterText.text;
            _filteredRecords = new List<IntegrationTestRecord>();

            if (filterText.IsNullOrEmpty())
            {
                _filteredRecords = tempFiltered;
            }
            else
            {
                foreach (var record in tempFiltered)
                {
                    if (record.name.IndexOf(filterText, StringComparison.OrdinalIgnoreCase) >= 0 || record.levelUid.IndexOf(filterText, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        _filteredRecords.Add(record);
                    }
                }
            }

        }
        
        private void RefreshList()
        {
            FilterRecords();
            _recordCounter.text = $"Records shown: {_filteredRecords.Count} of {_allRecords.Count}";
            Util.SetListItemInstancesExactCount(_testListItems, _filteredRecords.Count, _itemTemplate, _listContent);
            var index = 0;
            var offsetY = 0f;
            var sizeY = _itemTemplate.GetComponent<RectTransform>().rect.height;
            foreach (var item in _testListItems)
            {
                var record = _filteredRecords[index];
                index++;
                item.SetStatus(record.currentStatus);
                item.SetSelectionView(_selectedRecords.Contains(record));
                item.Setup(record);
                item.onSelectEvent -= OnItemSelected;
                item.onSelectEvent += OnItemSelected;
                item.onNameChangeEvent -= OnItemNameChanged;
                item.onNameChangeEvent += OnItemNameChanged;
                item.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -offsetY);
                offsetY += sizeY;
                item.gameObject.SetActive(true);
            }
            _listContent.sizeDelta = new Vector2(_listContent.sizeDelta.x, offsetY);
            RefreshButtons();
        }

        private void RefreshButtons()
        {
            var singleSelectedRecord = _selectedRecords.Count == 1 ? _selectedRecords[0] : null;

            _openSnapshotBeforeMoveBtn.interactable =  singleSelectedRecord?.snapshotInitial != null;
            _openSnapshotExpectedBtn.interactable = singleSelectedRecord?.snapshotAfterMoveExpected != null;
            _openSnapshotActualBtn.interactable = singleSelectedRecord != null 
                                                  && singleSelectedRecord.currentStatus != TestStatusType.none 
                                                  && singleSelectedRecord.snapshotAfterMoveActual != null;
            
            _overwriteOpenedSnapshotInitial.interactable = singleSelectedRecord != null;
            _overwriteOpenedSnapshotExpected.interactable = singleSelectedRecord != null;
            
            _newRecordBtn.gameObject.SetActive(_currentlyRecordingState == null);
            _rewriteRecordBtn.gameObject.SetActive(_currentlyRecordingState == null);
            _exitRecordingBtn.gameObject.SetActive(_currentlyRecordingState != null);
            
            _newRecordBtn.interactable = M3Editor.PlayState;
            _rewriteRecordBtn.interactable = singleSelectedRecord != null && M3Editor.PlayState;
            
            _makeMoveBtn.interactable = singleSelectedRecord != null && M3Editor.PlayState;
        }

        private void OnItemNameChanged(IntegrationTestRecord record, IntegrationTestRecord oldRecord)
        {
            if (!_allRecords.Contains(record)) return;
            _pendingSave.Add(record);
            _pendingDeletion.Add(oldRecord);
            SaveRecordsToDisk();
        }

        private void LoadRecordsFromDisk()
        {
            if (!Directory.Exists(_currentFolderPath)) return;

            List<IntegrationTestRecord> oldRecords = null;
            if (_allRecords.Count > 0)
            {
                oldRecords = _allRecords;
                _allRecords = new List<IntegrationTestRecord>();
            }

            var files = Directory.GetFiles(_currentFolderPath);
            foreach (var f in files)
            {
                try
                {
                    if (!f.EndsWith(DOT_EXT)) continue;

                    var bytes = File.ReadAllBytes(f);
                    var bytesDecompressed = CLZF2.Decompress(bytes);
                    var str = Encoding.UTF8.GetString(bytesDecompressed);
                    var record = IntegrationTestRecord.Deserialize(str);
                    if (record == null)
                    {
                        Debug.LogError("Failed to deserialize file: " + f);
                        continue;
                    }

                    _allRecords.Add(record);
                }
                catch (Exception ex)
                {
                    Debug.LogError("Exception while reading file: " + f);
                    Debug.LogException(ex);
                }
            }

            if (oldRecords != null)
            {
                // Keep status of already finished tests.
                foreach (var record in oldRecords)
                {
                    foreach (var r in _allRecords)
                    {
                        if (record.recordDateTimeTicks == r.recordDateTimeTicks)
                        {
                            r.currentStatus = record.currentStatus;
                            break;
                        }
                    }
                }
            }

            _allRecords.Sort(Comparison);

            int Comparison(IntegrationTestRecord a, IntegrationTestRecord b) => String.Compare(a.name, b.name, StringComparison.InvariantCulture);
        }

        private void SaveRecordsToDisk()
        {
            if (!Directory.Exists(_currentFolderPath)) return;

            foreach (var toDelete in _pendingDeletion)
            {
                var nameToDelete = toDelete.name + DOT_EXT;
                var path = _currentFolderPath + nameToDelete;
                if (File.Exists(path))
                {
                    File.Delete(path);
                }
            }

            _pendingDeletion.Clear();

            foreach (var toSave in _pendingSave)
            {
                var fileName = toSave.name + DOT_EXT;
                var str = IntegrationTestRecord.Serialize(toSave);
                if (string.IsNullOrEmpty(str))
                {
                    if (toSave.playerInput != null && toSave.snapshotAfterMoveActual != null)
                    {
                        Debug.LogError("Save failed for item: " + toSave.name + ", " + fileName);
                    }
                    continue;
                }

                var bytes = Encoding.UTF8.GetBytes(str);
                var serialized = CLZF2.Compress(bytes);
                var path = _currentFolderPath + fileName;
                File.WriteAllBytes(path, serialized);
            }

            _pendingSave.Clear();
        }

        private void OnItemSelected(LevelTestListItem item)
        {
            SetOpenedSnapshot(OpenedSnapshotType.None);
            var itemIndex = _testListItems.IndexOf(item);
            if (itemIndex < 0) return;

            if (!IsPressedControl() && !isPressedShift())
            {
                var isAlreadySelected = false;
                foreach (var selected in _selectedRecords)
                {
                    var recordIndex = _filteredRecords.IndexOf(selected);
                    if (recordIndex < 0 || recordIndex >= _testListItems.Count) continue;
                    if (recordIndex == itemIndex) isAlreadySelected = true;
                    _testListItems[recordIndex].SetSelectionView(false);
                }

                _selectedRecords.Clear();
                if (isAlreadySelected && itemIndex == _lastClickedSelectedItemIndex)
                {
                    RefreshButtons();
                    return;
                }
            }

            if (!IsPressedControl() && isPressedShift() && _lastClickedSelectedItemIndex >= 0)
            {
                var delta = _lastClickedSelectedItemIndex > itemIndex ? -1 : 1;
                for (int i = _lastClickedSelectedItemIndex; i != itemIndex; i += delta)
                {
                    var record = _filteredRecords[i];
                    var selectedIndex = _selectedRecords.IndexOf(record);
                    if (selectedIndex < 0)
                    {
                        _selectedRecords.Add(record);
                        _testListItems[i].SetSelectionView(true);
                    }
                }

                var recordLast = _filteredRecords[itemIndex];
                var selection = _selectedRecords.IndexOf(recordLast);
                if (selection < 0)
                {
                    _selectedRecords.Add(recordLast);
                    _testListItems[itemIndex].SetSelectionView(true);
                }

                _lastClickedSelectedItemIndex = itemIndex;
            }
            else
            {
                var record = _filteredRecords[itemIndex];
                var selectedIndex = _selectedRecords.IndexOf(record);
                if (selectedIndex >= 0)
                {
                    _selectedRecords.RemoveAt(selectedIndex);
                    item.SetSelectionView(false);
                    _lastClickedSelectedItemIndex = -1;
                }
                else
                {
                    _selectedRecords.Add(record);
                    item.SetSelectionView(true);
                    _lastClickedSelectedItemIndex = itemIndex;
                }
            }

            RefreshButtons();
        }

        private bool IsPressedControl()
        {
            return Input.GetKey(KeyCode.LeftControl);
        }

        private bool isPressedShift()
        {
            return Input.GetKey(KeyCode.LeftShift);
        }

        private static bool CompareSnapshots(LevelSnapshot a, string nameA, LevelSnapshot b, string nameB)
        {
            bool isMatch = true;
            if (a.turnsLimit != b.turnsLimit)
            {
                Debug.Log($"Turns limit doesn't match: {a.turnsLimit} in '{nameA}', {b.turnsLimit} in '{nameB}'");
            }

            if (a.gridDto.CellDtos.Count != b.gridDto.CellDtos.Count)
            {
                Debug.LogError($"Cells count doesn't match: {a.gridDto.CellDtos.Count} in '{nameA}', {b.gridDto.CellDtos.Count} in '{nameB}'");
                isMatch = false;
            }

            if (a.gridDto.tilesSpawnedCount != b.gridDto.tilesSpawnedCount)
            {
                Debug.LogError($"Tiles spawned count doesn't match: {a.gridDto.tilesSpawnedCount} in '{nameA}', {b.gridDto.tilesSpawnedCount} in '{nameB}'");
                isMatch = false;
            }

            int matchedCellsCount = 0;
            int unmatchedCellsCount = 0;
            foreach (var cellA in a.gridDto.CellDtos)
            {
                CellDto matchedCellB = null;

                foreach (var cellB in b.gridDto.CellDtos)
                {
                    if (cellA.X == cellB.X && cellA.Y == cellB.Y)
                    {
                        matchedCellB = cellB;
                        break;
                    }
                }

                if (matchedCellB is null)
                {
                    Debug.LogError($"Cell in grid '{nameA}' was not found in grid '{nameB}' at coords: {cellA.X},{cellA.Y}");
                    unmatchedCellsCount++;
                    isMatch = false;
                }
                else if (!cellA.IntegrationEqualityCompare(matchedCellB))
                {
                    var fullCellA = new Cell(new Coords(cellA.X, cellA.Y));
                    fullCellA.FromDto(cellA);
                    var fullCellB = new Cell(new Coords(matchedCellB.X, matchedCellB.Y));
                    fullCellB.FromDto(matchedCellB);
                    Debug.Log($"Cell at {cellA.X},{cellA.Y} in '{nameA}' doens't match cell in '{nameB}': " +
                              $"first cell {fullCellA} second cell {fullCellB}");
                    unmatchedCellsCount++;
                    isMatch = false;
                }
                else
                {
                    matchedCellsCount++;
                }
            }

            if (!a.goalsDto.Equals(b.goalsDto))
            {
                Debug.LogError($"Goals state in '{nameA}' doesn't match goals state in '{nameB}'");
                isMatch = false;
            }

            if (!isMatch)
            {
                Debug.Log($"Total unmatched cells={unmatchedCellsCount}, matched cells count={matchedCellsCount}");
            }

            return isMatch;
        }

        public void ChangePlayState(bool playState)
        {
            RefreshButtons();
        }
        
        private void ApplyFilter()
        {
            RefreshList();
        }

        private void ResetFilter()
        {
            _filterStatus.value = 0;
            _filterText.text = null;
            RefreshList();
        }
    }
}
#endif
