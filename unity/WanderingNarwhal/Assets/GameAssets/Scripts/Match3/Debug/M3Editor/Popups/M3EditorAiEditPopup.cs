#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Systems;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorAiEditPopup : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private Button _saveButton;
        [SerializeField] private GameObject _inputFieldTemplate;
        
        private readonly List<GameObject> _createdInputFields = new List<GameObject>(); 

        private ConsequenceState _aiState;
        
        public void InitializeByContext(IContext context)
        {
            _closeButton.ReplaceOnClick(Hide);
            _saveButton.ReplaceOnClick(Save);
        }

        public void Show()
        {
            _aiState = AiDataManagement.LoadAiState(AiTrainingSettings.AiStateAssetName);

            foreach (ConsequenceType type in Enum.GetValues(typeof(ConsequenceType)))
            {
                if (_aiState.HasKey(type))
                    continue;

                _aiState.Add(type, 0f);
            }

            RefreshView();
            gameObject.SetActive(true);
        }

        private void RefreshView()
        {
            _createdInputFields.Clear();
            
            foreach (var tuple in _aiState.TuplesEnum)
            {
                var holder = _inputFieldTemplate.transform.parent;
                var go = Instantiate(_inputFieldTemplate, holder);
                var inputField = go.GetComponentInChildren<InputField>();
                var nameTextField = go.GetComponentInChildren<TextMeshProUGUI>();
                var consequenceName = tuple.Item1.ToString();
                go.name = consequenceName;
                nameTextField.text = consequenceName;
                inputField.text = tuple.Item2.ToString("F2");
                _createdInputFields.Add(go);
                inputField.onValueChanged.AddListener(value =>
                {
                    if (float.TryParse(value, out var floatValue))
                    {
                        var consequenceType = (ConsequenceType) Enum.Parse(typeof(ConsequenceType), consequenceName);
                        _aiState.Replace(consequenceType, floatValue);
                    }
                });
                
                go.SetActive(true);
            }
        }

        private void Save()
        {
            AiDataManagement.SaveAiState(_aiState, AiTrainingSettings.AiStateAssetName);
            Hide();
        }

        private void Hide()
        {
            _aiState = null;
            
            foreach(var inputFieldGos in _createdInputFields)
                Destroy(inputFieldGos);
            
            _createdInputFields.Clear();
            gameObject.SetActive(false);
        }
    }
}
#endif