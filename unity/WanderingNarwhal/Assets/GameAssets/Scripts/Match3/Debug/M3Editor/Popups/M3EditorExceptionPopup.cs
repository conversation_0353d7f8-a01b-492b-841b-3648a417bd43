using BBB.DI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorExceptionPopup : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private Button _closeBgButton;
        [SerializeField] private Button _closeButton;
        [SerializeField] private TextMeshProUGUI _exceptionText;
        
        public void InitializeByContext(IContext context)
        {
            _closeBgButton.ReplaceOnClick(Hide);
            _closeButton.ReplaceOnClick(Hide);
        }

        public void Show(string text)
        {
            _exceptionText.text = text;
            gameObject.SetActive(true);
        }

        private void Hide()
        {
            gameObject.SetActive(false);
            _exceptionText.text = null;
        }
    }
}