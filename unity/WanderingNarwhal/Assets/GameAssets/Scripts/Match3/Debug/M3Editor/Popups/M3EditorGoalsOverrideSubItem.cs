#if UNITY_EDITOR
using System;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorGoalsOverrideSubItem : BbbMonoBehaviour
    {
        [SerializeField] private Toggle _toggle;
        [SerializeField] private TextMeshProUGUI _goalName;
        [SerializeField] private TMP_InputField _inputField;
        [SerializeField] private Button _increaseButton;
        [SerializeField] private Button _decreaseButton;

        private Action _goalsUpdate;
        private bool _isGridBased;
        
        [NonSerialized]
        public int CurrentValue;
        [NonSerialized]
        public bool ToggleState;
        [NonSerialized]
        public bool CachedToggleValue;
        
        public void Setup(GoalType goalType, int currentCount, bool toggleState, bool isGridBased, Action goalsUpdate)
        {
            _goalName.text = goalType.ToString();
            CurrentValue = currentCount;
            ToggleState = toggleState;
            _toggle.isOn = ToggleState;
            _inputField.enabled = ToggleState;
            _inputField.text = CurrentValue.ToString();
            _isGridBased = isGridBased;

            _goalsUpdate = goalsUpdate;
            
            _increaseButton.ReplaceOnClick(Increment);
            _decreaseButton.ReplaceOnClick(Decrement);
            
            _toggle.onValueChanged.RemoveAllListeners();
            _toggle.onValueChanged.AddListener(ToggleValueChanged);
            
            _inputField.onValueChanged.RemoveAllListeners(); 
            _inputField.onValueChanged.AddListener(OnInputValueChanged);
        }

        private void OnInputValueChanged(string val)
        {
            int.TryParse(val, out var moves);

            if (moves < 0) return;
            CurrentValue = moves;
            UpdateGoals();
        }
       
        private void ToggleValueChanged(bool state)
        {
            if (state == ToggleState) return;
            ToggleState = state;
            _inputField.enabled = ToggleState;
            //Making Current Value as 0, will auto-calculate the number of items on grid and update the goal count
            //Mechanics such as Toad, MagicHat, Bee have non-grid custom goal count
            CurrentValue = _isGridBased ? 0 : 1;
            UpdateGoals();
        }
        
        private void Increment()
        {
            if (!ToggleState) return;
            CurrentValue++;
            UpdateGoals();
        }

        private void Decrement()
        {
            if (!ToggleState) return;
            if (CurrentValue <= 0) return;
            CurrentValue--;
            UpdateGoals();
        }

        private void UpdateGoals()
        {
            _inputField.text = CurrentValue.ToString();
            CachedToggleValue = ToggleState;
            _goalsUpdate?.Invoke();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _goalsUpdate = null;
        }
    }
}
#endif

