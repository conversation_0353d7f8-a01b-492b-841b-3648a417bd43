#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Input;
using BebopBee.Core;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorHistogramPopup : BbbMonoBehaviour, IContextInitializable
    {
        private const string Running = "RUNNING";
        private const string Passed = "PASSED";
        private const string Failed = "FAILED";

        [SerializeField] private Button _closeButton;
        [SerializeField] private Button _closeBgButton;
        [SerializeField] private TextMeshProUGUI _lossReasonsText;
        [SerializeField] private TextMeshProUGUI _typeText;
        [SerializeField] private TextMeshProUGUI _minText;
        [SerializeField] private TextMeshProUGUI _avgText;
        [SerializeField] private TextMeshProUGUI _medText;
        [SerializeField] private TextMeshProUGUI _maxText;
        [SerializeField] private TextMeshProUGUI _sampleSizeText;
        [SerializeField] private TextMeshProUGUI _allGoalsReachedCountText;
        [SerializeField] private GameObject _goalReachedTemplate;
        [SerializeField] private Transform _goalReachedHolder;
        [SerializeField] private List<RectTransform> _columnTransforms;
        [SerializeField] private List<TextMeshProUGUI> _middleNumberTexts;
        [SerializeField] private List<TextMeshProUGUI> _overColumnNumberTexts;
        [SerializeField] private TextMeshProUGUI _standardDeviationText;
        [SerializeField] private GameObject _tabButtonTemplate;
        [SerializeField] private TMP_Dropdown _dropDown;
        [SerializeField] private Button _replayTestButton;
        [SerializeField] private TMP_InputField _replayText;
        [SerializeField] private TextMeshProUGUI _replayResult;

        private readonly List<float> _values = new();
        private Match3Statistics _shownStats;
        private string _resultType;

        private readonly List<Toggle> _tabToggles = new();
        private readonly ReplayLevelSystem _replaySystem = new();

        public M3Editor M3Editor { get; set; }

        public void InitializeByContext(IContext context)
        {
            _closeBgButton.ReplaceOnClick(Hide);
            _closeButton.ReplaceOnClick(Hide);
            _values.Clear();
            foreach (var unused in _columnTransforms)
                _values.Add(0);
        }

        private void Hide()
        {
            gameObject.SetActive(false);

            Clear();
            RemoveTabs();

            _goalReachedHolder.RemoveAllChildren();
        }

        private void Clear()
        {
            for (var i = 0; i < _columnTransforms.Count; i++)
            {
                SetColumnHeightNormalized(i, 0f);
            }

            for (var i = 0; i < _values.Count; i++)
            {
                _values[i] = 0;
            }

            foreach (var text in _middleNumberTexts)
            {
                text.text = string.Empty;
            }

            foreach (var text in _overColumnNumberTexts)
            {
                text.text = string.Empty;
            }

            _dropDown.ClearOptions();
            _dropDown.gameObject.SetActive(false);

            _lossReasonsText.text = string.Empty;
            _typeText.text = string.Empty;
            _minText.text = string.Empty;
            _avgText.text = string.Empty;
            _medText.text = string.Empty;
            _maxText.text = string.Empty;
            _sampleSizeText.text = string.Empty;
            _allGoalsReachedCountText.text = string.Empty;
            _standardDeviationText.text = string.Empty;

            _replayTestButton.gameObject.SetActive(false);
            _replayText.gameObject.SetActive(false);
            _replayText.text = string.Empty;
            _replayResult.text = string.Empty;
        }

        private void SetColumnHeightNormalized(int index, float heightRatio)
        {
            var item = _columnTransforms[index];
            var parent = item.parent.GetComponent<RectTransform>();
            var parentHeight = parent.rect.height;
            var itemHeight = (item.anchorMax.y - item.anchorMin.y) * parentHeight;
            var offsetMaxY = -itemHeight * (1f - heightRatio);
            item.offsetMin = new Vector2(0, 0);
            item.offsetMax = new Vector2(0, offsetMaxY);
        }

        public void Show(Match3Statistics stats)
        {
            _shownStats = stats;
            SetupTabs();
            if (_tabToggles.Count > 0)
            {
                _tabToggles.First().isOn = true;
            }
        }

        private void SetupTabs()
        {
            var holder = _tabButtonTemplate.transform.parent.GetComponent<RectTransform>();

            _goalReachedHolder.RemoveAllChildren();

            var offsetX = 0f;

            var statsKeys = _shownStats.ResultTypes
                .Concat(_shownStats.AssistSystemResultTypes)
                .Concat(_shownStats.ReplaySystemResultTypes);

            foreach (var resultType in statsKeys)
            {
                var tabGo = Instantiate(_tabButtonTemplate, holder, true);
                var rectTransform = tabGo.GetComponent<RectTransform>();
                rectTransform.anchoredPosition = new Vector2(offsetX, 0);
                offsetX += rectTransform.sizeDelta.x + 1;
                var toggle = tabGo.GetComponent<Toggle>();
                toggle.onValueChanged.AddListener(OnToggleValueChanged);
                var text = tabGo.GetComponentInChildren<TextMeshProUGUI>();
                text.text = resultType;
                tabGo.name = resultType;
                _tabToggles.Add(toggle);
                tabGo.SetActive(true);
            }

            holder.sizeDelta = new Vector2(offsetX, holder.sizeDelta.y);

            foreach (var resultType in _shownStats.GoalResultTypes)
            {
                var goalReachedCount = _shownStats.CountEqualOrGreaterThenCeiling(resultType);
                var result = _shownStats.GetResults(resultType);
                var goalReachedInstance = Instantiate(_goalReachedTemplate, _goalReachedHolder, false);
                var grTitle = goalReachedInstance.transform.Find("GoalReachedTitle").GetComponent<TextMeshProUGUI>();
                var grNumber = goalReachedInstance.transform.Find("GoalReachedNumber").GetComponent<TextMeshProUGUI>();
                grTitle.text = string.Format(grTitle.text, resultType);
                grNumber.text = goalReachedCount + "/" + result.Count;
                goalReachedInstance.SetActive(true);
            }
        }

        private void RefreshTabColors()
        {
            foreach (var tabToggle in _tabToggles)
            {
                var image = tabToggle.GetComponent<Image>();
                image.color = tabToggle.isOn ? Color.green : Color.white;
            }
        }

        private void RemoveTabs()
        {
            foreach (var tabToggle in _tabToggles)
            {
                tabToggle.onValueChanged.RemoveAllListeners();
                Destroy(tabToggle.gameObject);
            }

            _tabToggles.Clear();
        }

        private void OnToggleValueChanged(bool value)
        {
            var firstToggleOn = _tabToggles.FirstOrDefault(t => t.isOn);

            if (firstToggleOn != null)
            {
                RefreshTabColors();
                _resultType = firstToggleOn.gameObject.name;
                switch (_resultType)
                {
                    case var _ when _shownStats.AssistSystemResultTypes.Contains(_resultType):
                        ShowAssistTab();
                        break;
                    case Match3Statistics.Replay:
                        ShowReplayTab();
                        break;
                    default:
                        ShowTab();
                        break;
                }
            }
        }

        private void ShowTab()
        {
            Clear();

            var result = _shownStats.GetResults(_resultType);
            _typeText.text = _shownStats.PickLogic.ToString();
            _lossReasonsText.text = _shownStats.GetLossReasonString();
            var minXValue = result.Min();
            var maxXValue = result.Max();

            _minText.text = minXValue.ToString();
            _maxText.text = maxXValue.ToString();
            _avgText.text = result.Average().ToString("F2");
            _medText.text = _shownStats.GetThresholdForRatio(_resultType, 0.5f).ToString("F2");
            _sampleSizeText.text = result.Count.ToString();

            _allGoalsReachedCountText.text = _shownStats.CountAllEqualOrGreaterThenCeiling()
                                             + "/" + _shownStats.GetResults(_shownStats.GoalResultTypes.First()).Count;

            var histStep = (maxXValue - minXValue) / (float) _columnTransforms.Count;

            var xValuesList = new List<float>();

            if (maxXValue - minXValue > _columnTransforms.Count)
            {
                for (var i = 0; i < _columnTransforms.Count; i++)
                {
                    var firstBorderValue = minXValue + histStep * i;
                    var secondBorderValue = firstBorderValue + histStep;


                    if (i % 2 == 0 && i < _columnTransforms.Count - 2)
                        xValuesList.Add(firstBorderValue + 2 * histStep);

                    _values[i] = result.Count(number => number >= firstBorderValue && number < secondBorderValue);
                    _overColumnNumberTexts[i].text = _values[i].ToString(CultureInfo.InvariantCulture);
                }
            }
            else
            {
                for (var i = 0; i < _columnTransforms.Count; i++)
                    _values[i] = 0;

                for (var i = minXValue; i < maxXValue; i++)
                {
                    var columnIndex = i - minXValue;
                    _values[columnIndex] = result.Count(number => number >= i && number < i + 1);
                    _overColumnNumberTexts[columnIndex].text = _values[columnIndex].ToString(CultureInfo.InvariantCulture);
                }

                for (var i = minXValue + 1; i < maxXValue; i++)
                {
                    if (i % 2 == 0)
                        xValuesList.Add(i);
                }
            }

            var maxYValue = _values.Max();

            for (var i = 0; i < _values.Count; i++)
            {
                var normalizedValue = maxYValue > 0 ? _values[i] / maxYValue : 0;
                SetColumnHeightNormalized(i, normalizedValue);
            }

            for (var i = 0; i < xValuesList.Count; i++)
            {
                _middleNumberTexts[i].text = Mathf.RoundToInt(xValuesList[i]).ToString();
            }

            _standardDeviationText.text = _shownStats.GetStandardDeviation(_resultType).ToString("F0");
            gameObject.SetActive(true);
        }

        private void ShowAssistTab()
        {
            Clear();
            var assistSystemResults = _shownStats.GetAssistSystemResults(_resultType);

            _dropDown.gameObject.SetActive(true);
            _dropDown.ClearOptions();
            _dropDown.onValueChanged.RemoveAllListeners();

            for (var i = 0; i < assistSystemResults.Count; i++)
            {
                _dropDown.AddOptions(new List<TMP_Dropdown.OptionData> {new($"Turn_{i + 1}")});
            }

            _dropDown.onValueChanged.AddListener(OnDropDownValueChanged);
            OnDropDownValueChanged(0);
        }

        private void OnDropDownValueChanged(int turn)
        {
            if (!_shownStats.GetAssistSystemResults(_resultType).TryGetValue(turn, out var result))
                return;

            var numberOfValues = result.Count;
            var numberOfColumns = _columnTransforms.Count;

            if (numberOfValues > numberOfColumns)
            {
                var averagedValues = new float[numberOfColumns];
                var columnCounts = new int[numberOfColumns];

                for (var i = 0; i < numberOfValues; i++)
                {
                    var columnIndex = Mathf.Min(i * numberOfColumns / numberOfValues, numberOfColumns - 1);
                    averagedValues[columnIndex] += result[i];
                    columnCounts[columnIndex]++;
                }

                for (var i = 0; i < numberOfColumns; i++)
                {
                    if (columnCounts[i] > 0)
                    {
                        averagedValues[i] /= columnCounts[i];
                    }

                    UpdateColumnDisplay(i, averagedValues[i]);
                }
            }
            else
            {
                for (var i = 0; i < numberOfColumns; i++)
                {
                    var value = i < numberOfValues ? result[i] : 0f;
                    UpdateColumnDisplay(i, value);
                }
            }

            gameObject.SetActive(true);
            return;

            void UpdateColumnDisplay(int index, float value)
            {
                _overColumnNumberTexts[index].text = value.ToString("F2");
                var columnHeight = Mathf.Max(value, 0f);
                SetColumnHeightNormalized(index, columnHeight);
            }
        }

        private void ShowReplayTab()
        {
            Clear();
            var replaySystemResults = _shownStats.GetReplaySystemResults(_resultType);

            _dropDown.gameObject.SetActive(true);
            _dropDown.ClearOptions();
            _dropDown.onValueChanged.RemoveAllListeners();

            for (var i = 0; i < replaySystemResults.Count; i++)
            {
                _dropDown.AddOptions(new List<TMP_Dropdown.OptionData> {new($"Turn_{i + 1}")});
            }

            _dropDown.onValueChanged.AddListener(ReplaySystemOnDropDownValueChanged);
            ReplaySystemOnDropDownValueChanged(0);
        }

        private void ReplaySystemOnDropDownValueChanged(int turn)
        {
            var result = _shownStats.GetReplaySystemResults(_resultType);

            if (result.Count > turn && result[turn] != string.Empty)
            {
                _replayText.gameObject.SetActive(true);
                _replayText.text = result[turn];
                _replayTestButton.gameObject.SetActive(true);
                _replayResult.text = string.Empty;

                _replayTestButton.AddOnClick(() =>
                {
                    _replayResult.text = Running;
                    Rx.InvokeNextFrame(() =>
                        {
                            _replaySystem.SetFromBinaryData(result[turn]);
                            PlaySimulationOnlyReplay();
                            _replayResult.text = result[turn] == M3Editor.LevelAnalyticsReporter.GetReplayData() ? Passed : Failed;
                        }
                    );
                });

                gameObject.SetActive(true);
            }
        }

        private void PlaySimulationOnlyReplay()
        {
            RandomSystem.DebugLockSeed = false;
            RandomSystem.Reset(_replaySystem.RandomSeed);
            RandomSystem.DebugLockSeed = true;
            M3Editor.LevelController.PlayerManager.SetOnAimToWinSkillModifier(_replaySystem.GetOnAimToWinSkillModifier);
            M3Editor.LevelController.PlayerManager.SetOnAimToLoseSkillModifier(_replaySystem.GetOnAimToLoseSkillModifier);
            AssistParams.AimAtWinningLevel = _replaySystem.AimAtWinningLevel;
            M3Editor.LevelController.SetSuperDiscoBallValue(_replaySystem.InitialSuperDiscoBall);

            M3Editor.GameController.Refresh();

            foreach (var currentRecord in _replaySystem.Records)
            {
                RandomSystem.SetRandomGenerationValues(currentRecord.RandomState);

                switch (currentRecord.PlayerAction)
                {
                    case ReplayLevelSystem.PlayerAction.SingleTap:
                        M3Editor.GameController.PlayerInputSync(new PlayerInputSingleTap(currentRecord.Positions[0]));
                        break;

                    case ReplayLevelSystem.PlayerAction.DoubleTap:
                        M3Editor.GameController.PlayerInputSync(new PlayerInputDoubleTap(currentRecord.Positions[0]));
                        break;

                    case ReplayLevelSystem.PlayerAction.Swap:
                        var newPair = new EasyTouchInputController.CoordsPair(currentRecord.Positions[0], currentRecord.Positions[1]);
                        M3Editor.GameController.PlayerInputSync(new PlayerInputSwap(newPair));
                        break;

                    case ReplayLevelSystem.PlayerAction.Booster:
                        M3Editor.GameController.PlayerInputSync(new PlayerInputItemShovel(currentRecord.Positions[0]));
                        break;

                    case ReplayLevelSystem.PlayerAction.SuperBoost:
                        M3Editor.LevelController.SuperBoostPanelController.TriggerSupperBoostOnReplay(currentRecord.Positions[0]);
                        break;

                    case ReplayLevelSystem.PlayerAction.AddedMoves:
                        M3Editor.DebugView.AddMoves(true, currentRecord.AddedMoves);
                        break;
                    
                    case ReplayLevelSystem.PlayerAction.SuperDiscoBall:
                        M3Editor.LevelController.SetSuperDiscoBallValue(currentRecord.SuperDiscoBall);
                        break;

                    case ReplayLevelSystem.PlayerAction.VerticalBooster:
                        M3Editor.GameController.PlayerInputSync(new PlayerInputItemVerticalBooster(currentRecord.Positions[0]));
                        break;

                    case ReplayLevelSystem.PlayerAction.HorizontalBooster:
                        M3Editor.GameController.PlayerInputSync(new PlayerInputItemHorizontalBooster(currentRecord.Positions[0]));
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }
    }
}
#endif