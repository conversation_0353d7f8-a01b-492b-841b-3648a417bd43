using System;
using System.Collections.Generic;
using System.Linq;
using BBB.DI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorLevelSelectionPopup : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private GameObject _buttonTemplate;
        
        private Action<string> _selectCallback;

        private readonly List<Button> _levelSelectionButtons = new List<Button>();
        
        public void InitializeByContext(IContext context)
        {
            _closeButton.ReplaceOnClick(OnCloseButton);
        }

        private void OnCloseButton()
        {
            _selectCallback = null;
            gameObject.SetActive(false);
            
            foreach(var go in _levelSelectionButtons.Select(b => b.gameObject))
                Destroy(go);
            _levelSelectionButtons.Clear();
        }

        public void ShowLevelSelection(IEnumerable<string> levels, Action<string> selectCallback)
        {
            _selectCallback = selectCallback;

            var holderTransform = _buttonTemplate.transform.parent;
            foreach (var levelName in levels)
            {
                var buttonGo = Instantiate(_buttonTemplate, holderTransform);
                
                var button = buttonGo.GetComponent<Button>();
                var lName = levelName;
                button.ReplaceOnClick(() => Select(lName));

                buttonGo.name = lName;
                
                var buttonText = buttonGo.GetComponentInChildren<TextMeshProUGUI>();
                buttonText.text = lName;
                
                _levelSelectionButtons.Add(button);
                
                buttonGo.SetActive(true);
            }
            
            gameObject.SetActive(true);
        }

        private void Select(string levelName)
        {
            _selectCallback.SafeInvoke(levelName);
            
            _selectCallback = null;
            gameObject.SetActive(false);
            foreach(var go in _levelSelectionButtons.Select(b => b.gameObject))
                Destroy(go);
            _levelSelectionButtons.Clear();
        }
    }
}