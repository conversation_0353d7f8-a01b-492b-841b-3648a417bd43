using System;
using BBB.DI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class ProcessCancelledEvent : IEvent
    {
    }

    public class M3EditorProgressPopup : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private TextMeshProUGUI _mainTitle;
        [SerializeField] private TextMeshProUGUI _firstBarTitle;
        [SerializeField] private TextMeshProUGUI _secondBarTitle;
        [SerializeField] private Image _firstBarProgressImage;
        [SerializeField] private Image _secondBarProgressImage;
        [SerializeField] private Button _cancelButton;
        [SerializeField] private Button _skipButton;

        private Action _cancelCallback;
        private Action _skipCallback;
        private IEventDispatcher _eventDispatcher;

        public void InitializeByContext(IContext context)
        {
            _firstBarTitle.gameObject.SetActive(false);
            _secondBarTitle.gameObject.SetActive(false);
            _firstBarProgressImage.fillAmount = 0f;
            _secondBarProgressImage.fillAmount = 0f;
            _cancelButton.ReplaceOnClick(OnCancelButton);
            _skipButton.ReplaceOnClick(OnSkip);
            _skipButton.gameObject.SetActive(false);
            _eventDispatcher = context.Resolve<IEventDispatcher>();
        }

        private void OnCancelButton()
        {
            _cancelCallback.SafeInvoke();
            _cancelCallback = null;
            _cancelButton.interactable = false;
            _eventDispatcher.TriggerEventNextFrame(_eventDispatcher.GetMessage<ProcessCancelledEvent>());
        }

        private void OnSkip()
        {
            _skipCallback?.Invoke();
        }

        public void SetProcessTitle(string title)
        {
            if (_mainTitle != null)
            {
                _mainTitle.text = title;
            }
        }

        public void SetCancelCallback(Action cancelCallback)
        {
            _cancelCallback = cancelCallback;
        }

        public void SetSkipCallback(Action skipCallback)
        {
            _skipCallback = skipCallback;
            _skipButton.gameObject.SetActive(true);
        }

        public void ShowFirstBar(string text, float progress)
        {
            if (!_firstBarTitle.gameObject.activeSelf)
                _firstBarTitle.gameObject.SetActive(true);

            _firstBarTitle.text = text;
            _firstBarProgressImage.fillAmount = progress;
            Show();
        }

        public void ShowSecondBar(string text, float progress)
        {
            if (!_secondBarTitle.gameObject.activeSelf)
                _secondBarTitle.gameObject.SetActive(true);

            _secondBarTitle.text = text;
            _secondBarProgressImage.fillAmount = progress;
            Show();
        }

        private void Show()
        {
            if (!gameObject.activeSelf)
            {
                gameObject.SetActive(true);
            }
        }

        public void Hide()
        {
            _firstBarTitle.gameObject.SetActive(false);
            _secondBarTitle.gameObject.SetActive(false);
            _firstBarProgressImage.fillAmount = 0f;
            _secondBarProgressImage.fillAmount = 0f;
            gameObject.SetActive(false);
            _cancelButton.interactable = true;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _cancelCallback = null;
            _skipCallback = null;
        }
    }
}