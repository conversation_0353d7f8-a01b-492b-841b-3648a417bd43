#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Input;
using BBB.Wallet;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Utils;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorReplayPopup : BbbMonoBehaviour
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private Button _pasteBinDataButton;

        [SerializeField] private Button _playAllButton;
        [SerializeField] private TextMeshProUGUI _playAllButtonText;
        [SerializeField] private Image _playAllButtonImage;
        [SerializeField] private Button _playOneStepButton;

        [SerializeField] private Text _pastedText;
        [SerializeField] private ReplayListItem _itemTemplate;
        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private RectTransform _listContent;

        private readonly ReplayLevelSystem _replaySystem = new();
        private readonly List<ReplayListItem> _listInstances = new();
        private ReplayListItem _selectedItem;
        public bool IsPlaying;
        public string AssistSystemUid;
        private bool _isPlayingOneStep;
        private bool _resumeNextStep;

        private IInputController _inputController;
        private M3EditorLevelFileToConfigMap _fileToConfigMap;
        private SpawnerSettings[] _globalSpawners;

        public M3Editor M3Editor { get; set; }

        private void Awake()
        {
            _closeButton.ReplaceOnClick(OnCloseButton);
            _closeButton.ReplaceOnClick(OnCloseButton);
            _pasteBinDataButton.ReplaceOnClick(OnPasteBinaryData);

            _playAllButton.ReplaceOnClick(OnPlayAllButton);
            _playAllButton.interactable = false;

            _playOneStepButton.ReplaceOnClick(OnPlayOneStep);
            _playOneStepButton.interactable = false;

        }

        private void OnCloseButton()
        {
            gameObject.SetActive(false);
        }

        private void OnPasteBinaryData()
        {
            var te = new TextEditor();
            te.Paste();
            _replaySystem.SetFromBinaryData(te.text);
            _pastedText.text = _replaySystem.ToString();
            Refresh();
            RestoreRandomSystem();
        }

        private void RestoreRandomSystem()
        {
            RandomSystem.DebugLockSeed = false;
            RandomSystem.Reset(_replaySystem.RandomSeed);
            RandomSystem.DebugLockSeed = true;
            M3Editor.LevelController.PlayerManager.SetOnAimToWinSkillModifier(_replaySystem
                .GetOnAimToWinSkillModifier);
            M3Editor.LevelController.PlayerManager.SetOnAimToLoseSkillModifier(_replaySystem
                .GetOnAimToLoseSkillModifier);
            AssistParams.AimAtWinningLevel = _replaySystem.AimAtWinningLevel;
            M3Editor.LevelController.SetSuperDiscoBallValue(_replaySystem.InitialSuperDiscoBall);
            AssistSystemUid = _replaySystem.AssistSystemUid;
        }

        private void Refresh()
        {
            _replaySystem.PopulateListView(_itemTemplate, _listInstances, _listContent, OnItemSelected);
            var replaySystemRecordsIsNotNullOrEmpty = !_replaySystem.Records.IsNullOrEmpty();
            _playAllButton.interactable = replaySystemRecordsIsNotNullOrEmpty;
            _playOneStepButton.interactable = replaySystemRecordsIsNotNullOrEmpty;
        }

        private void OnItemSelected(ReplayListItem item)
        {
            if (IsPlaying) return;
            
            var index = _listInstances.IndexOf(item);
            SetSelectedItem(index);
        }

        private void SetSelectedItem(int index)
        {
            if (index < 0) return;
            if (_selectedItem != null)
            {
                _selectedItem.SetSelected(false);
            }
            _selectedItem = _listInstances[index];
            _selectedItem.SetSelected(true);
        }

        private void RestartLevel()
        {
            var utils = M3Editor.GetSystem<M3EditorSaveLoad>();
            if (M3Editor.PlayState) utils.TogglePlaying();
            utils.TogglePlaying();
        }

        private void OnPlayAllButton()
        {
            _isPlayingOneStep = false;
            IsPlaying = !IsPlaying;
            if (IsPlaying)
            {
                OnStartPlaying();
                StartCoroutine(StartReplay());
            }
            else
            {
                StopPlaying(true);
            }
        }

        private void OnPlayOneStep()
        {
            _isPlayingOneStep = true;
            _playOneStepButton.interactable = false;

            if (!IsPlaying)
            {
                IsPlaying = true;
                OnStartPlaying();
                StartCoroutine(StartReplay());
            }
            else
            {
                _resumeNextStep = true;
            }
        }

        private void OnStartPlaying()
        {
            _pasteBinDataButton.interactable = false;
            _playAllButtonText.text = "STOP";
            _playAllButtonImage.enabled = false;
            SetSelectedItem(0);
            RestartLevel();
        }

        private void StopPlaying(bool stopSystem)
        {
            IsPlaying = false;
            _isPlayingOneStep = false;
            _playOneStepButton.interactable = true;
            _pasteBinDataButton.interactable = true;
            _playAllButtonText.text = "Play";
            _playAllButtonImage.enabled = true;
            if (M3Editor.PlayState && stopSystem)
            {
                var utils = M3Editor.GetSystem<M3EditorSaveLoad>();
                utils.TogglePlaying();
            }
        }

        private void PlaceBoosters(IEnumerable<ReplayLevelSystem.AutoBooster> autoBoosters)
        {
            var boosters = new List<AutoBoostInstance>();
            foreach (var autoBooster in autoBoosters)
            {
                switch (autoBooster.Type)
                {
                    case ReplayLevelSystem.AutoBoosterType.Bomb:
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.BombBooster, autoBooster.Pos));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.ColorBomb:
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.LightningStrikeBooster, autoBooster.Pos));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.LineBreaker:
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.LineCrushBooster, autoBooster.Pos,
                            SimplifiedDirections.Horizontal));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.ColumnBreaker:
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.LineCrushBooster, autoBooster.Pos,
                            SimplifiedDirections.Vertical));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.Propeller :
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.PropellerBooster, autoBooster.Pos));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.PropellerButler :
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.PropellerBoosterButler, autoBooster.Pos));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.BombButler :
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.BombBoosterButler, autoBooster.Pos));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.LineBreakerButler :
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.LineBreakerBoosterButler,
                            autoBooster.Pos, SimplifiedDirections.Horizontal));
                        break;
                    case ReplayLevelSystem.AutoBoosterType.ColumnBreakerButler :
                        boosters.Add(new AutoBoostInstance(InventoryBoosters.LineBreakerBoosterButler,
                            autoBooster.Pos, SimplifiedDirections.Vertical));
                        break;
                    default:
                        Debug.LogError($"Replay system: cannot find inventory booster for '{autoBooster.Type}'");
                        break;
                }
            }

            if (boosters.Count <= 0) return;
            var tileRevealer = M3Editor.LevelController.GetTileRevealer();
            StartCoroutine(tileRevealer.SettleExtraBoosters(M3Editor.GameController.Grid, boosters, true));
        }

        private IEnumerator StartReplay()
        {
            if (!IsPlaying) yield break;

            _inputController = M3Editor.Context.Resolve<IInputController>();
            for (var actionIndex = 0; actionIndex < _replaySystem.Records.Count; ++actionIndex)
            {
                if (!IsPlaying) yield break;
                if (_isPlayingOneStep) _resumeNextStep = false;

                // Waiting until input is unlocked
                while (M3Editor.GameController.IsInputLocked)
                {
                    if (!IsPlaying)
                        yield break;

                    if (M3Editor.GameController.GameEnded && !M3Editor.GameController.HasWon)
                        break;

                    yield return WaitCache.EndOfFrame;
                }
                yield return WaitCache.Seconds(0.5f);

                if (_isPlayingOneStep && actionIndex > 0)
                {
                    _playOneStepButton.interactable = true;
                    while (!_resumeNextStep)
                    {
                        yield return WaitCache.EndOfFrame;
                    }
                }
                if (!IsPlaying) yield break;

                SetSelectedItem(actionIndex);
                yield return WaitCache.Seconds(1f);
                
                // Place boosters until input is unlocked
                while (M3Editor.GameController.IsInputLocked)
                {
                    if (!IsPlaying)
                        yield break;

                    if (M3Editor.GameController.GameEnded && !M3Editor.GameController.HasWon)
                        break;

                    yield return WaitCache.EndOfFrame;
                }

                if (actionIndex == 0)
                {
                    // place pre game boosters if they exist for the 0th turn
                    if (_replaySystem.AutoBoosters.TryGetValue(actionIndex, out var preAutoBoosters))
                    {
                        PlaceBoosters(preAutoBoosters);
                        yield return WaitCache.Seconds(2f);
                    }
                }

                // Synchronize random state
                var currentRecord = _replaySystem.Records[actionIndex];
                RandomSystem.SetRandomGenerationValues(currentRecord.RandomState);

                switch (currentRecord.PlayerAction)
                {
                    case ReplayLevelSystem.PlayerAction.SingleTap:
                    case ReplayLevelSystem.PlayerAction.DoubleTap:
                        _inputController.AutoDoubleTap(currentRecord.Positions[0]);
                        break;

                    case ReplayLevelSystem.PlayerAction.Swap:
                        _inputController.AutoSwap(currentRecord.Positions[0], currentRecord.Positions[1]);
                        break;

                    case ReplayLevelSystem.PlayerAction.Booster:
                        M3Editor.GameController.PlayerInputAsync(new PlayerInputItemShovel(currentRecord.Positions[0])).Forget();
                        break;

                    case ReplayLevelSystem.PlayerAction.SuperBoost:
                        M3Editor.LevelController.SuperBoostPanelController.TriggerSupperBoostOnReplay(currentRecord.Positions[0]);
                        break;

                    case ReplayLevelSystem.PlayerAction.AddedMoves:
                        M3Editor.DebugView.AddMoves(true, currentRecord.AddedMoves);
                        yield return WaitCache.Seconds(1f);
                        break;
                    
                    case ReplayLevelSystem.PlayerAction.SuperDiscoBall:
                        M3Editor.LevelController.SetSuperDiscoBallValue(currentRecord.SuperDiscoBall);
                        yield return WaitCache.Seconds(1f);
                        break;
                    
                    case ReplayLevelSystem.PlayerAction.VerticalBooster:
                        M3Editor.GameController.PlayerInputAsync(new PlayerInputItemVerticalBooster(currentRecord.Positions[0])).Forget();
                        break;
                    
                    case ReplayLevelSystem.PlayerAction.HorizontalBooster:
                        M3Editor.GameController.PlayerInputAsync(new PlayerInputItemHorizontalBooster(currentRecord.Positions[0])).Forget();
                        break;
                }

                yield return WaitCache.Seconds(1f);
                
                // Waiting again to place boosters until input is unlocked
                while (M3Editor.GameController.IsInputLocked)
                {
                    if (!IsPlaying)
                        yield break;

                    if (M3Editor.GameController.GameEnded && !M3Editor.GameController.HasWon)
                        break;

                    yield return WaitCache.EndOfFrame;
                }

                if (actionIndex > 0)
                {
                    // place skipped pregame boosters if they exist > 0th turn
                    if (_replaySystem.AutoBoosters.TryGetValue(actionIndex, out var postAutoBoosters))
                    {
                        PlaceBoosters(postAutoBoosters);
                        yield return WaitCache.Seconds(2f);
                    }
                }
            }
            StopPlaying(false);
        }
    }
}
#endif
