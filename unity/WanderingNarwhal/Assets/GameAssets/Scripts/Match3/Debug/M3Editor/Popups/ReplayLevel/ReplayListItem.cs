using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class ReplayListItem : BbbMonoBehaviour, IPointerClickHandler
    {
        [SerializeField] private Text _moveOrdinalText;
        [SerializeField] private Text _moveContentText;
        [SerializeField] private GameObject _selectionObject;

        public event Action<ReplayListItem> OnSelectEvent;

        public void Setup(ReplayLevelSystem.Record record, int index, IList<ReplayLevelSystem.AutoBooster> autoBoosters)
        {
            _moveOrdinalText.text = index.ToString();

            var sb = new StringBuilder();

            sb.Append(record.PlayerAction.ToString());
            if (!record.Positions.IsNullOrEmpty())
            {
                sb.Append(": ");
                var first = true;
                foreach (var pos in record.Positions)
                {
                    const string plus = "+";
                    if (!first)
                    {
                        sb.Append(plus);
                    }
                    sb.Append(pos.ToString());
                    first = false;
                }
            }

            switch (record.PlayerAction)
            {
                case ReplayLevelSystem.PlayerAction.AddedMoves:
                    sb.Append(": ").Append(record.AddedMoves);
                    break;
                case ReplayLevelSystem.PlayerAction.SuperDiscoBall:
                    sb.Append(": ").Append(record.SuperDiscoBall);
                    break;
            }

            if (!autoBoosters.IsNullOrEmpty() && index > 1)
            {
                sb.Append(" + ");
                foreach (var autoBooster in autoBoosters)
                {
                    const string at = " at ";
                    const string semicolon = "; ";
                    
                    sb.Append(autoBooster.Type.ToString());
                    sb.Append(at);
                    sb.Append(autoBooster.Pos.ToString());
                    sb.Append(semicolon);
                }
            }

            sb.Append($" Time: {record.TimeStamp}");

            _moveContentText.text = sb.ToString();
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            if (eventData.clickCount == 1)
            {
                OnSelectEvent?.Invoke(this);
            }
        }

        public void SetSelected(bool active)
        {
            _selectionObject.SetActive(active);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnSelectEvent = null;
        }
    }
}
