#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB.Match3.Systems;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorSpawnerListItem : BbbMonoBehaviour
    {
        private const float MinFade = 0.5f;

        public Text SpawnerIndexText;
        public InputField SpawnerNameInput;
        public Button SelectButton;
        public Button ConfigureButton;
        public Button DeleteButton;
        public M3EditorSpawnerTileImage TilePreviewImageTemplate;
        public event Action<int> OnSelectedClickEvent;
        public event Action<int> OnConfigureClickEvent;
        public event Action<int> OnDeletedClickEvent;

        private readonly List<M3EditorSpawnerTileImage> _tilePreviews = new();

        private int _currentUid = -1;
        private SpawnerSettings _currentSpawner;

        private void Awake()
        {
            TilePreviewImageTemplate.gameObject.SetActive(false);
            SelectButton.onClick.AddListener(() => OnSelectedClickEvent?.Invoke(_currentUid));
            ConfigureButton.onClick.AddListener(() => OnConfigureClickEvent?.Invoke(_currentUid));
            DeleteButton.onClick.AddListener(() => OnDeletedClickEvent?.Invoke(_currentUid));
            SpawnerNameInput.onEndEdit.AddListener((str) => {
                if (_currentSpawner != null)
                {
                    _currentSpawner.Name = str;
                }
            });
        }

        public void RefreshView(SpawnerSettings spawner, ISpawnerTileImageProvider imageProvider)
        {
            SpawnerIndexText.text = spawner.Uid.ToString();
            SpawnerNameInput.text = spawner.Name;
            UpdateTilePreviewImages(spawner, imageProvider);
        }

        public void SetActiveRemoveButton(bool active)
        {
            DeleteButton.interactable = active;
        }

        public void UpdateTilePreviewImages(SpawnerSettings spawner, ISpawnerTileImageProvider imageProvider)
        {
            _currentSpawner = spawner;
            _currentUid = spawner.Uid;
            SetImagesInstancesCount(spawner.TilesSettings.Length);

            var maxWeight = 0f;

            foreach (var tile in spawner.TilesSettings)
            {
                if (tile.ProbabilityWeight > maxWeight)
                {
                    maxWeight = tile.ProbabilityWeight;
                }
            }

            for (int i = 0; i < spawner.TilesSettings.Length; i++)
            {
                float ratio = maxWeight > 0 ? spawner.TilesSettings[i].ProbabilityWeight / maxWeight : 1f;
                float fade = Mathf.Lerp(MinFade, 1f, ratio);
                _tilePreviews[i].SetupImage(spawner.TilesSettings[i], imageProvider, fade);
            }
        }

        private void SetImagesInstancesCount(int count)
        {
            while (_tilePreviews.Count > count)
            {
                var last = _tilePreviews.Count - 1;
                var item = _tilePreviews[last];
                if (item != null)
                {
                    Destroy(item.gameObject);
                }

                _tilePreviews.RemoveAt(last);
            }

            while (_tilePreviews.Count < count)
            {
                var item = Instantiate(TilePreviewImageTemplate, parent: TilePreviewImageTemplate.transform.parent);
                item.gameObject.SetActive(true);
                _tilePreviews.Add(item);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnConfigureClickEvent = null;
            OnDeletedClickEvent = null;
            OnSelectedClickEvent = null;
        }
    }
}
#endif