#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB.Match3.Systems;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorSpawnerSettingsPopup : BbbMonoBehaviour
    {
        public M3EditorSpawnerTileListItem ListItemTemplate;
        public Button CloseButton;
        public Button AddListItemButton;
        public List<Button> PreSpawnedTilesPopupShowButtons;
        public GameObject AddListItemRootObject;

        [SerializeField] private GameObject _preSpawnedTilesPopup;
        [SerializeField] private TableLayout _preSpawnedTableLayout;
        

        private ISpawnerTileImageProvider _currentImageProvider;
        private SpawnerSettings _currentSpawner;
        private readonly List<M3EditorSpawnerTileListItem> _instances = new();

        public event Action OnClosedEvent;

        private void Awake()
        {
            ListItemTemplate.gameObject.SetActive(false);
            CloseButton.ReplaceOnClick(Hide);
            AddListItemButton.ReplaceOnClick(OnAddTileSpawnSettingClicked);
            if(PreSpawnedTilesPopupShowButtons != null)
                foreach (var button in PreSpawnedTilesPopupShowButtons)
                {
                    if(button != null)
                        button.ReplaceOnClick(OnPreSpawnedTilesShowHide);
                }
            if(_preSpawnedTilesPopup != null)
                _preSpawnedTilesPopup.gameObject.SetActive(false);
        }

        public void InitForSpawner(SpawnerSettings spawner, ISpawnerTileImageProvider imageProvider)
        {
            _currentSpawner = spawner;
            _currentImageProvider = imageProvider;
            RefreshViewInternal();
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }
        
        private List<PreSpawnedTile> FromTableToList()
        {
            var dataList = _preSpawnedTableLayout.GetData();
            var resultList = new List<PreSpawnedTile>();
            foreach (var item in dataList)
            {
                foreach (var kvp in item)
                {
                    if (kvp.Key == string.Empty)
                    {
                        Debug.LogError("Data empty key error");
                    }
                    
                    if (kvp.Value == string.Empty)
                    {
                        Debug.LogError("Data empty value error, for key " + kvp.Key);
                    }
                }
                
                var preSpawnedTile = new PreSpawnedTile
                {
                    Speciality = item["Speciality"].TryParseToEnum<TileSpeciality>(),
                    TileKind = item["TileKind"].TryParseToEnum<TileKinds>(),
                    Count = int.TryParse(item["Count"], out int count) ? count : -1
                };

                if (preSpawnedTile.Count == -1)
                {
                    Debug.LogError("Count field parsing error");
                }
                
                resultList.Add(preSpawnedTile);
            }

            return resultList;
        }

        private void FromListToTable(List<PreSpawnedTile> list)
        {
            var listOfDictionaries = new List<Dictionary<string, string>>();
            foreach (var entry in list)
            {
                listOfDictionaries.Add(new Dictionary<string, string>
                {
                    { "Speciality", entry.Speciality.ToString() },
                    { "TileKind", entry.TileKind.ToString() },
                    { "Count", entry.Count.ToString() }
                });
            }
            _preSpawnedTableLayout.SetData(listOfDictionaries);
        }

        public void Hide()
        {
            if (_preSpawnedTilesPopup.activeSelf)
            {
                _currentSpawner.FixedTilesToPreSpawn = FromTableToList();
                _preSpawnedTilesPopup.SetActive(false);
            }
            
            gameObject.SetActive(false);
        }

        protected override void OnDisable()
        {
            OnClosedEvent?.Invoke();
        }

        private void SetItemsCount(int count)
        {
            while (_instances.Count> count)
            {
                var last = _instances.Count - 1;
                var item = _instances[last];
                if (item != null)
                {
                    Destroy(item.gameObject);
                }

                _instances.RemoveAt(last);
            }

            while (_instances.Count < count)
            {
                _instances.Add(CreateListItem());
            }

            AddListItemRootObject.transform.SetAsLastSibling();
        }

        private void OnRemoveTileSpawnSettingClicked(int index)
        {
            if (_currentSpawner == null) return;

            if (index<0 || index>= _currentSpawner.TilesSettings.Length) return;

            var list = new List<TileSpawnSettings>(_currentSpawner.TilesSettings);
            list.RemoveAt(index);
            _currentSpawner.TilesSettings = list.ToArray();
            RefreshViewInternal();
        }

        private void OnAddTileSpawnSettingClicked()
        {
            if (_currentSpawner == null) return;

            var list = new List<TileSpawnSettings>(_currentSpawner.TilesSettings) { new () };
            _currentSpawner.TilesSettings = list.ToArray();
            RefreshViewInternal();
        }

        private void OnPreSpawnedTilesShowHide()
        {
            if (_preSpawnedTilesPopup.activeSelf)
            {
                _currentSpawner.FixedTilesToPreSpawn = FromTableToList();
            }
            else
            {
                FromListToTable(_currentSpawner.FixedTilesToPreSpawn);
            }
            
            _preSpawnedTilesPopup.gameObject.SetActive(!_preSpawnedTilesPopup.activeSelf);

        }

        private void RefreshViewInternal()
        {
            SetItemsCount(_currentSpawner.TilesSettings.Length);
            for (var i = 0; i < _currentSpawner.TilesSettings.Length; i++)
            {
                _instances[i].RefreshView(_currentSpawner.TilesSettings[i], tileIndex: i, imageProvider: _currentImageProvider);
            }
        }

        private M3EditorSpawnerTileListItem CreateListItem()
        {
            var item = Instantiate(ListItemTemplate, parent: ListItemTemplate.transform.parent);
            item.OnTileDeleteClickedEvent += OnRemoveTileSpawnSettingClicked;
            item.gameObject.SetActive(true);
            return item;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnClosedEvent = null;
        }
    }
}
#endif