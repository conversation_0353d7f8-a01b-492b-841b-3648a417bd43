#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public class AnimalTileEditorTool : TileTool
    {
        public AnimalTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;
            if (cell.Tile.Speciality != TileSpeciality.Frame) return;

            cell.Tile.Add(TileState.AnimalMod);
            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;
            cell.Tile.Remove(TileState.AnimalMod);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
