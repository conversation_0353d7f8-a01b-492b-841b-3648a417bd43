#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using UnityEditor;
using UnityEngine;
using Grid = BBB.Grid;
using Object = UnityEngine.Object;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    /// <summary>
    /// This is an abstract class that defines the common functionality of editor tools.
    /// </summary>
    public abstract class BaseEditorTool
    {
        // Fields for storing references to M3Editor systems
        protected readonly M3EditorCell M3EditorCell;
        protected readonly M3EditorTile M3EditorTile;

        // Fields for storing UI components
        private M3EditorUIActionButton _actionButton;
        private M3EditorAddTileButtonActiveState _buttonActiveState;

        // A field for storing a multi-tile component
        protected MultiTileElement MultiTileElement = new();

        /// <summary>
        /// Initializes a new instance of the BaseEditorTool class with the given M3Editor systems.
        /// </summary>
        /// <param name="m3Systems">A dictionary of M3Editor systems.</param>
        protected BaseEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems)
        {
            M3EditorTile = (M3EditorTile) m3Systems[typeof(M3EditorTile)];
            M3EditorCell = (M3EditorCell) m3Systems[typeof(M3EditorCell)];
        }

        /// <summary>
        /// Registers the tool to the editor by instantiating a prefab and setting up the UI components.
        /// </summary>
        /// <param name="parent">The parent transform for the prefab.</param>
        public void RegisterToEditor(Transform parent)
        {
            var prefab =
                AssetDatabase.LoadMainAssetAtPath(
                        $"Assets/GameAssets/Initial/Debug/Match3Editor/M3TileToolPrefab/{GetType().Name}.prefab") as
                    GameObject;
            var go = Object.Instantiate(prefab, Vector3.zero, Quaternion.identity, parent);
            go.name = go.name.Replace("(Clone)", string.Empty);
            _actionButton = go.GetComponent<M3EditorUIActionButton>();
            _buttonActiveState = go.GetComponent<M3EditorAddTileButtonActiveState>();
        }

        /// <summary>
        /// Gets the action button component of the tool.
        /// </summary>
        /// <returns>The action button component.</returns>
        public M3EditorUIActionButton GetActionButton()
        {
            return _actionButton;
        }

        /// <summary>
        /// Gets the active state button component of the tool.
        /// </summary>
        /// <returns>The active state button component.</returns>
        public M3EditorAddTileButtonActiveState GetActiveStateButton()
        {
            return _buttonActiveState;
        }

        /// <summary>
        /// Applies the tool logic to the given grid and coordinates.
        /// </summary>
        /// <param name="grid">The grid to modify.</param>
        /// <param name="coords">The coordinates to apply the tool.</param>
        /// <param name="cardinalDirections">The cardinal directions to apply the tool.</param>
        /// <param name="prm">An optional parameter for the tool.</param>
        public virtual void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            // This method is empty by default and should be overridden by subclasses.
        }

        /// <summary>
        /// Reverts the tool logic from the given grid and coordinates.
        /// </summary>
        /// <param name="grid">The grid to modify.</param>
        /// <param name="coords">The coordinates to revert the tool.</param>
        /// <param name="cardinalDirections">The cardinal directions to revert the tool.</param>
        /// <param name="prm">An optional parameter for the tool.</param>
        public virtual void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            // This method is empty by default and should be overridden by subclasses.
        }

         /// <summary>
         /// Tries to update the multi-tile component with the given coordinates and item index.
         /// </summary>
         /// <param name="coords">The coordinates to update.</param>
         /// <returns>True if the update was successful, false otherwise.</returns>
         public virtual bool TryUpdateMultiTile(Coords coords)
         {
             return MultiTileElement.TryUpdateMultiTileElement(coords);
         }
    }
}
#endif
