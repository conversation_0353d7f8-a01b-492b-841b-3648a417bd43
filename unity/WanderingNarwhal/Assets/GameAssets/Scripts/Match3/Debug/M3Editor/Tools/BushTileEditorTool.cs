#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class BushTileEditorTool : TileTool
    {
        public BushTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Bush, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            CreateTile(coords, grid, TileSpeciality.Bush, TileAsset.Bush, prm);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif