#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ChainTileEditorTool : TileTool
    {
        public ChainTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            var chainCount = cell.Tile.GetParam(TileParamEnum.ChainLayerCount);
            chainCount++;
            if (chainCount >= 3)
                chainCount = 3;

            cell.Tile.SetParam(TileParamEnum.ChainLayerCount, chainCount);
            cell.Tile.Add(TileState.Chained);
            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            if (cell.Tile.IsAnyOf(TileState.ChainMod))
            {
                var chainCount = cell.Tile.GetParam(TileParamEnum.ChainLayerCount);

                chainCount--;
                if (chainCount <= 0)
                    chainCount = 0;

                cell.Tile.SetParam(TileParamEnum.ChainLayerCount, chainCount);
                cell.Tile.CleanParams();

                if (chainCount <= 0) cell.Tile.Remove(TileState.Chained);
                
                M3EditorTile.RefreshGoals();
                M3EditorTile.RefreshGrid();
            }
        }
    }
}
#endif
