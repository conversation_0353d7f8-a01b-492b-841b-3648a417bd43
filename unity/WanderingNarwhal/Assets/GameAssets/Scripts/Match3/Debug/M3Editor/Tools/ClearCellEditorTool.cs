#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ClearCellEditorTool : BaseEditorTool, ICellTool
    {
        public ClearCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;
            M3EditorCell.ClearCell(cell);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
