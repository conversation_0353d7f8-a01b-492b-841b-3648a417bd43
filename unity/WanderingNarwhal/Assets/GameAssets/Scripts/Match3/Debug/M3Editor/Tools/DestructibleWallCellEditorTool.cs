#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class DestructibleWallCellEditorTool : BaseEditorTool, ICellTool
    {
        public DestructibleWallCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            SetDestructibleWall(grid, coords, cardinalDirections, prm);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            SetDestructibleWall(grid, coords, cardinalDirections, -1);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        private static void SetDestructibleWall(Grid grid, Coords coords, CardinalDirections cardinalDirections, int count)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;

            var newCoords = coords.GoSingleCardinalDirection(cardinalDirections);
            if (!grid.TryGetCell(newCoords, out var cellReverse)) return;

            cell.DestructibleWalls ??= new DestructibleWalls();
            cell.DestructibleWalls.DestructibleWall ??= new DestructibleWall[]
            {
                new() {Directions = CardinalDirections.N, Count = 0},
                new() {Directions = CardinalDirections.E, Count = 0},
                new() {Directions = CardinalDirections.W, Count = 0},
                new() {Directions = CardinalDirections.S, Count = 0}
            };
            
            cellReverse.DestructibleWalls ??= new DestructibleWalls();
            cellReverse.DestructibleWalls.DestructibleWall ??= new DestructibleWall[]
            {
                new() {Directions = CardinalDirections.N, Count = 0},
                new() {Directions = CardinalDirections.E, Count = 0},
                new() {Directions = CardinalDirections.W, Count = 0},
                new() {Directions = CardinalDirections.S, Count = 0}
            };
            
            if (cellReverse.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(cardinalDirections.Reversed())].Count > 0) return;
            
            var cellWall = cell.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(cardinalDirections)];

            var wallCount = cellWall.Count + count;
            wallCount = wallCount > 3 ? 0 : wallCount < 0 ? 0 : wallCount;

            cellWall.Count = wallCount;

            if (!cell.HasDestructibleWall())
                cell.Remove(CellState.DestructibleWall);
            else
                cell.Add(CellState.DestructibleWall);

            if (!cellReverse.HasDestructibleWall())
                cellReverse.Remove(CellState.DestructibleWall);
            else
                cellReverse.Add(CellState.DestructibleWall);
        }
    }
}
#endif
