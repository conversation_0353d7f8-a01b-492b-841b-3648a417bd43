#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class EggTileEditorTool : TileTool
    {
        public EggTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Egg, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            var targetHp = 1;
            if (cell.Tile != null)
            {
                var hp = cell.Tile.GetParam(TileParamEnum.EggLayerCount);
                targetHp = hp + 1;
                cell.HardRemoveTile(0);
            }

            targetHp = targetHp < 1 ? 1 : targetHp > 2 ? 2 : targetHp;
            var tileParams = new List<TileParam>(1)
            {
                new(TileParamEnum.EggLayerCount, targetHp)
            };
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++,TileAsset.Egg, new TileOrigin(Creator.LevelEditor, cell),
                TileKinds.None, tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
