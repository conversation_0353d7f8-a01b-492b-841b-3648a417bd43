#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class FillEmptyWithUndefinedCellEditorTool : BaseEditorTool, ICellTool
    {
        public FillEmptyWithUndefinedCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            grid.RefrehsAllCellsMultisizeCaches();

            foreach (var cell in grid.Cells)
            {
                if (cell.HasMultiSizeCellReference()) continue;
                if (!ReferenceEquals(cell.Tile, null)) continue;
                var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Simple,
                    new TileOrigin(Creator.LevelEditor, cell),
                    (TileKinds) (-2));
                cell.AddTile(newTile);
            }


            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
