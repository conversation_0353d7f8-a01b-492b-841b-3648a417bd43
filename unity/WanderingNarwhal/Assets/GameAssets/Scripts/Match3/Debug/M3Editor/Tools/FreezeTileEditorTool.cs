#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class FreezeTileEditorTool : TileTool
    {
        public FreezeTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            var iceCount = cell.Tile.GetParam(TileParamEnum.IceLayerCount);
            iceCount++;
            if (iceCount >= 3)
                iceCount = 3;

            cell.Tile.SetParam(TileParamEnum.IceLayerCount, iceCount);
            cell.Tile.Add(TileState.IceCubeMod);

            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            if (cell.Tile.IsAnyOf(TileState.IceCubeMod))
            {
                var iceCount = cell.Tile.GetParam(TileParamEnum.IceLayerCount);

                iceCount--;
                if (iceCount <= 0)
                    iceCount = 0;

                cell.Tile.SetParam(TileParamEnum.IceLayerCount, iceCount);
                cell.Tile.CleanParams();

                if (iceCount <= 0) cell.Tile.Remove(TileState.IceCubeMod);
            }
        }
    }
}
#endif
