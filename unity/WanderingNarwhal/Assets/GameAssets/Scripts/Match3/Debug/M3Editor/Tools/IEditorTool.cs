#if UNITY_EDITOR
using BBB;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    // This is an interface that defines the methods for an editor tool
    public interface IEditorTool
    {
        // This method registers the tool to the editor with a parent transform
        void RegisterToEditor(Transform parent);

        // This method applies the tool to a grid of tiles with given coordinates, directions and parameter
        void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm);

        // This method undoes the changes made by the tool to the grid
        void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm);

        // This method tries to update a multi-tile with given coordinates
        bool TryUpdateMultiTile(Coords coords);
    }
}
#endif
