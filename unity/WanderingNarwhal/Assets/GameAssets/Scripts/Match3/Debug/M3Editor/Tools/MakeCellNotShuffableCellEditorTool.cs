#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class MakeCellNotShuffableCellEditorTool : BaseEditorTool, ICellTool
    {
        public MakeCellNotShuffableCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.Contains(coords)) return;
            var cell = grid.GetCell(coords);

            if (cell.IsAnyOf(CellState.NotShuffable))
                cell.Remove(CellState.NotShuffable);
            else
                cell.Add(CellState.NotShuffable);


            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.Contains(coords)) return;
            var cell = grid.GetCell(coords);
            cell.Remove(CellState.NotShuffable);


            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
