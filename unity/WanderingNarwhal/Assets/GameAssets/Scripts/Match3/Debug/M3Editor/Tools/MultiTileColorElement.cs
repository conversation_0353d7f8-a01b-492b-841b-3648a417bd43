#if UNITY_EDITOR
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    // A class that represents a multi-tile element with a specific color
    public sealed class MultiTileColorElement : MultiTileElement
    {
        // A field that stores the reference to the M3EditorTile object
        private readonly M3EditorTile _m3EditorTile;
        // A field that stores the tile kind of the multi-tile element
        private readonly TileKinds _tileKind;

        // A constructor that takes a tile kind and a M3EditorTile object as parameters
        public MultiTileColorElement(TileKinds tileKind, M3EditorTile m3EditorTile)
        {
            // Assign the parameters to the fields
            _tileKind = tileKind;
            _m3EditorTile = m3EditorTile;
        }

        // A method that tries to update the multi-tile element with the given coordinates
        public override bool TryUpdateMultiTileElement(Coords coords)
        {
            // Call the TryUpdateMultiTileElementWithColor method of the M3EditorTile object with the fields and parameters
            return _m3EditorTile.TryUpdateMultiTileElementWithColor(coords, _tileKind);
        }
    }
}
#endif
