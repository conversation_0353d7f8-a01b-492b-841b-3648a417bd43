#if UNITY_EDITOR
using BBB;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public class MultiTileElement
    {
        //This is a virtual method that tries to update the multi-tile element at the given coordinates and returns true if successful
        //The default implementation returns false, but subclasses can override this method with their own logic 
        public virtual bool TryUpdateMultiTileElement(Coords coords)
        {
            return false;
        }
    }
}
#endif
