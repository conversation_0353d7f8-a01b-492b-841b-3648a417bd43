#if UNITY_EDITOR
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    // A class that represents a multi-tile element with a specific TNT target type
    public sealed class MultiTileTntElement : MultiTileElement
    {
        // A field that stores the reference to the M3EditorTile object
        private readonly M3EditorTile _m3EditorTile;
        // A field that stores the TNT target type of the multi-tile element
        private readonly TntTargetType _tntTargetType;

        // A constructor that takes a TNT target type and a M3EditorTile object as parameters
        public MultiTileTntElement(TntTargetType tntTargetType, M3EditorTile m3EditorTile)
        {
            // Assign the parameters to the fields
            _tntTargetType = tntTargetType;
            _m3EditorTile = m3EditorTile;
        }

        // A method that tries to update the multi-tile element with the given coordinates
        public override bool TryUpdateMultiTileElement(Coords coords)
        {
            // Call the TryUpdateMultiTileElementWithTargetType method of the M3EditorTile object with the fields and parameters
            return _m3EditorTile.TryUpdateMultiTileElementWithTargetType(coords, _tntTargetType);
        }
    }
}
#endif
