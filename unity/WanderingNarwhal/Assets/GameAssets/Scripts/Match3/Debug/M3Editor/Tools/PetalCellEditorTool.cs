#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class PetalCellEditorTool : BaseEditorTool, ICellTool
    {
        public PetalCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;

            cell.BackgroundCount = 1;
            cell.Remove(CellState.BackOne);
            cell.Remove(CellState.BackDouble);
            cell.Add(CellState.Petal);


            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;

            cell.BackgroundCount = 0;
            cell.Remove(CellState.Petal);


            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
