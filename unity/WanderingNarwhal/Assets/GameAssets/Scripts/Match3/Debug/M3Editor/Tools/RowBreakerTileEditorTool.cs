#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class RowBreakerTileEditorTool : TileTool
    {
        public RowBreakerTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.BoosterAny, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell != null)
            {
                var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.RowBreaker, 
                    new TileOrigin(Creator.LevelEditor, cell));

                if (cell.Tile != null)
                    cell.HardRemoveTile(0);

                cell.AddTile(newTile);
            }

            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
