#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SandTileEditorTool : TileTool
    {
        public SandTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            if (cell.Tile.IsNull())
            {
                var newTile =
                    TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Sand, new TileOrigin(Creator.LevelEditor, cell));
                cell.AddTile(newTile);
            }
            else
            {
                cell.Tile.Add(TileState.SandMod);
            }

            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            if (!cell.Tile.IsNull()) cell.Tile.Remove(TileState.SandMod);

            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
