#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using BBB.Match3.Renderer;
using UnityEditor;
using UnityEngine;
using Grid = BBB.Grid;

// This is an abstract class that inherits from BaseEditorTool and implements ITileTool interface
// It provides methods to apply and unapply changes to a grid of tiles
namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public abstract class TileTool : BaseEditorTool, ITileTool
    {
        // The constructor takes a dictionary of M3Editor systems as a parameter
        protected TileTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        // The Apply method takes a grid, coordinates, directions and a parameter as arguments
        // It calls the RefreshGoals and RefreshGrid methods of the M3EditorTile class
        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        protected virtual void CreateTile(Coords coords, Grid grid, TileSpeciality tileSpeciality, TileAsset tileAsset, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;
            
            var originCell = M3EditorTile.GetGridCell(coords, false);
            if (originCell == null) return;

            var prefabName = tileSpeciality.ToLayerState().ToFullAssetPath();
            var fullAssetPath = $"Assets/GameAssets/Initial/Match3/Bundle/{prefabName}.prefab";
            var renderer = AssetDatabase.LoadMainAssetAtPath(fullAssetPath) as GameObject;

            if (renderer == null)
            {
                UnityEngine.Debug.LogError(fullAssetPath + " not found");
                return;
            }

            var tileBehaviorContext = new TileBehaviorContext
            {
                TileParams = new List<TileParam>(),
                EditorParam = prm
            };

            var behaviors = renderer.GetComponents<ITileCreateBehaviour>();

            foreach (var behavior in behaviors)
            {
                behavior.Create(tileBehaviorContext);
            }

            //TODO move this to a separate behavior MultiSizeTileCreateBehaviour
            if (!IsTilePlacementValid(coords, grid, tileBehaviorContext))
            {
                return;
            }
            
            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++, tileAsset,
                new TileOrigin(Creator.LevelEditor, originCell),
                TileKinds.None, tileBehaviorContext.TileParams);

            originCell.AddTile(newTile);
        }

        private bool IsTilePlacementValid(Coords coords, Grid grid, TileBehaviorContext tileBehaviorContext)
        {
            var tileParams = tileBehaviorContext.TileParams;

            if (tileParams == null)
            {
                return false;
            }
            
            var sizeX = 1;
            var sizeY = 1;
            
            foreach (var tileParam in tileParams)
            {
                switch (tileParam.Param)
                {
                    case TileParamEnum.SizeX:
                        sizeX = tileParam.Value;
                        break;
                    case TileParamEnum.SizeY:
                        sizeY = tileParam.Value;
                        break;
                }
            }
            
            var fitsToGrid = true;
            for (var x = coords.X; x < coords.X + sizeX && fitsToGrid; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                var pos = new Coords(x, y);
                var cell = M3EditorTile.GetGridCell(pos, false);
                if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile())
                {
                    fitsToGrid = false;
                    break;
                }
            }

            if (!fitsToGrid) return false;

            for (var x = coords.X; x < coords.X + sizeX; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                grid.TryGetCell(new Coords(x, y), out var cell);
                cell.HardRemoveTile(0);
                cell.ClearState(true);
                cell.UpdateCellBackgroundState();
            }

            return true;
        }

        // The UnApply method takes the same arguments as the Apply method
        // It removes the tile at the given coordinates and calls the RefreshGoals and RefreshGrid methods
        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell == null) return;
            cell.HardRemoveTile(0);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
