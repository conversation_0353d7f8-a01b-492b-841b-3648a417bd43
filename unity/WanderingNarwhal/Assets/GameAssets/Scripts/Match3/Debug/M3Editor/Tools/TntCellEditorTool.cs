#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class TntCellEditorTool : BaseEditorTool, ICellTool
    {
        public TntCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell is null) return;

            CustomInputField.DeserializeParamsFromInt(prm, out var sizeX, out var sizeY, out var count, out _);

            cell.Add(CellState.Tnt);
            cell.SizeX = Mathf.Max(2, sizeX);
            cell.SizeY = Mathf.Max(2, sizeY);
            cell.TntCount = count;
            cell.TntKind = TileKinds.Yellow;

            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell is null) return;

            cell.Remove(CellState.Tnt);
            cell.SizeX = 0;
            cell.SizeY = 0;

            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
