#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class UndefinedTileEditorTool : TileTool
    {
        private const TileKinds TileKind = TileKinds.Undefined;

        public UndefinedTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileColorElement(TileKind, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            cell.HardRemoveTile(0);
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++,TileAsset.Simple,
                new TileOrigin(Creator.LevelEditor, cell), TileKind);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
