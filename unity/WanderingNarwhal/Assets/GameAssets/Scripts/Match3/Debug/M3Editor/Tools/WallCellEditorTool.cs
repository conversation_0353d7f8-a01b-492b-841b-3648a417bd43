#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class WallCellEditorTool : BaseEditorTool, ICellTool
    {
        public WallCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            grid.AddWallBetween(coords, cardinalDirections);
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            grid.RemoveWallBetween(coords, cardinalDirections);
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
