#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class WaterCellEditorTool : BaseEditorTool, ICellTool
    {
        public WaterCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;

            cell.HardRemoveTile(0);
            M3EditorCell.ClearCell(cell);
            cell.Add(CellState.Water);
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;

            cell.Remove(CellState.Water);
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
