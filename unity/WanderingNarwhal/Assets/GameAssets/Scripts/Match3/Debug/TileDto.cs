using System;
using System.Collections.Generic;

namespace BBB.Match3
{
    [Serializable]
    public struct TileParam
    {
        public TileParamEnum Param;
        public int Value;

        public TileParam(TileParamEnum param, int value)
        {
            Param = param;
            Value = value;
        }
    }

    [Serializable]
    public sealed class TileDto
    {
        public int Id;
        public TileAsset Asset;
        public int Kind;
        public ulong State;
        public List<TileParam> TileParams;
        public TileSpeciality Speciality;
        public int hp;
        public bool IntegrationEqualityCompare(TileDto other)
        {
            if (Id != other.Id) return false;
            if (Asset != other.Asset) return false;
            if (Kind != other.Kind) return false;
            if (Speciality != other.Speciality) return false;
            if (hp != other.hp) return false;

            bool isParamsExist = TileParams != null && TileParams.Count > 0;
            bool isOtherParamsExist = other.TileParams != null && other.TileParams.Count > 0;
            if ((isParamsExist && !isOtherParamsExist) || (isOtherParamsExist && !isParamsExist)) return false;

            if (isParamsExist)
            {
                if (TileParams.Count != other.TileParams.Count) return false;

                foreach (var prm in TileParams)
                {
                    bool paramFound = false;

                    foreach (var otherPrm in other.TileParams)
                    {
                        if (prm.Param == otherPrm.Param)
                        {
                            paramFound = true;
                            if (prm.Value != otherPrm.Value) return false;
                            break;
                        }
                    }

                    if (!paramFound)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
    }
}