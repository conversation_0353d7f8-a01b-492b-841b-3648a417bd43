namespace GameAssets.Scripts.Match3.Logic
{
    public enum AnimalSkin
    {
        <PERSON> = 0,
        <PERSON> = 1,
        <PERSON> = 2,
        <PERSON><PERSON> = 3,
        <PERSON> = 4,
        <PERSON> = 5,
        Squirrel = 6
    }

    public static class AnimalSkinExtensions
    {
        public static string ToSpriteName(this AnimalSkin animalSkin)
        {
            var result = string.Empty;
            switch (animalSkin)
            {
                case AnimalSkin.Cat:
                    result += "Cat_Sprite";
                    break;
                case AnimalSkin.Dog:
                    result +=  "Dog_Sprite";
                    break;
                case AnimalSkin.Bird:
                    result +=  "Bird_Sprite";
                    break;
                case AnimalSkin.Panda:
                    result +=  "Panda_Sprite";
                    break;
                case AnimalSkin.Pig:
                    result +=  "Pig_Sprite";
                    break;
                case AnimalSkin.Rabbit:
                    result +=  "Rabbit_Sprite";
                    break;
                case AnimalSkin.Squirrel:
                    result +=  "Squirrel_Sprite";
                    break;
                default:
                    return null;
            }

            return result;

        }
        
        public static string ToPrefabName(this AnimalSkin levelAnimalSkin)
        {
            switch(levelAnimalSkin)
            {
                case AnimalSkin.Cat:
                    return "<PERSON>";
                case AnimalSkin.Dog:
                    return "<PERSON>";
                case AnimalSkin.Bird:
                    return "<PERSON>";
                case AnimalSkin.Panda:
                    return "Panda";
                case AnimalSkin.Pig:
                    return "Pig";
                case AnimalSkin.Rabbit:
                    return "Rabbit";
                case AnimalSkin.Squirrel:
                    return "Squirrel";
                default:
                    return null;
            }
        }
    }
}