using System;
using BBB;
using BBB.DI;
using BBB.UI.Core;
using UnityEngine;
using UnityEngine.Profiling;

/// <summary>
/// Component that calls Release method on pooled object, which is used on match3 Effects prefabs.
/// </summary>
public class AutoReleaseEffect : ContextedUiBehaviour
{
    private PoolItem _poolItem;
    private float _timer = -1f;
    private Action _releaseAction;

    private IUpdateDispatcher _updateDispatcher;

    public void SetTime(float time)
    {
        if (_poolItem == null)
        {
            _poolItem = GetComponent<PoolItem>();
        }

        if (_poolItem == null)
        {
            Debug.LogError("AutoRelease can't find pool item component on attached object", gameObject);
            _timer = 0;
            enabled = false;
            return;
        }

        _timer = time;
    }

    protected override void InitWithContextInternal(IContext context)
    {
        _updateDispatcher = context.Resolve<IUpdateDispatcher>();
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        
        _updateDispatcher.OnUpdate += OnUpdateHandler;
    }

    private void OnUpdateHandler()
    {
        if (_poolItem == null)
        {
            // This may happen if SetTime was never called for this instance.
            // Usually this means that this component was forgotten to be removed from some prefab.
            enabled = false;
            return;
        }

        Profiler.BeginSample("AutoReleaseEffect.OnUpdateHandler");
        if (_timer >= 0f)
        {
            _timer -= Time.deltaTime;

            if (_timer <= 0f)
            {
                var pool = _poolItem.Pool;
                pool.Release(gameObject);
                _releaseAction.SafeInvoke();
                _releaseAction = null;
                _poolItem = null;
            }
        }
        Profiler.EndSample();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        
        _updateDispatcher.OnUpdate -= OnUpdateHandler;

        _timer = -1;
        if (_poolItem != null)
        {
            var pool = _poolItem.Pool;
            pool.Release(gameObject);
            _poolItem = null;
        }

        _releaseAction = null;
    }

    public void SetCallback(Action releaseCallback)
    {
        _releaseAction = releaseCallback;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        _releaseAction = null;
        _updateDispatcher.OnUpdate -= OnUpdateHandler;
    }
}