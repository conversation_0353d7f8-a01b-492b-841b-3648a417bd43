using System.Collections.Generic;
using BebopBee.Core;
using DG.Tweening;
using UnityEngine;
using BBB.Core;

namespace BBB
{
    public class BalloonEffect : ActivePoolItem
    {
        [SerializeField] private ParticleSystem _particleSystem;
        [SerializeField] private Ease _movementEase = Ease.InOutCubic;
        [SerializeField] private Ease _rotationEase = Ease.OutQuad;
        [SerializeField] private float _rotationAngleTarget = 20f;
        
        public void Play(List<Coords> coordsList, float totalTime, Vector2 cellSize)
        {
            var tf = transform;
            tf.localPosition = coordsList[0].GetPositionOfCoordinate(cellSize);

            var initialScale = Vector3.one;
            tf.localScale = Vector3.zero;
            var stepTime = totalTime / coordsList.Count;
            var sequence = DOTween.Sequence();

            var scaleUpTween = tf.DOScale(initialScale, stepTime).SetEase(_movementEase).OnComplete(() =>
            {
                var rotationSequence = DOTween.Sequence();
                for (int i = 1; i < coordsList.Count; i++)
                {
                    var xDif = (coordsList[i] - coordsList[i - 1]).X;
                    var rotateTarget = tf.localRotation.eulerAngles;

                    if (xDif == 0)
                        rotateTarget.z = 0f;
                    else
                        rotateTarget.z = _rotationAngleTarget * (xDif < 0 ? 1f : -1f);
                    
                    rotationSequence.Append(tf.DOLocalRotate(rotateTarget, stepTime).SetEase(_rotationEase));
                }
                
            });
            var scaleDownTween = tf.DOScale(Vector3.zero, stepTime).SetEase(_movementEase);
            
            sequence.Append(scaleUpTween);
            for (int i = 1; i < coordsList.Count; i++)
            {
                var targetPos = coordsList[i].GetPositionOfCoordinate(cellSize);
                sequence.Append(tf.DOLocalMove(targetPos, stepTime).SetEase(_movementEase));
            }
            sequence.Append(scaleDownTween);

            if (_particleSystem != null)
            {
                _particleSystem.Play();

                Rx.Invoke(totalTime, _ =>
                {
                    _particleSystem.Stop();
                });
            }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            var tf = transform;
            tf.localScale = Vector3.one;
            tf.localRotation = Quaternion.identity;
        }
    }
}