using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BananaAppearEffect : ConfigurableEffectBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _animationStartName;

        [SerializeField]
        private string _animationLoopName;

        public override void OnSpawn()
        {
            base.OnSpawn();
            if (!_animationStartName.IsNullOrEmpty() || !_animationLoopName.IsNullOrEmpty())
            {
                if (!_animationStartName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, _animationStartName, loop: false);
                    if (!_animationLoopName.IsNullOrEmpty())
                    {
                        _sk.AnimationState.AddAnimation(0, _animationLoopName, loop: true, delay: 0);
                    }
                }
                else
                {
                    _sk.AnimationState.SetAnimation(0, _animationLoopName, loop: true);
                }
            }
        }
    }
}