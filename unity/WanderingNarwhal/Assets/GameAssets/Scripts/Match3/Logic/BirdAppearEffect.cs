using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BirdAppearEffect : ConfigurableEffectBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _animationStartName;

        [SerializeField]
        private string _animationLoopName;

        public override void OnSpawn()
        {
            base.OnSpawn();
            if (!_animationStartName.IsNullOrEmpty())
            {
                _sk.AnimationState.ClearTracks();
                _sk.AnimationState.AddAnimation(0, _animationStartName, loop: false, delay: 0);
            }

            if (!_animationLoopName.IsNullOrEmpty())
            {
                if (_animationStartName.IsNullOrEmpty())
                {
                    _sk.AnimationState.ClearTracks();
                }

                _sk.AnimationState.AddAnimation(0, _animationLoopName, loop: true, delay: 0);
            }
        }
    }
}