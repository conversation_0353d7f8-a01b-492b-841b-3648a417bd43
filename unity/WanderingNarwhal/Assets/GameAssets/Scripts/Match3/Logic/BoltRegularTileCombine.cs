using System;
using BBB.Core;
using GameAssets.Scripts.Match3.Settings;
using JetBrains.Annotations;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BoltRegularTileCombine : ActivePoolItem
    {
        private const string ActionAnimation = "action";
        [SerializeField] 
        private Animation _animation;
        [SerializeField] 
        private SkeletonGraphic _skeletonGraphic;

        private Action _callback;

        public override void OnRelease()
        {
            if (_animation)
            {
                foreach (AnimationState state in _animation)
                {
                    state.speed = 1f;
                }
            }

            base.OnRelease();
        }

        private void OnAnimEnded()
        {
            _callback.SafeInvoke();
            _callback = null;
        }
        
        [UsedImplicitly]
        private void SetSpineAnimation()
        {
            if(_skeletonGraphic == null)return;
            _skeletonGraphic.AnimationState.ClearTracks();
            _skeletonGraphic.AnimationState.SetAnimation(0, ActionAnimation, false);
        }
        
        public void PlayClipWithDuration(M3Settings m3Settings, float boltAnimDuration, Action callback)
        {
            if (_skeletonGraphic != null)
            {
                _skeletonGraphic.Skeleton.SetSkin(m3Settings.GetDiscoBallSkin());
            }

            if (!_animation)
                return;

            
            foreach (AnimationState state in _animation)
            {
                var currentClipLength = state.length;
                var speedMult = currentClipLength / boltAnimDuration;
                state.speed = speedMult;
            }
            _animation.Play();
            _callback = callback;
        }
    }
}