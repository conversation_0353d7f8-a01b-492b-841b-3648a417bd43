using System;
using BBB.Core;
using BebopBee.Core;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BombBombCombineEffect : ActivePoolItem
    {
        [SerializeField] private string _animName;
        [SerializeField] private SkeletonGraphic _skeletonGraphic;
        private Animator _animator;
        private Action _callback;

        public override void OnInstantiate()
        {
            if (!Initialized)
                _animator = GetComponent<Animator>();
            base.OnInstantiate();
        }
        public override void OnSpawn()
        {
            base.OnSpawn();
            _skeletonGraphic.AnimationState.AddAnimation(0, _animName, loop: false, 0f);
            var anim = _skeletonGraphic.SkeletonData.FindAnimation("BombBoost_combo");
            if (anim != null)
            {
                Rx.Invoke(anim.Duration, _ =>
                {
                    _callback.SafeInvoke();
                    _callback = null;
                });
            }
        }

        public override void OnRelease()
        {
            _skeletonGraphic.Skeleton.SetToSetupPose();
            _skeletonGraphic.AnimationState.ClearTracks();
            base.OnRelease();

            if (_animator && !Initialized)
            {
                _animator.Rebind();
            }
        }

        public void SetCallback(Action callback)
        {
            _callback = callback;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _callback = null;
        }
    }
}