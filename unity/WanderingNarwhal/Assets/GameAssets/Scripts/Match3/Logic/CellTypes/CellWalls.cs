using System;

namespace BBB.CellTypes
{
    [Serializable]
    public sealed class CellWalls
    {
        public CardinalDirections Directions { get; set; }

        public CellWalls(CardinalDirections directions)
        {
            Directions = directions;
        }

        public CellWalls Clone()
        {
            return new CellWalls(Directions);
        }

        public CellWallsDto ToDto()
        {
            return new CellWallsDto() { directions = Directions };
        }

        public void FromDto(CellWallsDto dto)
        {
            Directions = dto.directions;
        }
    }

    [Serializable]
    public class CellWallsDto
    {
        public CardinalDirections directions;
    }
}