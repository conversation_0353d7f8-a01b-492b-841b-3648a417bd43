using System;

namespace BBB.CellTypes
{
    [Serializable]
    public class DestructibleWalls
    {
        public const int NumberOfWalls = 4;

        public DestructibleWall[] DestructibleWall { get; set; }
        
        public DestructibleWalls Clone()
        {
            var walls = new DestructibleWalls
            {
                DestructibleWall = new DestructibleWall[NumberOfWalls]
            };
            
            if (DestructibleWall != null)
            {
                for (var i = 0; i < NumberOfWalls; i++)
                {
                    walls.DestructibleWall[i] = DestructibleWall[i]?.Clone();
                }
            }
            
            return walls;
        }
        
        public static int CardinalToIndex(CardinalDirections direction)
        {
            return direction switch
            {
                CardinalDirections.N => 0,
                CardinalDirections.E => 1,
                CardinalDirections.W => 2,
                CardinalDirections.S => 3,
                _ => 0
            };
        }
        
        public static CardinalDirections IndexToCardinal(int index)
        {
            return index switch
            {
               0 => CardinalDirections.N,
               1 => CardinalDirections.E,
               2 => CardinalDirections.W,
               3 => CardinalDirections.S,
               _ => CardinalDirections.N
            };
        }
    }
    
    
    [Serializable]
    public class DestructibleWall
    {
        public CardinalDirections Directions { get; set; }
        public int Count { get; set; }
        
        public DestructibleWall Clone()
        {
            return new DestructibleWall
            {
                Directions = Directions,
                Count = Count
            };
        }
    }
}