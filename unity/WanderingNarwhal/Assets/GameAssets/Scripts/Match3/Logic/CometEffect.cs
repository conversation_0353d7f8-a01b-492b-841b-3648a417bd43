using DG.Tweening;
using UnityEngine;
using BBB.Core;

namespace BBB
{
    public class CometEffect : ActivePoolItem
    {
        [SerializeField] private ParticleSystem[] _particleSystems;

        public void Play(Vector2 from, Vector2 to, float time)
        {
            var tf = transform;
            tf.position = from;
            tf.localPosition = new Vector3(tf.localPosition.x, tf.localPosition.y, 0f);
            tf.DOMove(to, time).SetEase(Ease.InCubic);
            
            foreach (var ps in _particleSystems)
                ps.Play(false);
        }
        
        public override void OnInstantiate()
        {
            transform.position = new Vector2(-10000, -10000);
            base.OnInstantiate();
        }

        public override void OnRelease()
        {
            base.OnRelease();
            transform.position = new Vector2(-10000, -10000);
            if (!Initialized)
            {
                foreach (var ps in _particleSystems)
                    ps.Stop(false);
            }
        }
    }
}