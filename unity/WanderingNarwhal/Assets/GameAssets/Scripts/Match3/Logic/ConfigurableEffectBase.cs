using System;
using BBB;
using UnityEngine;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic
{
    /// <summary>
    /// Effect that is controlled by special settings.
    /// Used for <PERSON>, Banana, Bee and Chicken effects.
    /// </summary>
    [DisallowMultipleComponent]
    public class ConfigurableEffectBase : ActivePoolItem, IPoolItemReleaseNotifier
    {
        public event Action OnReleaseEvent;

        public override void OnRelease()
        {
            base.OnRelease();
            OnReleaseEvent?.Invoke();
            OnReleaseEvent = null;
        }

        public virtual void ApplyParameters(FxOptionalParameters prm)
        {
        }
    }
}