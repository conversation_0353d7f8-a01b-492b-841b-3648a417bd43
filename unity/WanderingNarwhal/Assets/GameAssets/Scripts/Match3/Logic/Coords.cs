using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;

namespace BBB
{
    [Serializable]
    public struct Coords : IEquatable<Coords>
    {
        public static readonly Coords OutOfGrid = new(-1, -1);
        public static readonly Vector2 OutOfGridVector2 = -1 * Vector2.one;
        public static readonly Coords Zero = new (0, 0);

        public int X;
        public int Y;

        public Coords(int x, int y)
        {
            X = x;
            Y = y;
        }

        public Coords(Vector2 v)
        {
            X = (int)v.x;
            Y = (int)v.y;
        }

        [JsonIgnore]
        public int SqrMagnitude => X * X + Y * Y;

        public bool IsUnitDistanceFrom(Coords other)
        {
            return X - other.X == 0 && (Y - other.Y == 1 || Y - other.Y == -1)
                || Y - other.Y == 0 && (X - other.X == 1 || X - other.X == -1);
        }

        public float DistanceFrom(Vector2 anchorPoint)
        {
            return (ToUnityVector2() - anchorPoint).magnitude;
        }

        public float SqrDistanceFrom(Vector2 anchorPoint)
        {
            return (ToUnityVector2() - anchorPoint).sqrMagnitude;
        }

        public Vector2 ToUnityVector2()
        {
            return new Vector2(X, Y);
        }

        public Vector3 ToXZ()
        {
            return new Vector3(X, 0, Y);
        }
        
        public Coords Perp()
        {
            return new Coords(Y, -X);
        }

        public readonly bool Equals(Vector2 obj)
        {
            return obj.x == X && obj.y == Y;
        }
        
        public override bool Equals(object obj)
        {
            return obj is Coords coords && this == coords;
        }
        
        public bool Equals(Coords other)
        {
            return this == other;
        }

        public override int GetHashCode()
        {
            unchecked
            {
                int hash = (int)2166136261;
                hash = (hash * 16777619) ^ X;
                hash = (hash * 16777619) ^ Y;
                return hash;
            }
        }

        public static Coords operator +(Coords a, Coords b)
        {
            return new Coords(a.X + b.X, a.Y + b.Y);
        }

        public static Coords operator -(Coords a, Coords b)
        {
            return new Coords(a.X - b.X, a.Y - b.Y);
        }

        public static Coords operator *(int k, Coords a)
        {
            return new Coords(a.X * k, a.Y * k);
        }

        public static Coords operator /(Coords a, int k)
        {
            return new Coords(a.X / k, a.Y / k);
        }

        public static bool operator ==(Coords a, Coords b)
        {
            return a.X == b.X && a.Y == b.Y;
        }

        public static bool operator !=(Coords a, Coords b)
        {
            return !(a == b);
        }

        public static bool operator <(Coords a, Coords b)
        {
            return a.CompareTo(b) < 0;
        }

        public static bool operator >(Coords a, Coords b)
        {
            return a.CompareTo(b) > 0;
        }

        private int CompareTo(Coords other)
        {
            if (Y > other.Y)
            {
                return 1;
            }

            if (Y < other.Y)
            {
                return -1;
            }

            if (X > other.X)
            {
                return 1;
            }

            if (X < other.X)
            {
                return -1;
            }

            return 0;
        }

        public override string ToString()
        {
            return "[" + X + "," + Y + "]";
        }

        public static bool StringToCoords(string str, out Coords coords)
        {
            var strs = str.Trim('[', ']').Split(',');

            if (strs.Length != 2)
            {
                coords = default(Coords);
                return false;
            }

            int x;
            if (!int.TryParse(strs[0], out x))
            {
                coords = default(Coords);
                return false;
            }

            int y;
            if (!int.TryParse(strs[1], out y))
            {
                coords = default(Coords);
                return false;
            }

            coords = new Coords(x, y);
            return true;
        }
    }
    
    public class SortedCoordsSet
    {
        private readonly List<Coords> _coords;
        public SortedCoordsSet(IEnumerable<Match> matches)
        {
            var coordsSet = new HashSet<Coords>();

            foreach (var match in matches)
            {
                foreach (var coord in match.GetAllCoords())
                {
                    coordsSet.Add(coord);
                }
            }

            _coords = new List<Coords>(coordsSet);
            _coords.Sort((c1, c2) => c1.X != c2.X ? c1.X.CompareTo(c2.X) : c1.Y.CompareTo(c2.Y));
        }

        public override bool Equals(object obj)
        {
            return (obj is SortedCoordsSet ct) && Equals(ct);
        }
        
        private bool Equals(SortedCoordsSet other)
        {
            if (_coords.Count != other._coords.Count)
            {
                return false;
            }

            var thisEnumerator = _coords.GetEnumerator();
            var otherEnumerator = other._coords.GetEnumerator();

            while (thisEnumerator.MoveNext() && otherEnumerator.MoveNext())
            {
                if (!thisEnumerator.Current.Equals(otherEnumerator.Current))
                {
                    return false;
                }
            }

            return true;
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hash = (int)2166136261;
                foreach (var coord in _coords)
                {
                    hash = (hash * 16777619) ^ coord.GetHashCode();
                }
                return hash;
            }
        }
    }


    public static class CoordsHelper
    {
        public static Coords Prep(Coords coords)
        {
            return new Coords(coords.Y, -coords.X);
        }

        public static bool IsAdjacent(this Coords first, Coords second)
        {
            var deltaX = Mathf.Abs(first.X - second.X);
            var deltaY = Mathf.Abs(first.Y - second.Y);

            if (deltaX > 0 && deltaY > 0) return false;

            return deltaX + deltaY == 1;
        }

        public static int DistanceBetweenCoordsOnTheSameLine(this Coords first, Coords second)
        {
            var deltaX = Mathf.Abs(first.X - second.X);
            var deltaY = Mathf.Abs(first.Y - second.Y);

            if (deltaX > 0 && deltaY > 0)
                throw new Exception("[CoordsHelper]: Coordinates not on the same line: " + first + " and " + second);

            return deltaX + deltaY;
        }
        
        public static IEnumerable<Coords> GetCoordsAround(this Coords coords, int distance = 1, bool includeCenter = false)
        {
            var minX = coords.X - distance;
            var minY = coords.Y - distance;
            var maxX = coords.X + distance;
            var maxY = coords.Y + distance;

            for (var x = minX; x <= maxX; x++)
            {
                for (var y = minY; y <= maxY; y++)
                {
                    if(includeCenter || minX != coords.X || minY != coords.Y)
                        yield return new Coords(x, y);
                }   
            }
        }

        public static Vector3 GetPositionOfCoordinate(this Coords coords, Vector2 tileSize)
        {
            return coords.ToUnityVector2().Multiply(y: tileSize) + tileSize / 2f;
        }
    }
}