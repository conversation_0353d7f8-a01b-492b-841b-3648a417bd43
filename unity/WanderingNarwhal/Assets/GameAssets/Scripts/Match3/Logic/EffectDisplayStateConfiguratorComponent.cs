using BBB;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Match3.Logic
{
    /// <summary>
    /// Change color for the parts of the effect by dynamic parameters.
    /// </summary>
    public class EffectDisplayStateConfiguratorComponent : BbbMonoBehaviour
    {
        [SerializeField]
        private ParticleSystem[] _particles;

        [SerializeField]
        private SpriteRenderer[] _sprites;

        [SerializeField]
        private Image[] _images;

        public void SetParticlesColor(Color color)
        {
            foreach (var ps in _particles)
            {
                var main = ps.main;
                main.startColor = color;
            }
        }

        public void SetSpritesColor(Color color)
        {
            foreach (var spriteRenderer in _sprites)
            {
                spriteRenderer.color = color;
            }

            foreach (var image in _images)
            {
                image.color = color;
            }
        }
    }
}