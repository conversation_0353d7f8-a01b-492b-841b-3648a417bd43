using System;
using BBB;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Renderer;
using BebopBee.Core;
using DG.Tweening;
using GameAssets.Scripts.Wallet.Visualizing;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Match3.Logic
{
    public class FireWorksFlightEffect : ActivePoolItem
    {
        [SerializeField] private Transform _rocket;
        [SerializeField] private Transform _shadow;
        [SerializeField] private SpineDrivenPathHolder _leftSpinePath;
        [SerializeField] private SpineDrivenPathHolder _rightSpinePath;
        [SerializeField] private SpineDrivenPathHolder _leftShadowSpinePath;
        [SerializeField] private SpineDrivenPathHolder _rightShadowSpinePath;
        [SerializeField] private Image[] _imagesToEnableDisable;

        private Tweener _tweener;
        private Action _callback;
        private readonly Quaternion _rocketAngle = Quaternion.Euler(0, 0, 270);

        public override void OnInstantiate()
        {
            _leftSpinePath.gameObject.SetActive(false);
            _rightSpinePath.gameObject.SetActive(false);
            _leftShadowSpinePath.gameObject.SetActive(false);
            _rightShadowSpinePath.gameObject.SetActive(false);

            foreach (var image in _imagesToEnableDisable)
            {
                image.enabled = true;
            }

            base.OnInstantiate();
        }

        public void Launch(Vector2 sourceCoords, Vector2 targetCoords, float duration, IGridController gridController,
            FireWorksEffectSettings settings, Action callback)
        {
            _callback = callback;

            var isSameColumn = Math.Abs(sourceCoords.x - targetCoords.x) == 0;
            var isFirstColumn = Math.Abs(sourceCoords.x) <= settings.FireWorksTargetPositionOffset;
            var isLastColumn = Math.Abs(sourceCoords.x - (gridController.GetCurrentGridSize().x - 1)) <= settings.FireWorksTargetPositionOffset;

            var spineDrivenPath = (isSameColumn && isFirstColumn) || (!isSameColumn && targetCoords.x - sourceCoords.x > 0) ? _rightSpinePath
                : (isSameColumn && isLastColumn) || targetCoords.x < sourceCoords.x ? _leftSpinePath : _rightSpinePath;
            
            var sourcePosition = gridController.ToDisplacedWorldPosition(sourceCoords);
            var targetPosition = gridController.ToDisplacedWorldPosition(targetCoords);
            
            transform.position = sourcePosition;

            spineDrivenPath.gameObject.SetActive(true);
            spineDrivenPath.SetPositions(sourcePosition, targetPosition);
            var followingTransform = spineDrivenPath.GetFollowingTransformFromStartPosition();
            followingTransform.Start(duration);

            var shadowSpinePath = spineDrivenPath == _rightSpinePath ? _rightShadowSpinePath : _leftShadowSpinePath;
            shadowSpinePath.gameObject.SetActive(true);
            shadowSpinePath.SetPositions(sourcePosition, targetPosition);
            var shadowFollowingTransform = shadowSpinePath.GetFollowingTransformFromStartPosition();
            shadowFollowingTransform.Start(duration);

            _rocket.position = followingTransform.Transform.position;
            _rocket.localRotation = followingTransform.Transform.localRotation * _rocketAngle;

            _shadow.position = shadowFollowingTransform.Transform.position;
            _shadow.localRotation = shadowFollowingTransform.Transform.localRotation * _rocketAngle;

            _tweener = DOTween.To(() => 0f, _ => { _rocket.position = followingTransform.Transform.position; }, 1f, duration);
            DOTween.To(() => 0f, _ => { _rocket.localRotation = followingTransform.Transform.localRotation * _rocketAngle; }, 1f, duration);
            DOTween.To(() => 0f, progress =>
            {
                _rocket.localScale = new Vector3(settings.FireWorksFlightScaleCurveX.Evaluate(progress),
                    settings.FireWorksFlightScaleCurveY.Evaluate(progress),
                    settings.FireWorksFlightScaleCurveZ.Evaluate(progress));
            }, 1f, duration);

            DOTween.To(() => 0f, _ => { _shadow.position = shadowFollowingTransform.Transform.position; }, 1f, duration);
            DOTween.To(() => 0f, _ => { _shadow.localRotation = shadowFollowingTransform.Transform.localRotation * _rocketAngle; }, 1f, duration);
            DOTween.To(() => 0f, progress =>
            {
                _shadow.localScale = new Vector3(settings.FireWorksFlightScaleCurveX.Evaluate(progress),
                    settings.FireWorksFlightScaleCurveY.Evaluate(progress),
                    settings.FireWorksFlightScaleCurveZ.Evaluate(progress));
            }, 1f, duration);

            spineDrivenPath.Completed -= CompleteAction;
            spineDrivenPath.Completed += CompleteAction;

            shadowSpinePath.Completed -= CompleteShadowAction;
            shadowSpinePath.Completed += CompleteShadowAction;

            return;

            void CompleteShadowAction()
            {
                shadowFollowingTransform.Finish();
            }

            void CompleteAction()
            {
                followingTransform.Finish();
                _callback?.SafeInvoke();
                _callback = null;

                foreach (var image in _imagesToEnableDisable)
                {
                    image.enabled = false;
                }

                Rx.Invoke(settings.ReleaseFireWorksRocketTargetFx, _ =>
                {
                    gameObject.Release();
                });

                _tweener?.Kill();
            }
        }

        public override void OnRelease()
        {
            _tweener?.Kill();
            _tweener = null;

            base.OnRelease();
        }
    }
}