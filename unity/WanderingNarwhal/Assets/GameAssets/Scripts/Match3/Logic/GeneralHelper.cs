using System;
using System.Collections.Generic;
using BBB.Match3.Systems;

namespace BBB.Match3.Logic
{
    public static class GeneralHelper
    {
        public static string ToString<T>(this T value) where T : struct
        {
            return Enum.GetName(typeof(T), value);
        }

        public static T DeterministicRandomInSelf<T>(this ICollection<T> iEnumerable)
        {
            var count = iEnumerable.Count;
            if (count == 0) return default;
            var randomValue = RandomSystem.Next(count);
            var index = 0;

            foreach (var element in iEnumerable)
            {
                if (index == randomValue)
                {
                    return element;
                }
                index++;
            }

            return default;
        }
        
        public static void DeterministicShuffle<T>(this IList<T> list)
        {
            for (var i = 0; i < list.Count; i++)
            {
                var anotherId = (int)RandomSystem.Range(0f, list.Count);
                (list[anotherId], list[i]) = (list[i], list[anotherId]);
            }
        }
        
        public static void DeterministicShuffle<T>(this T[] array, int from, int to)
        {
            for (var i = from; i < to; i++)
            {
                var anotherId = (int)RandomSystem.Range(0f, to - from);
                (array[anotherId], array[i]) = (array[i], array[anotherId]);
            }
        }

        public static bool NotNullOrEmpty<T>(this ICollection<T> iEnumerable)
        {
            return iEnumerable is { Count: > 0 };
        }

        public static string Paint(this string systemName)
        {
            return systemName switch
            {
                "[RandomSystem]" => systemName.Paint("cyan"),
                "[GravitySystem]" => systemName.Paint("olive"),
                "[GravitySystem.Swap]" => systemName.Paint("#878768ff"), // paleOlive
                "[GridController]" => systemName.Paint("lime"),
                "[RestrictSettledTilesSystem]" => systemName.Paint("magenta"),
                "[AssistSystem.UndefinedTiles]" => systemName.Paint("teal"),
                "[AutoBruteSystem]" => systemName.Paint("maroon"),
                "[SpecialTileSystem]" => systemName.Paint("blue"),
                "[PlaySimulationSystem]" => systemName.Paint("yellow"),
                "[RestrictCellsForSpecificSpawnerSystem]" => systemName.Paint("green"),
                _ => systemName
            };
        }

        public static string Paint(this string systemName, string colorName)
        {
            return "<color=" + colorName + ">" + systemName + "</color>";
        }
    }
}
