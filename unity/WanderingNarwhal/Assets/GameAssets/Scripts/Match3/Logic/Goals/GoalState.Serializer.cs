using System.Collections.Generic;
using Newtonsoft.Json;

namespace GameAssets.Scripts.Match3.Logic
{
    public partial class GoalState
    {
        public static class Serlializer
        {
            public static string To<PERSON><PERSON>(GoalState instance)
            {
                var dict = instance._state;
                return JsonConvert.SerializeObject(dict);
            }

            public static GoalState FromJson(string jsonString)
            {
                var result = new GoalState();
                result._state = JsonConvert.DeserializeObject<Dictionary<GoalType, int>>(jsonString);
                return result;
            }
        }
    }
}