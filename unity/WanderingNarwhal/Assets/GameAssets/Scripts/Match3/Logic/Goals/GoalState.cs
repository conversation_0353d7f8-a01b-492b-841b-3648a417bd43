using System.Collections;
using System.Collections.Generic;
using System.Text;
using BBB;
using BBB.CellTypes;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3;
using BBB.Match3.Systems;
using PBGame;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Logic
{
    public partial class GoalState : IEnumerable<KeyValuePair<GoalType, int>>
    {
        private Dictionary<GoalType, int> _state = new ();

        private static readonly List<GoalType> _goalsTemp = new (25);

        private static readonly Dictionary<GoalType, int> _countsListTemp = new Dictionary<GoalType, int>(10);

        private static readonly List<Cell> _cellTemp = new List<Cell>();

        public GoalState()
        {
        }

        public GoalState(GoalResultState goalResultState)
        {
            if (goalResultState.GoalCountsMap == null)
            {
                BDebug.LogError(LogCat.Match3, "Goal count map not found in Goal Result State");
                return;
            }

            foreach (var kvp in goalResultState.GoalCountsMap)
            {
                if (kvp.Value <= 0)
                {
                    BDebug.LogError(LogCat.Match3, $"GoalResultState -> GoalState: all values should be >0 or missing in the dict. key={kvp.Key}, {kvp.Value}");
                    continue;
                }

                var goalType = (GoalType)kvp.Key;
                if (goalType == GoalType.Score)
                {
                    BDebug.LogError(LogCat.Match3, $"GoalResultState -> GoalState: GoalType.Score is not expected.");
                    continue;
                }
                _state.Add(goalType, kvp.Value);
            }

            RemoveNonPositiveValues();
        }

        public int GetRevealStepsCount()
        {
            var result = 0;
            foreach (var (key, value) in _state)
            {
                switch (key)
                {
                    // Drop items goals should trigger reveal step every time each tile is collected,
                    // unlike other goals that should do it only after completion. -VK
                    case GoalType.DropItems:
                        result += value;
                        break;
                    case GoalType.Bush:
                    case GoalType.FlowerPot:
                        if (value > 0)
                        { 
                            result += 1;
                            //If there is an explicit background or petal goal it will be added automatically via default calculation - YA  
                            if(!_state.ContainsKey(GoalType.Petal) || !_state.ContainsKey(GoalType.Backgrounds))
                                result += 1;
                        }
                        break;
                    default:
                        result += value > 0 ? 1 : 0;
                        break;
                }
            }
            return result;
        }

        public GoalResultState ToGoalResultState()
        {
            var result = new GoalResultState
            {
                GoalCountsMap = new Dictionary<int, int>()
            };

            foreach (var kv in _state)
            {
                result.GoalCountsMap[(int)kv.Key] = kv.Value;
            }

            return result;
        }

        public int NonZeroTypesCount()
        {
            int result = 0;
            foreach (var kvp in _state)
            {
                if (kvp.Value > 0)
                {
                    result++;
                }
            }

            return result;
        }

        public bool DoesGoalExist(GoalType goalType)
        {
            return _state.ContainsKey(goalType);
        }
        public void AddNewGoal(GoalType goalType, int value)
        {
            if (!_state.ContainsKey(goalType))
            {
                _state.Add(goalType, value);
            }
        }

        public GoalType GetHighestValueGoal(GoalState originalGoals)
        {
            GoalType result = GoalType.None;
            float maxValue = 0f;
            foreach (var kv in _state)
            {
                var value = kv.Value / (float)originalGoals.GetGoalValue(kv.Key);
                if (value > maxValue)
                {
                    maxValue = value;
                    result = kv.Key;
                }
            }

            return result;
        }

        public int GetGoalValue(GoalType goalType)
        {
            if (_state.TryGetValue(goalType, out int value))
            {
                return value;
            }

            return 0;
        }

        public void SetGoalValue(GoalType goalType, int value)
        {
            if (goalType.IsGridBased())
            {
                BDebug.LogError(LogCat.Match3, "Can not set grid based goals directly");
            }

            if (value > 0)
                _state[goalType] = value;
            else
                _state.Remove(goalType);
        }

        public int Reduce(GoalType goalType, int reduceOn)
        {
            if (_state.TryGetValue(goalType, out var value))
            {
                var prevValue = value;
                value -= reduceOn;
                if (value <= 0)
                {
                    value = 0;
                    _state.Remove(goalType);
                }
                else
                    _state[goalType] = value;

                return prevValue - value;
            }

            return 0;
        }

        public bool AnyGoalsLeft()
        {
            foreach (var kvp in _state)
            {
                if (kvp.Value > 0)
                {
                    return true;
                }
            }
            return false;
        }

        public void RemoveGoalsExceptScore()
        {
            _state.TryGetValue(GoalType.Score, out var score);
            _state.Clear();
            if (score > 0)
            {
                _state.Add(GoalType.Score, score);
            }
        }

        public void RemoveAllGoals()
        {
            _state.Clear();
        }

        public GoalState Clone()
        {
            var result = new GoalState();
            foreach (var kvp in _state)
                result._state.Add(kvp.Key, kvp.Value);
            return result;
        }

        public static GoalState operator +(GoalState first, GoalState second)
        {
            var result = first.Clone();
            foreach (var kvp in second)
            {
                if (result._state.TryGetValue(kvp.Key, out var value))
                {
                    result._state[kvp.Key] = value + kvp.Value;
                }
                else
                {
                    result._state[kvp.Key] = kvp.Value;
                }
            }

            return result;
        }

        public static GoalState operator -(GoalState first, GoalState second)
        {
            var result = new GoalState();

            foreach (var kvp in first._state)
            {
                var key = kvp.Key;
                var firstValue = kvp.Value;

                if (second._state.TryGetValue(key, out var secondValue))
                {
                    var resultValue = firstValue - secondValue;
                    if (resultValue > 0)
                        result._state.Add(key, resultValue);
                }
                else if (firstValue > 0)
                {
                    result._state.Add(key, firstValue);
                }
            }

            return result;
        }

        public void Multiply(float value)
        {
            if (value < 0f)
            {
                BDebug.LogError(LogCat.Match3, $"Negative multiplier {value} is not allowed for goals");
                return;
            }

            if (value < 0.01f)
            {
                _state.Clear();
                return;
            }

            _countsListTemp.Clear();

            foreach (var kvp in _state)
            {
                var multValue = value * kvp.Value;
                var intValue = multValue >= 1f ? Mathf.RoundToInt(multValue) : 1;
                _countsListTemp[kvp.Key] = intValue;
            }

            _state.Clear();
            foreach (var kv in _countsListTemp)
            {
                _state.Add(kv.Key, kv.Value);
            }
        }

        public void RemoveNonPositiveValues()
        {
            lock (_goalsTemp)
            {
                _goalsTemp.Clear();

                foreach (var kvp in _state)
                {
                    if (kvp.Value <= 0)
                        _goalsTemp.Add(kvp.Key);
                }

                if (_goalsTemp.Count > 0)
                {
                    foreach (var key in _goalsTemp)
                        _state.Remove(key);
                }
            }
        }

        /// <summary>
        /// Setup non-automatic level goals data.
        /// </summary>
        /// <param name="usedKinds">Used tile kinds in level, which is used to filter incorrect tile kinds goals.</param>
        /// <param name="remoteGoalData">Preselected goals and target counts.</param>
        /// <remarks>
        /// This will setup non-automatic level goals from remote level config.
        /// Non-automatic goal is any goal, which doesn't require automatic recalculation of targets on grid.
        /// Any goal can be non-automatic if it specified in config dict, and any goal will be automatic, if it's specified as OptionalGoal.
        /// Also any automatic goal will be forced to become non-automatic in case if target tile is assigned to spawner and it can be spawned on grid
        /// (this is required because automatic goal can only count existing tiles on grid at start of level, not future possible tiles in spawners).
        /// </remarks>
        public void SetupRemoteData(Grid grid, List<TileKinds> usedKinds, LevelRemoteGoalData remoteGoalData)
        {
            // SK: Score goal is currently not allowed anymore
            //_state[GoalType.Score] = remoteGoalData.Score;

            if (remoteGoalData.TileKindGoals != null)
                foreach (var kvp in remoteGoalData.TileKindGoals)
                {
                    // TileKindGoals may contains tile kind goals and also any other goal with predefined amount of target goal count.
                    var goal = kvp.Key;
                    bool isTileKindType = (goal & GoalType.TileKindTypes) != 0;
                    if (!isTileKindType)
                    {
                        _state[kvp.Key] = kvp.Value <= 0 ? CalculateGoalTargetCountFromCurrentGrid(grid, kvp.Key) : kvp.Value;
                    }
                    else
                    if (usedKinds.Contains(kvp.Key.ToTileKind()))
                    {
                        _state[kvp.Key] = kvp.Value <= 0 ? CalculateGoalTargetCountFromCurrentGrid(grid, kvp.Key) : kvp.Value;
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Match3,$"<color=red>Goal for level {remoteGoalData.Uid} of type '{kvp.Key}' found in goals, but not found in used kinds.</color>");
                    }
                }

            RemoveNonPositiveValues();
        }

        public int CalculateGoalTargetCountFromCurrentGrid(Grid grid, GoalType goal)
        {
            return DebugCalculateExpectedGoalTargetCountFromCurrentGrid(grid, goal, 0);
        }

        public int DebugCalculateExpectedGoalTargetCountFromCurrentGrid(Grid grid, GoalType goal, int targetValue)
        {
            int count = 0;
            bool beeHiveFound = false;
            bool magicHatFound = false;
            bool toadFound = false;
            foreach (var cell in grid.Cells)
            {
                if (goal == GoalType.Sheep)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.SheepMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    }
                }
                else if (goal == GoalType.Chicken)
                {
                    if (!cell.Tile.IsNull())
                    {
                        if (cell.Tile.IsAnyOf(TileState.ChickenMod))
                        {
                            count++;
                        }
                        else if (cell.Tile.IsAnyOf(TileState.HenMod))
                        {
                            count += 3;
                        }
                    }
                }
                else if (goal == GoalType.Banana)
                {
                    if (!cell.Tile.IsNull())
                    {
                        if (cell.Tile.IsAnyOf(TileState.BananaMod))
                        {
                            count++;
                        }
                        else if (cell.Tile.IsAnyOf(TileState.MonkeyMod | TileState.BigMonkeyMod))
                        {
                            count += cell.Tile.GetParam(TileParamEnum.RestoresCount);
                        }
                    }
                }
                else if (goal == GoalType.Bee)
                {
                    if (!cell.Tile.IsNull())
                    {
                        if (cell.Tile.IsAnyOf(TileState.BeeMod))
                        {
                            count++;
                        }

                        if (cell.Tile.IsAnyOf(TileState.HiveMod))
                        {
                            beeHiveFound = true;
                        }
                    }
                }
                else if (goal == GoalType.Squid)
                {
                    if (!cell.Tile.IsNull())
                    {
                        if (cell.Tile.HasParam(TileParamEnum.SquidsState))
                        {
                            // When squid has adjacent hp then it is single in aquarium tile, and will collect goal only once upon final tile death.
                            bool isSingle = cell.Tile.GetParam(TileParamEnum.AdjacentHp) > 0;
                            if (isSingle)
                            {
                                count++;
                            }
                            else
                            {
                                var sizeX = cell.Tile.GetParam(TileParamEnum.SizeX);
                                var sizeY = cell.Tile.GetParam(TileParamEnum.SizeY);
                                count += sizeX * sizeY;
                            }
                        }
                    }
                }
                else if (goal == GoalType.Toad)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.ToadMod))
                    {
                        toadFound = true;
                    }
                }
                else if (goal == GoalType.MagicHat)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.MagicHatMod))
                    {
                        magicHatFound = true;
                    }
                }
                else if (goal == GoalType.BowlingPin)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.BowlingMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    }
                }
                else if (goal == GoalType.Bush)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.BushMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.GiantPinata)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.GiantPinataMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.Safe)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.SafeMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.FlowerPot)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.FlowerPotMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.SodaBottle)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.SodaMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    }
                }
                else if (goal == GoalType.DynamiteStick)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.DynamiteBoxMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    }
                }
                else if (goal == GoalType.IceBar)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.IceBarMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.MetalBar)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.MetalBarMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.Shelf)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.ShelfMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.JellyFish)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.JellyFishMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    }
                }
                else if (goal == GoalType.GoldenScarab)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.GoldenScarabMod))
                    {
                        count += cell.Tile.GetParam(TileParamEnum.GoldenScarabCount);
                    }
                }
                else if (goal == GoalType.Gondola)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.GondolaMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.FireWorks)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.FireWorksMod))
                    {
                        count += 1;
                    }
                }
                else if (goal == GoalType.SlotMachine)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.SlotMachineMod))
                    {
                        count += 1;
                    }
                }
                else if (IsCellContainsGridBasedGoalRelatedItem(cell, goal))
                {
                    count++;
                }
            }

            if (goal == GoalType.Bee)
            {
                if (count < targetValue && beeHiveFound)
                {
                    count = targetValue;
                }
            }
            if (goal == GoalType.MagicHat)
            {
                if (count < targetValue && magicHatFound)
                {
                    count = targetValue;
                }
            }
            if (goal == GoalType.Toad)
            {
                if (count < targetValue && toadFound)
                {
                    count = targetValue;
                }
            }

            return count;
        }

        /// <summary>
        /// Check if target goal have have no manually specified target count, and generate value from current grid if required.
        /// </summary>
        public void InitializeTargetGoalCountsIfNotManuallySpecified(Grid grid, string levelUid, GoalType automaticGoals, SpawnerSettings[] globalSpawnSettings)
        {
            if (automaticGoals != GoalType.None)
            {
                foreach (var goal in GoalTypeExtensions.GridBasedTypes())
                {
                    if ((automaticGoals & goal) != 0 && !_state.ContainsKey(goal))
                    {
                        _state[goal] = 0;
                    }
                }
            }

            _goalsTemp.Clear();

            foreach (var goalItem in _state)
            {
                if (goalItem.Value <= 0)
                {
                    _goalsTemp.Add(goalItem.Key);
                }
            }

            foreach (var goal in _goalsTemp)
            {
                // Count targets count on grid as it is at the moment of level start.
                var count = CalculateGoalTargetCountFromCurrentGrid(grid, goal);
                if (count <= 0 && goal != GoalType.AssistRedefinedTypes && goal != GoalType.OnCellGoalType && goal != GoalType.AdjacentGoalTypes)
                {
                    if (goal != GoalType.DropItems)
                    {
                        BDebug.LogError(LogCat.Match3,$"Failed to initialize starting goals count on level '{levelUid}' for automatic goal: {goal}");
                    }
                    else
                    {
                        BDebug.Log(LogCat.Match3,$"Drop item count on initial grid is zero (are dropitems spawners used on this level?), level:{levelUid}");
                    }
                }

                // Set constant goal count for goal, which didn't have manually specified target constant in config.
                _state[goal] = count;
            }
        }

        /// <summary>
        /// Check if grid contains enough items for specified goal, and if not, check that grid has at least one spawner for this item.
        /// If all checks failed, reset goal target count to value, which is equal to existing goal items on current grid.
        /// </summary>
        /// <remarks>
        /// For example, if level contains goal Watermelons 71, but grid has only 30 watermelons and no spawners for watermelons placed on grid,
        /// then this method will reset target goal count to 30.
        /// Or if there are no goal items on grid, then target count will be reset to 0.
        /// </remarks>
        public void ResetGoalsWithMissingSpawnersIfNeeded(Grid grid, string debugLevelUid, SpawnerSettings[] globalSpawners)
        {
            var spawnersCache = _cellTemp;
            spawnersCache.Clear();
            if (grid == null)
            {
                return;
            }

            if (globalSpawners == null)
            {
                BDebug.LogError(LogCat.Match3,"Global spawners not assigned");
                return;
            }

            if (globalSpawners.Length == 0)
            {
                BDebug.LogError(LogCat.Match3,"Global spawners are empty");
                return;
            }

            var goalsToAddSpawnersFor = _goalsTemp;
            goalsToAddSpawnersFor.Clear();

            foreach (var goalItem in _state)
            {
                var goal = goalItem.Key;
                if (goal.IsSpawnable() && !goal.IsTileKind())
                {
                    int existingCount = 0;
                    foreach (var cell in grid.Cells)
                    {
                        if (IsCellContainsGridBasedGoalRelatedItem(cell, goal))
                        {
                            existingCount++;
                        }
                    }

                    if (existingCount < goalItem.Value)
                    {
                        if (spawnersCache.Count == 0)
                        {
                            foreach (var cell in grid.Cells)
                            {
                                if (cell.IsAnyOf(CellState.Spawner))
                                {
                                    spawnersCache.Add(cell);
                                }
                            }
                        }

                        bool isSpawnedOnGrid = false;

                        foreach (var spawnerCell in spawnersCache)
                        {
                            var spawnerSetting = SpawnerSettings.FindSpawnerByUid(globalSpawners, spawnerCell.SpawnerUid);
                            if (spawnerSetting == null)
                            {
                                BDebug.LogError(LogCat.Match3,$"M3: Found spawner on grid with non-existent spawner uid {spawnerCell.SpawnerUid}, max allowed uid = {globalSpawners.Length}");
                                spawnerSetting = SpawnerSettings.FindSpawnerByUid(globalSpawners, 0);
                            }

                            if (spawnerSetting != null)
                            {
                                if (spawnerSetting.IsSpawningAnyTileRelatedToGoal(goal))
                                {
                                    isSpawnedOnGrid = true;
                                    break;
                                }
                            }
                        }

                        if (!isSpawnedOnGrid)
                        {
                            if (!goalsToAddSpawnersFor.Contains(goal))
                            {
                                goalsToAddSpawnersFor.Add(goal);
                            }
                        }
                    }
                }

                spawnersCache.Clear();
            }

            if (goalsToAddSpawnersFor.Count > 0)
            {
                foreach (var missingGoalSpawn in goalsToAddSpawnersFor)
                {
                    var originalCunt = _state[missingGoalSpawn];
                    _state[missingGoalSpawn] = CalculateGoalTargetCountFromCurrentGrid(grid, missingGoalSpawn);
                    BDebug.LogError(LogCat.Match3,
                        $"Level '{debugLevelUid}' doesn't contain required spawners for goal {missingGoalSpawn}.\nReset goal target count from '{originalCunt}' to '{_state[missingGoalSpawn]}' to make it not require spawner");
                }
            }
        }

        public static bool HasCellRelatedToGoal(Grid grid, GoalType goal)
        {
            foreach (var cell in grid.Cells)
            {
                if (IsCellContainsGridBasedGoalRelatedItem(cell, goal))
                {
                    return true;
                }
            }

            return false;
        }

        public int CountCellsRelatedToGoal(Grid grid, GoalType goal)
        {
            return DebugCalculateExpectedGoalTargetCountFromCurrentGrid(grid, goal, 0);
        }

        // TODO: Make sure all goals are represented
        public static void GetAffectedGoalCoordsForCell(Grid grid, Cell cell, GoalType goalType,
            HashSet<Coords> affectedCoords)
        {
            if (cell == null || goalType == GoalType.AdjacentGoalTypes || goalType == GoalType.AssistRedefinedTypes) return;

            if ((goalType & GoalType.AdjacentGoalTypes) != 0)
            {
                foreach (var dir in CardinalDirectionsHelper.GetAllStraight())
                {
                    foreach (var referencedCell in cell.ReferencedCells)
                    {
                        var coords = referencedCell.Coords.GoSingleCardinalDirection(dir);
                        if (!grid.IsOutsideGridBounds(coords.X, coords.Y))
                        {
                            affectedCoords.Add(coords);
                        }
                    }
                }

                foreach (var referencedCell in cell.ReferencedCells)
                {
                    affectedCoords.Remove(referencedCell.Coords);
                }
            }
            else if ((goalType & GoalType.OnCellGoalType) != 0)
            {
                affectedCoords.Add(cell.Coords);
            }

        }

        /// <summary>
        /// Check if cell contains state or tile, which is related to specific grid-based goal.
        /// </summary>
        /// <remarks>
        /// Important note: this is used to check relation of cell to the goal, but it can't be used to collect goals,
        /// because some tiles may be related to goal, but can't be collected for this goal.
        /// example: egg tile is related to bird goal, but egg tile can't be collected to bird goal. Egg only spawns bird tile when destroyed.
        /// </remarks>
        public static bool IsCellContainsGridBasedGoalRelatedItem(Cell cell, GoalType goal)
        {
            if (cell == null) return false;
            switch (goal)
            {
                case GoalType.Backgrounds:
                    return cell.BackgroundCount > 0 && cell.IsBackState();
                case GoalType.Ivy:
                    return cell.IvyCount > 0 && cell.IsAnyOf(CellState.Ivy);
                case GoalType.Petal:
                    return cell.BackgroundCount > 0 && cell.IsPetal();
                case GoalType.Tnt:
                    return cell.IsAnyOf(CellState.Tnt) && cell.TntCount > 0;
                default:
                    return IsTileGridBasedGoalRelatedItem(cell.Tile, goal);
            }
        }

        public static bool IsTileGridBasedGoalRelatedItem(Tile tile, GoalType goal)
        {
            if (tile == null) return false;
            return IsTileGridBasedGoalRelatedItem(tile.Speciality, tile.State, tile.Kind, goal);
        }

        public static bool IsTileGridBasedGoalRelatedItem(TileSpeciality tileSpeciality, TileState tileState, TileKinds tileKind, GoalType goal)
        {
            switch (goal)
            {
                case GoalType.DropItems:
                    return tileSpeciality == TileSpeciality.DropItem;
                case GoalType.Stickers:
                    return tileSpeciality == TileSpeciality.Sticker;
                case GoalType.Litters:
                    return tileSpeciality == TileSpeciality.Litter;
                case GoalType.Pinata:
                    return tileSpeciality == TileSpeciality.Pinata;
                case GoalType.ColorCrate:
                    return tileSpeciality == TileSpeciality.ColorCrate;
                case GoalType.Watermelon:
                    return tileSpeciality == TileSpeciality.Watermelon;
                case GoalType.Toad:
                    return tileSpeciality == TileSpeciality.Toad;
                case GoalType.Bush:
                    return tileSpeciality == TileSpeciality.Bush;
                case GoalType.IceBar:
                    return tileSpeciality == TileSpeciality.IceBar;
                case GoalType.MagicHat:
                    return tileSpeciality == TileSpeciality.MagicHat;
                case GoalType.Safe:
                    return tileSpeciality == TileSpeciality.Safe;
                case GoalType.GiantPinata:
                    return tileSpeciality == TileSpeciality.GiantPinata;
                case GoalType.MetalBar:
                    return tileSpeciality == TileSpeciality.MetalBar;
                case GoalType.Egg:
                case GoalType.Bird:
                    return tileSpeciality == TileSpeciality.Egg || tileSpeciality == TileSpeciality.Bird;
                case GoalType.FlowerPot:
                    return tileSpeciality == TileSpeciality.FlowerPot;
                case GoalType.Gondola:
                    return tileSpeciality == TileSpeciality.Gondola;
                case GoalType.TukTuk:
                    return tileSpeciality == TileSpeciality.TukTuk;
                case GoalType.Stone:
                    return tileSpeciality == TileSpeciality.Stone;
                case GoalType.Pouch:
                    return tileSpeciality == TileSpeciality.Pouch;
                case GoalType.IceCubes:
                case GoalType.Chains:
                case GoalType.Animal:
                case GoalType.Sand:
                case GoalType.Vase:
                case GoalType.MoneyBag:
                case GoalType.Penguin:
                case GoalType.Banana:
                case GoalType.Sheep:
                case GoalType.Skunk:
                case GoalType.Chicken:
                case GoalType.Bee:
                case GoalType.Mole:
                case GoalType.Squid:
                case GoalType.GameEventScore:
                case GoalType.StealingHatScore:
                case GoalType.BowlingPin:
                case GoalType.SodaBottle:
                case GoalType.DynamiteStick:   
                case GoalType.Shelf:
                case GoalType.JellyFish:
                case GoalType.FireWorks:
                case GoalType.SlotMachine:
                    return IsModStateRelatedToGoal(tileState, goal);
                default:
                    if (tileSpeciality != TileSpeciality.ColorCrate)
                    {
                        switch (goal)
                        {
                            case GoalType.Green:
                                return tileKind == TileKinds.Green;
                            case GoalType.Yellow:
                                return tileKind == TileKinds.Yellow;
                            case GoalType.Blue:
                                return tileKind == TileKinds.Blue;
                            case GoalType.Red:
                                return tileKind == TileKinds.Red;
                            case GoalType.Purple:
                                return tileKind == TileKinds.Purple;
                            case GoalType.Orange:
                                return tileKind == TileKinds.Orange;
                            case GoalType.White:
                                return tileKind == TileKinds.White;
                        }
                    }

                    break;
            }

            return false;
        }

        public static bool IsModStateRelatedToGoal(TileState state, GoalType goal)
        {
            switch (goal)
            {
                case GoalType.IceCubes:
                    return (state & TileState.IceCubeMod) != 0;
                case GoalType.Chains:
                    return (state & TileState.ChainMod) != 0;
                case GoalType.Animal:
                    return (state & TileState.AnimalMod) != 0;
                case GoalType.Sand:
                    return (state & TileState.SandMod) != 0;
                case GoalType.ColorCrate:
                    return (state & TileState.ColorCrateMod) != 0;
                case GoalType.Watermelon:
                    return (state & TileState.WatermelonMod) != 0;
                case GoalType.Vase:
                    return (state & TileState.VaseMod) != 0;
                case GoalType.MoneyBag:
                    return (state & TileState.MoneyBagMod) != 0;
                case GoalType.Penguin:
                    return (state & TileState.PenguinMod) != 0;
                case GoalType.Egg:
                    return (state & TileState.EggMod) != 0;
                case GoalType.Bird:
                    return (state & TileState.BirdMod) != 0;
                case GoalType.Banana:
                    return (state & TileState.BananaMod) != 0;
                case GoalType.Sheep:
                    return (state & TileState.SheepMod) != 0;
                case GoalType.Skunk:
                    return (state & TileState.SkunkMod) != 0;
                case GoalType.GameEventScore:
                    return (state & TileState.GameEventLabel) != 0;
                case GoalType.StealingHatScore:
                    return (state & TileState.StealingHatLabel) != 0;
                case GoalType.Chicken:
                    return (state & (TileState.HenMod | TileState.ChickenMod)) != 0;
                case GoalType.Bee:
                    return (state & (TileState.HiveMod | TileState.BeeMod)) != 0;
                case GoalType.Mole:
                    return (state & (TileState.MoleMod)) != 0;
                case GoalType.Squid:
                    return (state & (TileState.SquidMod)) != 0;
                case GoalType.Toad:
                    return (state & (TileState.ToadMod)) != 0;
                case GoalType.MagicHat:
                    return (state & (TileState.MagicHatMod)) != 0;
                case GoalType.BowlingPin:
                    return (state & TileState.BowlingMod) != 0;
                case GoalType.Bush:
                    return (state & TileState.BushMod) != 0;
                case GoalType.SodaBottle:
                    return (state & TileState.SodaMod) != 0;
                case GoalType.Safe:
                    return (state & TileState.SafeMod) != 0;
                case GoalType.FlowerPot:
                    return (state & TileState.FlowerPotMod) != 0;
                case GoalType.IceBar:
                    return (state & TileState.IceBarMod) != 0;
                case GoalType.DynamiteStick:
                    return (state & TileState.DynamiteBoxMod) != 0;
                case GoalType.GiantPinata:
                    return (state & TileState.GiantPinataMod) != 0;
                case GoalType.MetalBar:
                    return (state & TileState.MetalBarMod) != 0;
                case GoalType.Shelf:
                    return (state & TileState.ShelfMod) != 0;
                case GoalType.JellyFish:
                    return (state & TileState.JellyFishMod) != 0;
                case GoalType.Gondola:
                    return (state & TileState.GondolaMod) != 0;
                case GoalType.TukTuk:
                    return (state & TileState.TukTukMod) != 0;
                case GoalType.FireWorks:
                    return (state & TileState.FireWorksMod) != 0;
                case GoalType.SlotMachine:
                    return (state & TileState.SlotMachineMod) != 0;
                case GoalType.Stone:
                    return (state & TileState.StoneMod) != 0;
                case GoalType.Pouch:
                    return (state & TileState.PouchMod) != 0;
            }
            return false;
        }

        public IEnumerator<KeyValuePair<GoalType, int>> GetEnumerator()
        {
            return _state.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public override string ToString()
        {
            var builder = new StringBuilder();

            foreach (var kvp in _state)
            {
                const string equals = "=";
                const string comma = ", ";
                
                builder.Append(kvp.Key.ToShortString())
                    .Append(equals)
                    .Append(kvp.Value)
                    .Append(comma);
            }

            if (builder.Length > 2)
            {
                builder.Length -= 2;
            }

            return builder.ToString();
        }

        public IDictionary<string, int> ToNameDict()
        {
            var result = new Dictionary<string, int>();
            foreach (var kvp in _state)
            {
                result.Add(kvp.Key.ToNameString(), kvp.Value);
            }

            return result;
        }
        
        public Dictionary<GoalType, int> ToDictionary()
        {
            return _state;
        }

        public GoalStateDto ToDto()
        {
            var dto = new GoalStateDto()
            {
                state = new List<GoalRecord>()
            };

            foreach (var kv in _state)
            {
                dto.state.Add(new GoalRecord(kv.Key, kv.Value));
            }

            return dto;
        }

        public void FromDto(GoalStateDto dto)
        {
            _state.Clear();
            if (dto == null) return;
            if (dto.state == null) return;
            foreach (var item in dto.state)
            {
                _state[(GoalType)item.goalType] = item.value;
            }
        }
    }
}
