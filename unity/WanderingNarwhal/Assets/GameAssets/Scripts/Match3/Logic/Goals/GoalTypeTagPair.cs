namespace GameAssets.Scripts.Match3.Logic
{
    public struct GoalTypeTagPair
    {
        public readonly GoalType GoalType;
        public readonly string GoalTag; //for example used in the context of game events to hold a game event id

        public GoalTypeTagPair(GoalType goalType)
        {
            GoalType = goalType;
            GoalTag = null;
        }
        
        public GoalTypeTagPair(GoalType goalType, string goalTag)
        {
            GoalType = goalType;
            GoalTag = goalTag;
        }
    }
}