using BBB;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic
{
    public class WinValueDeterminer
    {
        public float GetWinValue(AssistState progress, AssistState original)
        {
            progress.RemoveNonPositiveValues();
            original.RemoveNonPositiveValues();

            var goalContributionSum = 0f;
            var weightSum = 0f;
            
            foreach (var kvp in original)
            {
                var progressValue = progress.GetValue(kvp.Key);
                var originalValue = kvp.Value;

                goalContributionSum += progressValue / originalValue;
                weightSum++;
            }

            if (weightSum > 0)
            {
                return goalContributionSum / weightSum;
            }

            BDebug.LogError(LogCat.Match3, "Weight sum is 0, which is not supposed to happen");
            return 1f;
        }

    }
}