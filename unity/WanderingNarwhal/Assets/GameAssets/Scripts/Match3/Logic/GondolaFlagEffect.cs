using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class GondolaFlagEffect : ConfigurableEffectBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _animationStartName = "intro";

        public override void OnSpawn()
        {
            base.OnSpawn();
            _sk.AnimationState.SetAnimation(0, _animationStartName, loop: false);
        }
    }
}