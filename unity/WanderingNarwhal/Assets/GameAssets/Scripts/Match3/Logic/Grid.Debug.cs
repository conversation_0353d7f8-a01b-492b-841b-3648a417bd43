using System;
using System.Collections.Generic;
using BBB.Match3;
using UnityEngine;

namespace BBB
{
    public sealed partial class Grid
    {
        [Serializable]
        public sealed class GridDto
        {
            public List<CellDto> CellDtos;
            public int tilesSpawnedCount;
        }

        public static bool IsRouteBlocked(Grid grid, Coords source, Coords target)
        {
            if (source.X == target.X)
            {
                for (int y = source.Y; y > target.Y; y--)
                {
                    var currentCoord = new Coords(source.X, y);

                    var nextCoord = currentCoord - new Coords(0, 1);
                    if (grid.HasWallBetween(currentCoord, nextCoord))
                        return true;

                    if (grid.TryGetCell(nextCoord, out var cell) && cell.HasTile() &&
                        cell.Tile.IsAnyOf(TileState.HiveMod))
                        return true;
                }
            }
            else if (Mathf.Abs(source.X - target.X) == 1)
            {
                bool allHorizontalBlocked = true;
                for (int y = source.Y; y > target.Y; y--)
                {
                    var currentCoord = new Coords(source.X, y);
                    var nextCoord = currentCoord - new Coords(0, 1);
                    var verticalWallBetween = grid.HasWallBetween(currentCoord, nextCoord); 

                    bool horizontalWallBetween = grid.HasWallBetween(new Coords(source.X, y), new Coords(target.X, y));

                    if (horizontalWallBetween && verticalWallBetween)
                        return true;

                    allHorizontalBlocked &= horizontalWallBetween;
                }

                return allHorizontalBlocked;
            }

            return false;
        }

        /// <summary>
        /// Convert grid state into data transfer object.
        /// </summary>
        /// <remarks>
        /// Used in cases when we need to serialize current state of grid (for example, for integration tests snapshots).
        /// </remarks>
        public GridDto ToDto()
        {
            var dto = new GridDto();
            dto.CellDtos = new List<CellDto>();
            foreach (var cell in Cells)
            {
                var cellDto = cell.ToDto();
                dto.CellDtos.Add(cellDto);
            }

            dto.tilesSpawnedCount = this.TilesSpawnedCount;
            return dto;
        }

        public void FromDto(GridDto dto)
        {
            Cells.Clear();

            if (dto == null) return;
            if (dto.CellDtos == null) return;

            Cells.Clear();
            int maxX = 0;
            int maxY = 0;
            foreach (var cellDto in dto.CellDtos)
            {
                var coords = new Coords(cellDto.X, cellDto.Y);
                var cell = new Cell(coords);
                maxX = Mathf.Max(maxX, coords.X);
                maxY = Mathf.Max(maxY, coords.Y); 
                cell.FromDto(cellDto);
                Cells.Add(cell);
            }

            _coordToCells = new Cell[maxX + 1, maxY + 1];
            Width = maxX + 1;
            Height = maxY + 1;
            foreach (var cell in Cells)
            {
                _coordToCells[cell.Coords.X, cell.Coords.Y] = cell;
            }

            this.TilesSpawnedCount = dto.tilesSpawnedCount;
        }
    }
}
