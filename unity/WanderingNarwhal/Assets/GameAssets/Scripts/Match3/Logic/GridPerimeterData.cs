using System.Collections.Generic;

namespace BBB
{
    public enum EdgeQuarterType
    {
        Corner = 0,
        Recess = 1,
        Face = 2
    }

    public class EdgeQuarterState
    {
        public EdgeQuarterType QuarterType { get; private set; }
        public float Angle { get; private set; }

        public EdgeQuarterState(EdgeQuarterType type, float angle)
        {
            QuarterType = type;
            Angle = angle;
        }
    }

    public class Edge
    {
        public Coords Coords { get; private set; }
        /*
            order:
            0:bl 1:tl
            2:tr 3:br
         */

        private readonly EdgeQuarterState[] _states = new EdgeQuarterState[4];

        public EdgeQuarterState[] States
        {
            get { return _states; }
        }

        public Edge(Coords coords)
        {
            Coords = coords;
        }

        public void SetQuarterState(int index, EdgeQuarterState state)
        {
            _states[index] = state;
        }
    }

    public class GridPerimeterData
    {
        private readonly List<Edge> _edges = new();

        public IEnumerable<Edge> Edges => _edges;

        public GridPerimeterData(Grid grid)
        {
            var neighbourExistenceFlags = new bool[8];

            var maxX = int.MinValue;
            var maxY = int.MinValue;

            foreach (var cell in grid.Cells)
            {
                if (cell.Coords.X > maxX)
                {
                    maxX = cell.Coords.X;
                }
                if (cell.Coords.Y > maxY)
                {
                    maxY = cell.Coords.Y;
                }
            }

            maxX += 1;
            maxY += 1;

            for (var x = -1; x <= maxX; x++)
            {
                for (var y = -1; y <= maxY; y++)
                {
                    var coords = new Coords(x, y);
                    if (!grid.Contains(coords) && AnyNeighbourCellExists(x, y, grid))
                    {
                        _edges.Add(GenerateEdge(x, y, grid, neighbourExistenceFlags));
                    }
                }
            }
        }


        private bool AnyNeighbourCellExists(int x, int y, Grid grid)
        {
            for (int nX = x - 1; nX <= x + 1; nX++)
            for (int nY = y - 1; nY <= y + 1; nY++)
            {
                if (grid.Contains(new Coords(nX, nY)))
                    return true;
            }

            return false;
        }

        private Edge GenerateEdge(int x, int y, Grid grid, bool[] neighbourExisanceFlags)
        {
            var result = new Edge(new Coords(x, y));

            for (int i = 0; i < neighbourExisanceFlags.Length; i++)
                neighbourExisanceFlags[i] = false;

            //bottom
            neighbourExisanceFlags[0] = grid.Contains(new Coords(x, y - 1));
            //bottom left
            neighbourExisanceFlags[1] = grid.Contains(new Coords(x - 1, y - 1));
            //left
            neighbourExisanceFlags[2] = grid.Contains(new Coords(x - 1, y));
            //top left
            neighbourExisanceFlags[3] = grid.Contains(new Coords(x - 1, y + 1));
            //top
            neighbourExisanceFlags[4] = grid.Contains(new Coords(x, y + 1));
            //top right
            neighbourExisanceFlags[5] = grid.Contains(new Coords(x + 1, y + 1));
            //right
            neighbourExisanceFlags[6] = grid.Contains(new Coords(x + 1, y));
            //bottom right
            neighbourExisanceFlags[7] = grid.Contains(new Coords(x + 1, y - 1));

            for (int i = 0; i < 4; i++)
            {
                int firstIndex = i * 2;
                int secondIndex = i * 2 + 1;
                int thirdIndex = i * 2 + 2;

                if (thirdIndex > 7)
                    thirdIndex -= 8;

                var quarterState = GenerateQuarterState(
                    neighbourExisanceFlags[firstIndex],
                    neighbourExisanceFlags[secondIndex],
                    neighbourExisanceFlags[thirdIndex], i * 90f);

                result.SetQuarterState(i, quarterState);
            }

            return result;
        }

        private EdgeQuarterState GenerateQuarterState(bool firstNeighbourExists, bool cornerNeighbourExists,
            bool secondNeighbourExists, float baseAngle)
        {
            if (firstNeighbourExists)
            {
                if (secondNeighbourExists)
                {
                    return new EdgeQuarterState(EdgeQuarterType.Recess, 270f - baseAngle);
                }

                return new EdgeQuarterState(EdgeQuarterType.Face, 360f - baseAngle);
            }

            if (cornerNeighbourExists)
            {
                if (secondNeighbourExists)
                {
                    return new EdgeQuarterState(EdgeQuarterType.Face, 270f - baseAngle);
                }

                return new EdgeQuarterState(EdgeQuarterType.Corner, 270f - baseAngle);
            }

            if (secondNeighbourExists)
            {
                return new EdgeQuarterState(EdgeQuarterType.Face, 270f - baseAngle);
            }

            return null;
        }
    }
}