using System;
using System.Collections.Generic;
using BebopBee.Core;
using DG.Tweening;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB
{
    public class LightningBoltEffect : BbbMonoBehaviour, IPoolItem
    {
        private readonly List<GameObject> _lightningPieces = new ();
        private readonly List<Tweener> _tweeners = new ();

        public void PlayAsBrokenLine(float time, GoPool piecePool, Coords from, Coords to, Func<Coords,
            Vector3> coordsTransform, Color baseColor, Color overlayColor, float widthMlt = 1f)
        {
            var numberOfLines = Random.Range(2, 7);
            var fromLocalPos = coordsTransform(from);
            var toLocalPos = coordsTransform(to);
            var dir = (toLocalPos - fromLocalPos).normalized;
            var magnitude = (toLocalPos - fromLocalPos).magnitude;
            var elementMagnitude = magnitude / numberOfLines;
            var prepMagnitude = .05f * magnitude;
            var perpDir = new Vector3(-dir.y, dir.x, 0f);

            var mod = Random.Range(0f,1f) > 0.5f ? 1 : -1;
            Vector3? end = null;
            var accumMagnitude = 0f;
            var oneLineAppearingTime = time / (numberOfLines-1);
            for (var i = 0; i < numberOfLines; i++)
            {
                var start = end ?? fromLocalPos;

                if (i == numberOfLines - 1)
                {
                    end = toLocalPos;
                }
                else
                {
                    accumMagnitude += elementMagnitude * Random.Range(0.5f, 1f);
                    var forwardVec = dir * accumMagnitude;
                    var perpVec = perpDir * prepMagnitude * mod * Random.Range(0.5f, 3f);
                    end = fromLocalPos + forwardVec + perpVec;
                    end += (end - start) * Random.Range(0, 0.2f);
                }

                var endToDraw = end.Value;

                _tweeners.Add(Rx.Invoke(oneLineAppearingTime * i, DrawLightningAction));
                
                mod *= -1;
                continue;

                void DrawLightningAction(long _)
                {
                    DrawLightning(piecePool, start, endToDraw, baseColor, overlayColor, widthMlt);
                }
            }
        }

        public void PlayAsStraightLine(GoPool piecePool,Coords from, Coords to, Vector2 cellSize, Color baseColor, Color overlayColor, float widthMlt)
        {
            var fromLocalPos = from.GetPositionOfCoordinate(cellSize);
            var toLocalPos = to.GetPositionOfCoordinate(cellSize);
            DrawLightning(piecePool, fromLocalPos, toLocalPos, baseColor, overlayColor, widthMlt);
        }

        private void DrawLightning(GoPool piecePool, Vector3 fromLocalPos, Vector3 toLocalPos, Color baseColor, Color overlayColor, float widthMlt)
        {
            var lightningLength = Vector3.Distance(fromLocalPos, toLocalPos);
            var prefab = piecePool.Prefab;
            var lightningPieceLength = prefab.GetComponent<RectTransform>().sizeDelta.y;
            var lengthRatio = lightningLength / lightningPieceLength;
            var numberOfPieces = Mathf.RoundToInt(lengthRatio);
            var lightningPieceScaleFactor = lengthRatio / numberOfPieces;
            var dir = (toLocalPos - fromLocalPos).normalized;
            var angle = Vector3.SignedAngle(Vector3.right, dir, Vector3.forward);

            for (var i = 0; i < numberOfPieces; i++)
            {
                var go = piecePool.Spawn<LightningBoltPieceEffect>();
                var localPos = fromLocalPos + i * (lightningPieceLength * lightningPieceScaleFactor) * dir;
                var tf = go.GetComponent<RectTransform>();
                tf.localPosition = localPos;
                var ls = tf.localScale;
                ls.x *= lightningPieceScaleFactor;
                ls.y *= widthMlt;
                tf.localScale = ls;
                tf.Rotate(Vector3.forward, angle);
                _lightningPieces.Add(go.gameObject);
                go.UpdateImageColors(baseColor, overlayColor);
            }
        }

        public void OnInstantiate()
        {
            gameObject.SetActive(false);
        }

        public void OnSpawn()
        {
            gameObject.SetActive(true);
        }

        public void OnRelease()
        {
            gameObject.SetActive(false);

            foreach (var piece in _lightningPieces)
            {
                var tf = piece.transform;
                tf.localPosition = Vector3.zero;
                tf.localRotation = Quaternion.identity;
                tf.localScale = Vector3.one;
                piece.Release();
            }

            _lightningPieces.Clear();

            foreach (var tweener in _tweeners)
            {
                tweener?.Kill();
            }
            _tweeners.Clear();
        }
    }
}