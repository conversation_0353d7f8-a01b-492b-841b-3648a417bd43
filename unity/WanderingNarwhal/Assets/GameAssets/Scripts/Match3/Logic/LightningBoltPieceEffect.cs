using BBB.Core;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class LightningBoltPieceEffect : ActivePoolItem
    {
        [SerializeField] private Image _baseImage;
        [SerializeField] private Image _overlayImage;
        
        public void UpdateImageColors(Color baseImageColor, Color overlayImageColor)
        {
            _baseImage.color = baseImageColor;
            _overlayImage.color = overlayImageColor;
        }
    }
}