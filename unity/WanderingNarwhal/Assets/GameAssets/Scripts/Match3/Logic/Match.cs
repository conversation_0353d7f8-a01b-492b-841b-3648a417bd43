using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using UnityEngine;
using System.Collections;
using BebopBee;

namespace BBB
{
    public enum MatchType
    {
        Normal = 0,
        Square = 1
    }

    public struct Match : IEquatable<Match>, IEnumerable<Coords>
    {
        public readonly Coords Start;
        public readonly int Length;
        public readonly TileKinds Kind;
        public readonly MatchType MatchType;

        public float DefaultPriority =>
            MatchType switch
            {
                MatchType.Normal => Length,
                MatchType.Square => 3.5f,
                _ => 0
            };

        private readonly CardinalDirections _direction;

        public Match(Coords start, int length, CardinalDirections direction, TileKinds kind, MatchType matchType = MatchType.Normal)
        {
            Start = start;
            Length = length;
            _direction = direction;
            Kind = kind;
            MatchType = matchType;
        }

        public IEnumerable<Coords> GetAllCoords()
        {
            if (MatchType == MatchType.Square)
            {
                for (var i = 0; i < Length; i++)
                {
                    yield return MatchHelper.SquareCoordsFromIndex(i, Start);
                }
            }
            else
            { 
                for (var i = 0; i < Length; i++)
                {
                    yield return Start.GoSingleCardinalDirection(_direction, i);
                } 
            }
        }

        public IEnumerable<Coords> GetAllCoordsStartingInTheMiddle()
        {
            if (MatchType == MatchType.Square)
            {
                for (int i = 0; i < Length; i++)
                {
                    yield return MatchHelper.SquareCoordsFromIndex(i, Start);
                }
            }
            else
            {
                var startIndex = Length - 3;
                for(int i = startIndex; i < Length; i++) 
                    yield return Start.GoSingleCardinalDirection(_direction, i);
            
                for(int i = 0; i < startIndex; i++)
                    yield return Start.GoSingleCardinalDirection(_direction, i);
            }
                
        }

        public Coords GetCoords(int offset)
        {
            if (MatchType == MatchType.Square)
            {
                return MatchHelper.SquareCoordsFromIndex(offset, Start);
            }

            return Start.GoSingleCardinalDirection(_direction, offset);
        }

        public bool Contains(Coords coords)
        {
            if (coords == Start) return true;

            if(MatchType == MatchType.Square)
            {
                for(int i = 1; i < Length; i++)
                    if (coords == MatchHelper.SquareCoordsFromIndex(i, Start))
                        return true;

                return false;
            }

            CardinalDirectionsHelper.GetDistanceAndDirrection(Start, coords, out var direction, out var distance);

            return direction == _direction && distance < Length;
        }

        public bool AnyOverlap(Match otherMatch)
        {
            foreach (var coord in otherMatch.GetAllCoords())
            {
                if (Contains(coord))
                    return true;
            }

            return false;
        }

        public bool EdgeIntersect(Match otherMatch, out Coords coords)
        {
            var otherStart = otherMatch.Start;
            var possibleIntersect1 = new Coords(Start.X, otherStart.Y);
            var possibleIntersect2 = new Coords(otherStart.X, Start.Y);

            if (Contains(possibleIntersect1) && otherMatch.Contains(possibleIntersect1))
            {
                coords = possibleIntersect1;
                return true;
            }

            if (Contains(possibleIntersect2) && otherMatch.Contains(possibleIntersect2))
            {
                coords = possibleIntersect2;
                return true;
            }

            coords = default;
            return false;
        }

        // If no prefered cells found than going center floor:
        private static readonly List<Coords> TempPreferedCoords = new List<Coords>();
        public Coords GetSpawnCoord(List<Coords> preferredCoords, 
            FixedSizeQueue<int> lastSettledTileIdsQueue,
            Grid grid)
        {
            lock (TempPreferedCoords)
            {
                if (preferredCoords != null)
                {
                    TempPreferedCoords.Clear();

                    foreach (var coord in preferredCoords)
                    {
                        if (Contains(coord))
                        {
                            TempPreferedCoords.Add(coord);
                        }
                    }

                    switch (TempPreferedCoords.Count)
                    {
                        case 0:
#if BBB_LOG
                            Debug.LogWarning("[Match] Found a match that already existed on the board for some reason:\n" + this);
#endif
                            break;
                        case 1:
                            return TempPreferedCoords[0];
                        default:
#if BBB_LOG
                            Debug.LogWarning("[Match] Found a match that has more than 1 preferred coords:\n" + this);
#endif
                            return TempPreferedCoords.DeterministicRandomInSelf();
                    }
                }

                if (lastSettledTileIdsQueue == null)
                {
                    return GetSpawnCoord(grid);
                }

                var tileIdsStack = new Stack<int>();
                foreach (var tileId in lastSettledTileIdsQueue)
                {
                    tileIdsStack.Push(tileId);
                }

                foreach (var tileId in tileIdsStack)
                {
                    foreach (var coord in GetAllCoords())
                    {
                        if (grid.TryGetCell(coord, out var cell) && cell.HasTile() && cell.Tile.Id == tileId)
                        {
                            return coord;
                        }
                    }
                }

                return GetSpawnCoord(grid);
            }
        }


        public Coords GetSpawnCoord(Grid grid)
        {
            if (MatchType == MatchType.Square)
            {
                //for square choose one of the bottoms randomly
                return Start;
            }

            switch (Length)
            {
                case 3: //for match3 choose middle
                    return GetCoords(1);
                case 5:  //for match5 choose middle
                case 4:  //for match4 choose third tile
                    return GetCoords(2);
                default:
                    return Start;
            }
        }

        public bool Equals(Match other)
        {
            return Start == other.Start
                   && Length == other.Length
                   && _direction == other._direction
                   && MatchType == other.MatchType;
        }

        public IEnumerator<Coords> GetEnumerator()
        {
            for (int i = 0; i < Length; i++)
            {
                yield return GetCoords(i);
            }
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = Start.GetHashCode();
                hashCode = (hashCode * 397) ^ Length;
                hashCode = (hashCode * 397) ^ (int) _direction;
                hashCode = (hashCode * 397) ^ (int) MatchType;
                return hashCode;
            }
        }

        public override string ToString()
        {
            var str = "";
            for (int i = 0; i < Length; i++)
            {
                var coord = GetCoords(i);
                str += coord + ",";
            }

            return "Match: " + str + " of kind " + Kind + " of type " + MatchType;
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public SimplifiedDirections GetSimplifiedDirections()
        {
            return _direction.GetSimplifiedDirections();
        }

        public CardinalDirections GetMatchAdjacentShape(Coords coord)
        {
            var result = CardinalDirections.N_E_S_W;

            foreach (var dir in CardinalDirectionsHelper.GetAllStraight())
            {
                var adjacentCoord = coord.GoSingleCardinalDirection(dir);
                if(Contains(adjacentCoord))
                    result &= ~dir;
            }
            
            return result;
        }

        public void GetMatchAdjacentCoords(ref HashSet<Coords> coords)
        {
            var allCoords = GetAllCoords();
            foreach (var coord in allCoords)
            {
                foreach (var dir in CardinalDirectionsHelper.GetAllStraight())
                {
                    coords.Add(coord.GoSingleCardinalDirection(dir));
                }
            }
        }
    }
}