using System;

namespace BBB
{
    public static class MatchHelper
    {
        private static Tile TempTile;

        public static Coords SquareCoordsFromIndex(int index, Coords squareBottomLeftCoords)
        {
            switch (index)
            {
                case 0:
                    return squareBottomLeftCoords;
                case 1:
                    var rightCoords = squareBottomLeftCoords;
                    rightCoords.X++;
                    return rightCoords;
                case 2:
                    var upCoords = squareBottomLeftCoords;
                    upCoords.Y++;
                    return upCoords;
                case 3:
                    var upRightCoords = squareBottomLeftCoords;
                    upRightCoords += new Coords(1,1);
                    return upRightCoords;

            }

            throw new NotImplementedException($"SquareCoordsFromIndex only supports [0,3] not {index}");
        }



        public static bool WillTileDieIfHit(Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            TempTile = tile.Clone();
            TempTile.ReduceHitPoints();
            return TempTile.IsDead;
        }
    }
}