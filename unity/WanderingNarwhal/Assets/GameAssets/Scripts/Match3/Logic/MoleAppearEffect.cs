using BBB;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class MoleAppearEffect : ConfigurableEffectBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _animationStartName;

        [SerializeField]
        private string _animationLoopName;

        [SerializeField]
        private string[] _skinsNames;

        public override void OnSpawn()
        {
            base.OnSpawn();
            if (!_animationStartName.IsNullOrEmpty() || !_animationLoopName.IsNullOrEmpty())
            {
                if (_sk == null) return;
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                if (!_animationStartName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, _animationStartName, loop: false);
                    if (!_animationLoopName.IsNullOrEmpty())
                    {
                        _sk.AnimationState.AddAnimation(0, _animationLoopName, loop: true, delay: 0);
                    }
                }
                else
                {
                    _sk.AnimationState.SetAnimation(0, _animationLoopName, loop: true);
                }
            }
        }

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            base.ApplyParameters(prm);
            ApplySkin(prm.skin);
        }

        private void ApplySkin(int skinIndex)
        {
            var skin = _sk.SkeletonData.FindSkin(_skinsNames[Mathf.Clamp(skinIndex, 0, _skinsNames.Length - 1)]);
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetToSetupPose();
        }
    }
}