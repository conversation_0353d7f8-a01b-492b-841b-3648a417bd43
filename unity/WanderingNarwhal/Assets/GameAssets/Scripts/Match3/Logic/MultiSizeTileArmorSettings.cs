using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [Serializable]
    public class DamageSourceCountPair
    {
        public DamageSource DamageSource;
        public int Limit;
        public int DamagePerHit;
    }

    [Serializable]
    public class SpecialityToDamageSourcesPair
    {
        public string TileSpeciality;
        public List<DamageSourceCountPair> DamageSources;
        public int DefaultLimit;
        public int DefaultDamagePerHit;
    }
    
    [CreateAssetMenu(fileName = "MultiSizeTileArmorSettings", menuName = "BBB/M3/MultiSizeTileArmorSettings", order = 1)]
    public class MultiSizeTileArmorSettings : ScriptableObject
    {
        public List<SpecialityToDamageSourcesPair> TileSettings;

        public (int limit, int damagePerHit) GetLimitAndDamageValueForSpecAndDamageSource(string specType, DamageSource damageSource)
        {
            if (TileSettings != null)
            {
                SpecialityToDamageSourcesPair foundSpecSetting = null;
                DamageSourceCountPair foundDamageSourceSetting = null;
                foreach (var specSettings in TileSettings)
                {
                    if (specSettings.TileSpeciality == specType)
                    {
                        foundSpecSetting = specSettings;
                        break;
                    }
                }

                if (foundSpecSetting?.DamageSources != null)
                {
                    foreach (var damageSourceSetting in foundSpecSetting.DamageSources)
                    {
                        if (damageSourceSetting.DamageSource == damageSource)
                        {
                            foundDamageSourceSetting = damageSourceSetting;
                            break;
                        }
                    }
                }

                if (foundDamageSourceSetting != null)
                    return (foundDamageSourceSetting.Limit, foundDamageSourceSetting.DamagePerHit);

                if (foundSpecSetting != null)
                {
                    return (foundSpecSetting.DefaultLimit, foundSpecSetting.DefaultDamagePerHit);
                }
            }

            return (int.MaxValue, 1);
        }
        
    }
}