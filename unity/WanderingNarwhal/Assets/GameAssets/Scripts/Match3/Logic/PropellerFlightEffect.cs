using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Match3;
using BebopBee.Core;
using BebopBee.UnityEngineExtensions;
using DG.Tweening;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class PropellerFlightEffect : ActivePoolItem
    {
        private static readonly int FlightIntro = Animator.StringToHash("FlightIntro");
        private static readonly int DestroyTrigger = Animator.StringToHash("DestroyTrigger");

        private static readonly Dictionary<Coords, int> CoordToMultiplierMap = new Dictionary<Coords, int>();
        
        [SerializeField] private Animator _animator;
        [SerializeField] private PropellerLayerRenderer _propellerRenderer;

        private Tweener _tweener;
        private Action _callback;

        //propellerComboIndex is only used in propeller+propeller combo
        //and needed to separate propellers from matching visually on the same trajectory
        //for all of the other cases propellerComboIndex is -1
        public void Launch(Coords sourceCoords, Coords targetCoords, int propellerComboIndex, Vector2 sourcePosition,
            Vector2 targetPosition, Vector2 cellSize,
            TrajectoryMultiplierDeterminer trajDeterminer,
            float duration,
            PropellerEffectSettings propellerEffectSettings, Vector3 scale, Action callback)
        {
            _callback = callback;
            _propellerRenderer.transform.localScale = scale;

            _animator.Play(FlightIntro);

            _propellerRenderer.PlayFlight();

            var tf = transform;
            tf.localPosition = sourcePosition;
            if (propellerComboIndex == 2)
            {
                duration += propellerEffectSettings.ThirdPropellerDelay;
            }

            tf.DOScale(propellerEffectSettings.TargetScale, duration).SetEase(propellerEffectSettings.ScaleEase);

            var ease = propellerEffectSettings.Ease;
            var path = GetPropellerEffectPath(sourceCoords, targetCoords, propellerComboIndex, sourcePosition, targetPosition,
                                                    cellSize, trajDeterminer, propellerEffectSettings, CoordToMultiplierMap);
            _tweener = tf.DOLocalPath(path, duration, PathType.CatmullRom)
                .OnComplete(CompleteAction)
                .SetEase(ease);
        }

        public static Vector3[] GetPropellerEffectPath(Coords sourceCoords, Coords targetCoords, int propellerComboIndex,
                                                        Vector2 sourcePosition, Vector2 targetPosition, Vector2 cellSize,
                                                        TrajectoryMultiplierDeterminer trajDeterminer, PropellerEffectSettings propellerEffectSettings,
                                                        Dictionary<Coords, int> coordToMultiplierMap = null)
        {
            var eccentricity = propellerEffectSettings.PerpPercent *
                                    GetPerpPercentDeterminer(sourceCoords, targetCoords, trajDeterminer, coordToMultiplierMap);

            if (propellerComboIndex != -1)
                eccentricity = (propellerComboIndex / 3f) * eccentricity;

            var path = PathFactory.GetCircPath(sourcePosition, targetPosition, eccentricity);
            foreach (var point in path)
                if (trajDeterminer.IsPointOutsideBoardRegion(point, 2, 0))
                {
                    var halfPath = (sourcePosition - targetPosition).magnitude * 0.5f;
                    eccentricity = Mathf.Sign(eccentricity) * cellSize.x / halfPath;
                    path = PathFactory.GetCircPath(sourcePosition, targetPosition, eccentricity);
                    break;
                }

            return path;
        }

        private static int GetPerpPercentDeterminer(Coords sourceCoords, Coords targetCoords,
            TrajectoryMultiplierDeterminer trajDeterminer, Dictionary<Coords, int> coordToMultiplierMap = null)
        {
            coordToMultiplierMap ??= new Dictionary<Coords, int>();

            if (Mathf.Abs(sourceCoords.X - targetCoords.X) <= 2)
            {
                var multiplier = trajDeterminer.GetTrajectoryMultiplier(sourceCoords.X);

                if (multiplier != 0)
                    return multiplier * (targetCoords.Y < sourceCoords.Y ? -1 : 1);
            }
            
            if (!coordToMultiplierMap.ContainsKey(sourceCoords))
            {
                coordToMultiplierMap.Add(sourceCoords, -1);
            }

            coordToMultiplierMap[sourceCoords] *= -1;
            return coordToMultiplierMap[sourceCoords];
        }

        public override void OnSpawn()
        {
            if (this == null) return;
            base.OnSpawn();
        }

        public override void OnRelease()
        {
            if (this == null) return;
            _tweener?.Kill();
            _tweener = null;

            transform.localScale = Vector3.one;
            
            _animator.PlayMain();
            _propellerRenderer.ResetSpine();

            base.OnRelease();
        }

        private void CompleteAction()
        {
            _callback.SafeInvoke();
            _callback = null;
            _propellerRenderer.PlayDestroy();
            _animator.SetTrigger(DestroyTrigger);
            gameObject.Release();
        }
    }
}