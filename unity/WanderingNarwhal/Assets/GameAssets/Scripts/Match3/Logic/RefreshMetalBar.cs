using BBB.CellTypes;

namespace BBB.Match3.Logic
{
    public static class RefreshMetalBar 
    {
        public static void RefreshMetalBarOnGrid(Grid grid, Cell c)
        {
            var sizeX = c.Tile.GetParam(TileParamEnum.SizeX);
            var sizeY = c.Tile.GetParam(TileParamEnum.SizeY);
            var orientation = c.Tile.GetParam(TileParamEnum.MetalBarOrientation);

            if (orientation is 0 or -90)
            {
                for (var x = c.Coords.X; x < c.Coords.X + sizeX; x++)
                    for (var y = c.Coords.Y; y > c.Coords.Y - sizeY; y--)
                    {
                        UpdateCell(x, y);
                    }
            }
            else
            {
                for (var x = c.Coords.X; x > c.Coords.X - sizeX; x--)
                    for (var y = c.Coords.Y; y < c.Coords.Y + sizeY; y++)
                    {
                        UpdateCell(x, y);
                    }
            }
            
            void UpdateCell(int x, int y)
            {
                if (!grid.TryGetCell(new Coords(x, y), out var affectedCell)) return;
                affectedCell.AddMultisizeCellReference(c);
                c.AddReferencedCell(affectedCell);
            }

            if (!c.IsAnyOf(CellState.MetalBar))
            {
                c.Add(CellState.MetalBar);
            }
            c.MetalBarStatus = true;
        }
    }
}
