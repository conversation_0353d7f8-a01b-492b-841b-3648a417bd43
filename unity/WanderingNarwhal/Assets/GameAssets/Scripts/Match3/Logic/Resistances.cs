using System;
using BBB.Match3.Renderer;

namespace BBB.Match3.Logic
{
    [Flags]
    public enum DamageSource
    {
        None                                            = 0, //0,
        Adjacent                                        = 1, //1,
        Gondola                                         = 1 << 2,
        LineBreakerArrowHor                             = 1 << 6, //64,
        Bomb                                            = 1 << 7, //128,
        Skunk                                           = 1 << 8, //256,
        FireWorks                                       = 1 << 9, //512
        Dynamite                                        = 1 << 10, //1024,
        Whirlpool                                       = 1 << 11, //2048,
        RemoveColorTiles                                = 1 << 12, //4096,
        TukTuk                                          = 1 << 13, //8192,
        AutoMatch                                       = 1 << 14, //16384,
        UsableBoost                                     = 1 << 18, //262144,
        EndGame                                         = 1 << 19, //524288,
        MultiBomb                                       = 1 << 20, //1048576,
        LineBreakerArrowVer                             = 1 << 21, //2097152,

        /// <summary>
        /// Special Damage type flag.
        /// If applied to tile allowed damage
        /// then this tile will be allowed to receive adjacentDamage
        /// only from tiles with same TileKind color.
        /// </summary>
        SameTileKind                                   = 1 << 22,

        Swap                                           = 1 << 23,

        /// <summary>
        /// Double tap input, only for boosters.
        /// </summary>
        DTap                                           = 1 << 24,
        BoostClear                                     = 1 << 25,
        SuperBoost                                     = 1 << 26,
        AdjacentFromBoost                              = 1 << 27,
        SmallCross                                     = 1 << 28,
        Propeller                                      = 1 << 29,
        PropellerCombo                                 = 1 << 30,
        InputMatch                                     = 1 << 31,
        
        AnyBomb = Bomb | MultiBomb,
        Match = AutoMatch | InputMatch,
        PropellerOrPropellerCombo = Propeller | PropellerCombo,
        AdjacentGeneral = Adjacent | AdjacentFromBoost,    
        //turned off score rendering on board
        ScoreRendering = None,
        ForceSkipDestroyAnim = TapOrSwap | BoostClear | Match,
        LineBreakerArrow = LineBreakerArrowHor | LineBreakerArrowVer,
        PowerUp = BoostClear | LineBreakerArrow | Bomb | MultiBomb | SmallCross | Propeller | PropellerCombo | TukTuk,
        AllBase = LineBreakerArrow | Bomb | MultiBomb | SmallCross | Propeller | PropellerCombo | Whirlpool | Dynamite 
                 | RemoveColorTiles | Match | UsableBoost | EndGame | Skunk | SuperBoost | BoostClear | TukTuk | FireWorks,
        AddsToSuperBoost = DTap | UsableBoost | PowerUp,
        TapOrSwap = DTap | Swap,
        TargetingMechanics = PropellerOrPropellerCombo | Skunk | FireWorks,
    }

    public static class ResistanceDamageHelper
    {
        public static TileLayerViewAnimParams ToAnimParams(this DamageSource damageSource)
        {
            var animParams = TileLayerViewAnimParams.None;
            
            switch (damageSource)
            {
                case DamageSource.Whirlpool:
                {
                    animParams |= TileLayerViewAnimParams.ByWhirlpool;
                    break;
                }
                case DamageSource.RemoveColorTiles:
                {
                    animParams |= TileLayerViewAnimParams.ByRemoveColorTiles;
                    break;
                }
            }

            return animParams;
        }
        
        public static bool CanDamage(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) != 0;
        }

        public static bool IsAnyOf(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) != 0;
        }

        public static bool Is(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) == allowedDamage;
        }
    }
}
