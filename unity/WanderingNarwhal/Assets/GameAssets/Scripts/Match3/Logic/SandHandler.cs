using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using UnityEngine;

namespace BBB.Match3.Logic
{
    public class SandHandler
    {
        /// <summary>
        /// Flag for checking is currently have destroyed sand since last sand generation.
        /// Sand will grow only if no sand tiles were destroyed during turn.
        /// </summary>
        private bool _isSandDestroyedOnGrid;

        private static List<Cell> _tempCellsCache = new List<Cell>(40);

        public void CheckIfSandWasDestroyedThisTurn(GameSimulation sim)
        {
            if (sim.HasRemoveActionFor(TileState.SandMod))
            {
                MarkSandWasDestroyedThisTurn();
            }
        }

        public void MarkSandWasDestroyedThisTurn()
        {
            _isSandDestroyedOnGrid = true;
        }

        /// <summary>
        /// Calculate sand grow.
        /// </summary>
        /// <param name="grid"><Level grid./param>
        /// <returns>Cell, on which sand should grow.</returns>
        public Cell PostProcessHandleGrowSandLogic(Grid grid)
        {
            if (_isSandDestroyedOnGrid)
            {
                _isSandDestroyedOnGrid = false;
                return null;
            }

            _tempCellsCache.Clear();
            for (int i = grid.Cells.Count - 1; i >= 0; i--)
            {
                var cell = grid.Cells[i];
                if (cell.HasTile() && cell.Tile.IsAnyOf(TileState.SandMod))
                {
                    _tempCellsCache.Add(cell);
                }
            }

            if (_tempCellsCache.Count == 0)
            {
                return null;
            }

            // Get random expansion direction.
            var dx = RandomSystem.Next(3) - 1; // -1, 0, 1.
            var dy = dx == 0 ? RandomSystem.Next(2) * 2 - 1 // -1, 1.
                : 0;

            // {dx,dy} is unit vector in random direction (random of 4 possible directions).

            Cell targetCell = null;

            while (targetCell == null && _tempCellsCache.Count > 0)
            {
                // Get random sand cell.
                var expansionCenterCell = _tempCellsCache.DeterministicRandomInSelf();
                _tempCellsCache.Remove(expansionCenterCell);

                for (int i = 0; i < 4; i++)
                {
                    // Try expand.
                    grid.TryGetCell(new Coords(expansionCenterCell.Coords.X + dx, expansionCenterCell.Coords.Y + dy), out targetCell);

                    if (IsCellExpandableBySand(expansionCenterCell, targetCell, grid))
                    {
                        break;
                    }
                    else
                    {
                        targetCell = null;

                        // Rotate expansion direction by 90 deg.
                        var tmp = dx;
                        dx = dy;
                        dy = -tmp;
                    }
                }
            }

            return targetCell;
        }

        public void GrowSandOnCell(Grid grid, Cell cell)
        {
            var existingTile = cell.Tile;
            if (!ReferenceEquals(existingTile, null) && !existingTile.IsDead)
            {
                existingTile.Add(TileState.SandMod);
            }
            else
            {
                var newSandTile = TileFactory.CreateTile(grid.TilesSpawnedCount, TileAsset.Sand, new TileOrigin(Creator.SpecialTile, cell));
                cell.AddTile(newSandTile);
                grid.TilesSpawnedCount = Mathf.Max(grid.TilesSpawnedCount, cell.Tile.Id + 1);
            }
        }

        private static bool IsCellExpandableBySand(Cell fromCell, Cell cell, Grid grid)
        {
            if (cell == null)
            {
                return false;
            }

            if (cell.IsAnyOf(CellState.Spawner | CellState.Water | CellState.FlagEnd))
            {
                return false;
            }

            if (cell.HasMultiSizeCellReference())
            {
                return false;
            }

            if (fromCell.HasMultiSizeCellReferenceWithCellOverlay())
            {
                return false;
            }

            if (grid.HasWallBetween(fromCell.Coords, cell.Coords))
            {
                return false;
            }

            if (!cell.IsBaseCellSwappable())
            {
                return false;
            }

            if (!cell.HasTile())
            {
                return true;
            }

            return cell.Tile.IsSimple() && !cell.Tile.IsAnyOf(TileState.SandMod | TileState.IceCubeMod | TileState.ChainMod);
        }
    }
}