using BBB;
using BBB.Core.Crash;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class ScaleableEffect : ConfigurableEffectBase
    {
        [SerializeField] private ParticleSystemEmitterSizeScaler _scaler;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            CrashLoggerService.Log("ScaleableEffect ApplyParameters called");
            _scaler.SetParticlesEmitterSize(prm.x, prm.y);
        }
    }
}