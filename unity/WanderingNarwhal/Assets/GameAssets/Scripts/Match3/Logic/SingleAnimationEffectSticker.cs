using System.Collections.Generic;
using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SingleAnimationEffectSticker : SingleAnimationEffect
    {
        public List<GameObject> _crateParticles;
        public List<GameObject> _slateParticles;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            base.ApplyParameters(prm);
            var skinPrm = (StickerSkin)prm.skin;
            if (skinPrm == StickerSkin.Slate)
            {
                SetState(_crateParticles, false);
                SetState(_slateParticles, true);
            }
            else
            {
                SetState(_crateParticles, true);
                SetState(_slateParticles, false);
            }
        }

        private static void SetState(List<GameObject> particles, bool state)
        {
            if (particles == null) return;
            foreach (var particle in particles)
            {
                if (particle != null)
                {
                    particle.SetActive(state);
                }
            }
        }
    }
}