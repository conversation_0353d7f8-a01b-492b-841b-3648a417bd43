using JetBrains.Annotations;
using UnityEngine;
using BBB.Core;

namespace BBB
{
    public struct FxOptionalParameters
    {
        public Color col;
        public int skin;
        public int x;
        public int y;
    }

    public class SingleAnimatorEffect : ActivePoolItem
    {
        [SerializeField] bool useEnableDisableVersion = false;
        private Animator _animator;

        public void SetupAnimDuration(float animDuration)
        {
            if (_animator != null)
            {
                var clipInfos = _animator.GetCurrentAnimatorClipInfo(0);

                if (clipInfos != null && clipInfos.Length > 0)
                {
                    var currentClipLength = clipInfos[0].clip.length;
                    var speedMult = currentClipLength / animDuration;
                    _animator.speed = speedMult;
                }
            }
        }

        public override void OnInstantiate()
        {
            _animator = GetComponent<Animator>();
            base.OnInstantiate();
            if (useEnableDisableVersion)
                UnregisterComponent(_animator);
        }

        void OnValidate()
        {
            if (useEnableDisableVersion)
            {
                if (!_animator)
                {
                    _animator = GetComponent<Animator>();
                }
                if (_animator)
                {
                    _animator.enabled = false;
                }
            }
        }

        public virtual void ApplyParameters(FxOptionalParameters prm)
        {
        }

        public override void OnSpawn()
        {
            if (this == null) return;
            if (useEnableDisableVersion && _animator)
            {
                _animator.enabled = true;
            }
            base.OnSpawn();
        }

        public override void OnRelease()
        {
            if (this == null) return;
            base.OnRelease();
            if (_animator)
            {
                _animator.Rebind();
            }
        }

        [UsedImplicitly]
        void DisableEffect()
        {
            if (_animator)
            {
                _animator.enabled = false;
            }
        }
    }
}