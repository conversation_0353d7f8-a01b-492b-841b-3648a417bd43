using System.Collections.Generic;
using UnityEngine;

namespace BBB
{
    public class SingleAnimatorEffectWithColoredParticles : SingleAnimatorEffect
    {
        [SerializeField]
        public List<ParticleSystem> _particleSystems;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            if (_particleSystems.Count == 0)
            {
                return;
            }

            foreach (var ps in _particleSystems)
            {
                if (ps == null)
                    continue;
                
                var mainModule = ps.main;
                mainModule.startColor = prm.col;
            }
        }
    }
}