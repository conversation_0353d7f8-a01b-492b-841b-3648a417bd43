using System;
using System.Collections;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SkunkAttackEffect : ConfigurableEffectBase
    {
        [SerializeField]
        private Transform _movableTrail;

        [SerializeField]
        private float _moveStartDelay = 0.05f;

        [SerializeField]
        private float _moveDuration = 0.1f;

        [SerializeField]
        private Vector3 _trailStartOffset;

        private IEnumerator _skunkFXCoroutine;
        private Action _callback;

        public void SetupSkunkAttackEffect(Vector3 sourceCellPosition, Action callback)
        {
            if (_skunkFXCoroutine != null)
            {
                StopCoroutine(_skunkFXCoroutine);
                _callback.SafeInvoke();
                _callback = null;
                _skunkFXCoroutine = null;
            }

            _callback = callback;
            _skunkFXCoroutine = MoveTrailRoutine(sourceCellPosition + _trailStartOffset, transform.position, callback);
            StartCoroutine(_skunkFXCoroutine);
            OnReleaseEvent -= OnReleaseFX;
            OnReleaseEvent += OnReleaseFX;
        }

        private void OnReleaseFX()
        {
            if(_skunkFXCoroutine == null)
                return;
            StopCoroutine(_skunkFXCoroutine);
            _callback.SafeInvoke();
            _callback = null;
            _skunkFXCoroutine = null;
            OnReleaseEvent -= OnReleaseFX;
        }
        
        private IEnumerator MoveTrailRoutine(Vector3 startPoint, Vector3 endPoint, Action callback)
        {
            // Coroutine may be finished before this effect executes the callback
            OnReleaseEvent -= callback;
            OnReleaseEvent += callback;
            
            if (_moveDuration <= 0)
            {
                yield break;
            }

            float timer = 0f;
            while (timer < _moveStartDelay)
            {
                yield return null;
                timer += Time.deltaTime;
            }

            timer = 0f;
            _movableTrail.gameObject.SetActive(true);
            while (timer < _moveDuration)
            {
                var ratio = timer / _moveDuration;
                var p = Vector3.Lerp(startPoint, endPoint, ratio);
                _movableTrail.position = p;
                yield return null;
                timer += Time.deltaTime;
            }

            _movableTrail.position = endPoint;
            _movableTrail.gameObject.SetActive(false);
            _skunkFXCoroutine = null;
            callback.SafeInvoke();
            OnReleaseEvent -= callback;
            _callback = null;
        }
    }
}