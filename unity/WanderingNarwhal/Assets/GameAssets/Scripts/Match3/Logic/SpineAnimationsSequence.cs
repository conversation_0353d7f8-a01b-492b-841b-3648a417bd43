using System;
using BBB;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    /// <summary>
    /// Component for triggering specific spine animations in sequence.
    /// </summary>
    /// <remarks>
    /// Used for m3 effects with Spin<PERSON> views inside.
    /// </remarks>
    public class SpineAnimationsSequence : BbbMonoBehaviour
    {
        [Serializable]
        public struct SpineAnimData
        {
            public string animName;
            public float delay;
            public bool isLooping;
        }

        [Serializable]
        public struct SoundData
        {
            public string soundUid;
            public float delay;
        }

        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private SpineAnimData[] _animsToPlay;
        [SerializeField] private SoundData[] _soundsToPlay;

        protected override void OnEnable()
        {
            base.OnEnable();
            StartPlayAnimations();
        }

        private void StartPlayAnimations()
        {
            if (_sk == null) return;

            if (_sk.SkeletonData is null)
            {
                _sk.Initialize(false);
            }

            int index = 0;
            foreach (var anim in _animsToPlay)
            {
                if (index == 0)
                {
                    _sk.AnimationState.SetAnimation(0, anim.animName, anim.isLooping);
                }
                else
                {
                    _sk.AnimationState.AddAnimation(0, anim.animName, anim.isLooping, anim.delay);
                }

                index++;
            }

            foreach (var item in _soundsToPlay)
            {
                if (item.soundUid.IsNullOrEmpty()) continue;

                Rx.Invoke(item.delay, (_) =>
                {
                    if (_sk == null) return;
                    if (gameObject == null) return;
                    if (!gameObject.activeInHierarchy) return;
                    AudioProxy.PlaySound(item.soundUid);
                });
            }
        }
    }
}