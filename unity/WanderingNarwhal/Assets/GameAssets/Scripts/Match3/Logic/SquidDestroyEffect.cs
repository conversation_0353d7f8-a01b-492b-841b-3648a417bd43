using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SquidDestroyEffect : ConfigurableEffectBase
    {
        [SerializeField] private GameObject[] _particlesBySizeList;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            base.ApplyParameters(prm);
            if (_particlesBySizeList is null || _particlesBySizeList.Length == 0) return;
            var index = Mathf.Clamp(prm.skin - 1, 0, _particlesBySizeList.Length - 1);
            foreach (var p in _particlesBySizeList)
            {
                p.SetActive(false);
            }

            _particlesBySizeList[index].SetActive(true);
        }
    }
}