using BBB;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SquidGoalEffect : ConfigurableEffectBase
    {
        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private string[] _skinsNames;

        public override void OnSpawn()
        {
            base.OnSpawn();
            if (_sk.SkeletonData is null)
            {
                _sk.Initialize(false);
            }
        }

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            base.ApplyParameters(prm);
            ApplySkin(prm.skin);
        }

        private void ApplySkin(int skinIndex)
        {
            var skinName = _skinsNames[Mathf.Clamp(skinIndex, 0, _skinsNames.Length - 1)];
            if (skinName.IsNullOrEmpty())
            {
                int closestIndex = 1 << 10;
                for (int i = 0; i < _skinsNames.Length; i++)
                {
                    if (_skinsNames[i].IsNullOrEmpty()) continue;

                    if (Mathf.Abs(skinIndex - i) <= Mathf.Abs(skinIndex - closestIndex))
                    {
                        closestIndex = i;
                        skinName = _skinsNames[i];
                    }
                }
            }

            var skin = _sk.SkeletonData.FindSkin(skinName); 
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetToSetupPose();
        }
    }
}