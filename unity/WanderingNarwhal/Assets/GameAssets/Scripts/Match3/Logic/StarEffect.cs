using UnityEngine;
using BBB.Core;

namespace BBB
{
	
	public class StarEffect : ActivePoolItem
	{
        [SerializeField] private Animation[] _animations;

		public override void OnRelease()
		{
            if (!Initialized)
            {
                foreach (var animation in _animations)
                {
                    animation.Rewind();
                }
            }

            base.OnRelease();
		}
	}


}
