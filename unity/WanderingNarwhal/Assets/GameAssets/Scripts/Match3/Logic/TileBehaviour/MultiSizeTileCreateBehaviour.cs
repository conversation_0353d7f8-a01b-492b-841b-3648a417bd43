using BBB;
using BBB.Match3;
using UnityEngine;

public class MultiSizeTileCreateBehaviour : BbbMonoBehaviour, ITileCreateBehaviour
{
    [SerializeField] private int _sizeX;
    [SerializeField] private int _sizeY;

    public void Create(TileBehaviorContext tileBehaviorContext)
    {
        var tileParams = tileBehaviorContext.TileParams;

        if (tileParams != null)
        {
            tileParams.Add(new TileParam(TileParamEnum.SizeX, _sizeX));
            tileParams.Add(new TileParam(TileParamEnum.SizeY, _sizeY));
        }
    }
}