using GameAssets.Scripts.Match3.Logic;

namespace BBB
{
    public static class TileKindExtensions
    {
        public static bool IsColored(this TileKinds tileKind)
        {
            return (int) tileKind > 0;
        }

        public static GoalType ToGoalType(this TileKinds tileKind)
        {
            switch (tileKind)
            {
                case TileKinds.Green:
                    return GoalType.Green;
                case TileKinds.Yellow:
                    return GoalType.Yellow;
                case TileKinds.Blue:
                    return GoalType.Blue;
                case TileKinds.Red:
                    return GoalType.Red;
                case TileKinds.Purple:
                    return GoalType.Purple;
                case TileKinds.Orange:
                    return GoalType.Orange;
                case TileKinds.White:
                    return GoalType.White;
                default:
                    return GoalType.None;
            }
        }
    }
    
    //do not change the values or names
    public enum TileKinds : short
    {
        Error = -3,
        Undefined = -2,
        None = 0,
        Green = 1,
        Yellow = 2,
        Blue = 3,
        <PERSON> = 4,
        <PERSON> = 5,
        <PERSON> = 6,
        White = 7,
        COUNT
    }
}