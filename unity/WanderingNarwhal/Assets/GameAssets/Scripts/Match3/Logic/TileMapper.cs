using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB
{
    public static class TileMapper
    {
        private const TileState InTransitionNotMatchable = TileState.InTransition | TileState.NotMatchable;
        public static bool IsSimple(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return tile.Speciality == TileSpeciality.None && tile.Kind.IsColored();
        }

        public static bool IsMatchable(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return tile.Kind.IsColored() && tile.IsNoneOf(InTransitionNotMatchable);
        }

        public static bool IsSpecialMatchable(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return !tile.IsAnyOf(InTransitionNotMatchable) || tile.Speciality == TileSpeciality.ColorBomb;
        }

        public static TileState ParamToModState(TileParamEnum tileParam)
        {
            switch (tileParam)
            {
                case TileParamEnum.ChainLayerCount: return TileState.Chained;
                case TileParamEnum.IceLayerCount: return TileState.IceCube;
                case TileParamEnum.VaseLayerCount: return TileState.Vase;
                case TileParamEnum.EggLayerCount: return TileState.Egg;
                case TileParamEnum.FlowerPotLayerCount: return TileState.FlowerPot;
                default:
                    return TileState.None;
            }
        }

        public static TileState UnwrapMods(TileState state, bool forSerialization = false)
        {
            var result = TileState.None;
            var fullModsState = TileStateExtensions.ModFullStates();
            foreach (var mod in TileStateExtensions.ModStates)
            {
                if (forSerialization)
                {
                    if (mod == TileState.MoneyBagMod)
                    {
                        // We must manually exclude money bag mod when this method is used for serialization,
                        // because money bag is serialized in tile speciality
                        // and some levels already contain tiles with moneybag state, which should not have been there.
                        // todo: remove this when all level will be resaved without moneybag tile state. -VK
                        continue;
                    }
                }

                if ((state & mod) != 0 && fullModsState.ContainsKey(mod))
                {
                    result |= fullModsState[mod];
                }
            }

            return result;
        }

        public static BoosterItem GetAssetDefaultBoosterApplicability(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.DropItem:
                    return BoosterItem.None;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Bomb:
                case TileSpeciality.Propeller:
                    return BoosterItem.All & ~BoosterItem.CreateBomb;
                case TileSpeciality.ColorBomb:
                    return BoosterItem.Wind;
                case TileSpeciality.Litter:
                case TileSpeciality.Sticker:
                case TileSpeciality.ColorCrate:
                case TileSpeciality.Frame:
                case TileSpeciality.Pinata:
                case TileSpeciality.Watermelon:
                case TileSpeciality.Stone:
                case TileSpeciality.Pouch:    
                    return BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                           BoosterItem.Vertical | BoosterItem.Horizontal;
            }

            return BoosterItem.All;
        }

        public static DamageSource GetTileStateExcludedDamageSource(TileState tilState)
        {
            if ((tilState & TileState.ChainMod) != 0)
            {
                return DamageSource.AdjacentGeneral;
            }
            
            return DamageSource.None;
        }

        public static DamageSource GetTileStateAllowedDamageSource(TileState tileState)
        {
            var result = DamageSource.None;
            if ((tileState & TileState.SandMod) != 0)
            {
                return DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.UsableBoost | DamageSource.SuperBoost;
            }

            if ((tileState & TileState.IceCubeMod) != 0 || (tileState & TileState.VaseMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.UsableBoost |
                          DamageSource.SuperBoost | DamageSource.Whirlpool | DamageSource.Dynamite |
                          DamageSource.Skunk | DamageSource.FireWorks;

            if ((tileState & TileState.ChainMod) != 0)
                result |= DamageSource.AllBase & ~DamageSource.AdjacentGeneral;

            if ((tileState & TileState.ColorCrateMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp;

            if ((tileState & TileState.MoneyBagMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.PenguinMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.EggMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.FlowerPotMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BirdMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SheepMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BananaMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.MonkeyMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SkunkMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.HenMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.ChickenMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.HiveMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BeeMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SquidMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp;

            if ((tileState & TileState.ToadMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.MagicHatMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BowlingMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.SodaMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BushMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.IceBarMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.DynamiteBoxMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.GiantPinataMod) != 0)
                result |= DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                          DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;

            if ((tileState & TileState.ShelfMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.JellyFishMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.GoldenScarabMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.GondolaMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.Gondola;
            
            if ((tileState & TileState.FireWorksMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.SlotMachineMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.BigMonkeyMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            return result;
        }

        public static DamageSource GetTileSpecialityAllowedDamageSource(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.BlinkingTile:
                    return DamageSource.Bomb | DamageSource.MultiBomb | DamageSource.LineBreakerArrow | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Bomb:
                case TileSpeciality.Propeller:
                    return DamageSource.AllBase | DamageSource.TapOrSwap;
                case TileSpeciality.ColorBomb:
                    return DamageSource.Propeller | DamageSource.TapOrSwap;
                case TileSpeciality.DropItem:
                    return DamageSource.None;
                case TileSpeciality.Pinata:
                case TileSpeciality.Safe:
                case TileSpeciality.GiantPinata:
                case TileSpeciality.MetalBar:
                case TileSpeciality.Stone:
                    return DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                           DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.ColorCrate:
                    return DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp
                           | DamageSource.Whirlpool | DamageSource.Dynamite | DamageSource.UsableBoost |
                           DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.Litter:
                case TileSpeciality.Sticker:
                case TileSpeciality.Watermelon:
                case TileSpeciality.Frame:
                case TileSpeciality.Mole:
                case TileSpeciality.Toad:
                case TileSpeciality.MagicHat:
                case TileSpeciality.Bowling:
                case TileSpeciality.Bush:
                case TileSpeciality.Soda:
                case TileSpeciality.IceBar:
                case TileSpeciality.DynamiteBox:
                case TileSpeciality.Shelf:
                case TileSpeciality.JellyFish: 
                case TileSpeciality.GoldenScarab:    
                case TileSpeciality.Gondola:
                case TileSpeciality.FireWorks:
                case TileSpeciality.SlotMachine:
                case TileSpeciality.Pouch:    
                    return DamageSource.AllBase | DamageSource.AdjacentGeneral;
                case TileSpeciality.Squid:
                {
                    return DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.Skunk |
                           DamageSource.FireWorks | DamageSource.PowerUp | DamageSource.Whirlpool |
                           DamageSource.Dynamite | DamageSource.UsableBoost | DamageSource.SuperBoost;
                    
                }
            }

            return DamageSource.AllBase;
        }

        private static readonly Dictionary<TileAsset, TileSpeciality> AssetToSpecialityMap = new()
            {
                { TileAsset.ColorBomb, TileSpeciality.ColorBomb },
                { TileAsset.Sticker, TileSpeciality.Sticker },
                { TileAsset.Frame, TileSpeciality.Frame },
                { TileAsset.DropItem, TileSpeciality.DropItem },
                { TileAsset.Litter, TileSpeciality.Litter },
                { TileAsset.Pinata, TileSpeciality.Pinata },
                { TileAsset.ColumnBreaker, TileSpeciality.ColumnBreaker },
                { TileAsset.RowBreaker, TileSpeciality.RowBreaker },
                { TileAsset.Bomb, TileSpeciality.Bomb },
                { TileAsset.BlinkingTile, TileSpeciality.BlinkingTile },
                { TileAsset.Sand, TileSpeciality.Sand },
                { TileAsset.ColorCrate, TileSpeciality.ColorCrate },
                { TileAsset.Watermelon, TileSpeciality.Watermelon },
                { TileAsset.MoneyBag, TileSpeciality.MoneyBag },
                { TileAsset.Penguin, TileSpeciality.Penguin },
                { TileAsset.Egg, TileSpeciality.Egg },
                { TileAsset.Bird, TileSpeciality.Bird },
                { TileAsset.Sheep, TileSpeciality.Sheep },
                { TileAsset.Banana, TileSpeciality.Banana },
                { TileAsset.Monkey, TileSpeciality.Monkey },
                { TileAsset.Skunk, TileSpeciality.Skunk },
                { TileAsset.Hen, TileSpeciality.Hen },
                { TileAsset.Chicken, TileSpeciality.Chicken },
                { TileAsset.Hive, TileSpeciality.Hive },
                { TileAsset.Bee, TileSpeciality.Bee },
                { TileAsset.Mole, TileSpeciality.Mole },
                { TileAsset.Squid, TileSpeciality.Squid },
                { TileAsset.Toad, TileSpeciality.Toad },
                { TileAsset.Propeller, TileSpeciality.Propeller },
                { TileAsset.Bowling, TileSpeciality.Bowling },
                { TileAsset.Bush, TileSpeciality.Bush },
                { TileAsset.Soda, TileSpeciality.Soda },
                { TileAsset.MagicHat, TileSpeciality.MagicHat },
                { TileAsset.Safe, TileSpeciality.Safe },
                { TileAsset.FlowerPot, TileSpeciality.FlowerPot },
                { TileAsset.IceBar, TileSpeciality.IceBar },
                { TileAsset.DynamiteBox, TileSpeciality.DynamiteBox },
                { TileAsset.GiantPinata, TileSpeciality.GiantPinata },
                { TileAsset.MetalBar, TileSpeciality.MetalBar },
                { TileAsset.Shelf, TileSpeciality.Shelf },
                { TileAsset.JellyFish, TileSpeciality.JellyFish },
                { TileAsset.GoldenScarab, TileSpeciality.GoldenScarab },
                { TileAsset.Gondola, TileSpeciality.Gondola },
                { TileAsset.TukTuk, TileSpeciality.TukTuk },
                { TileAsset.FireWorks, TileSpeciality.FireWorks },
                { TileAsset.SlotMachine, TileSpeciality.SlotMachine },
                { TileAsset.BigMonkey, TileSpeciality.BigMonkey },
                { TileAsset.Stone, TileSpeciality.Stone },
                { TileAsset.Pouch, TileSpeciality.Pouch },
            };

        private static readonly Dictionary<TileSpeciality, TileState> SpecialityToStateMap = new()
        {
            { TileSpeciality.None, TileState.None },
            { TileSpeciality.BlinkingTile, TileState.Blinking },
            { TileSpeciality.RowBreaker, TileState.LineBreaker },
            { TileSpeciality.ColumnBreaker, TileState.LineBreaker },
            { TileSpeciality.Bomb, TileState.Bomb },
            { TileSpeciality.ColorBomb, TileState.ColorBomb },
            { TileSpeciality.DropItem, TileState.DropItem },
            { TileSpeciality.Litter, TileState.Litter },
            { TileSpeciality.Sand, TileState.Sand },
            { TileSpeciality.Sticker, TileState.Sticker },
            { TileSpeciality.Frame, TileState.Frame },
            { TileSpeciality.Pinata, TileState.Pinata },
            { TileSpeciality.ColorCrate, TileState.ColorCrate },
            { TileSpeciality.Watermelon, TileState.Watermelon },
            { TileSpeciality.MoneyBag, TileState.MoneyBag },
            { TileSpeciality.Penguin, TileState.Penguin },
            { TileSpeciality.Egg, TileState.Egg },
            { TileSpeciality.Bird, TileState.Bird },
            { TileSpeciality.Sheep, TileState.Sheep },
            { TileSpeciality.Banana, TileState.Banana },
            { TileSpeciality.Monkey, TileState.Monkey },
            { TileSpeciality.Skunk, TileState.Skunk },
            { TileSpeciality.Hen, TileState.Hen },
            { TileSpeciality.Chicken, TileState.Chicken },
            { TileSpeciality.Hive, TileState.Hive },
            { TileSpeciality.Bee, TileState.Bee },
            { TileSpeciality.Mole, TileState.Mole },
            { TileSpeciality.Squid, TileState.Squid },
            { TileSpeciality.Toad, TileState.Toad },
            { TileSpeciality.MagicHat, TileState.MagicHat },
            { TileSpeciality.Propeller, TileState.Propeller },
            { TileSpeciality.Bowling, TileState.Bowling },
            { TileSpeciality.Bush, TileState.Bush },
            { TileSpeciality.Soda, TileState.Soda },
            { TileSpeciality.Safe, TileState.Safe },
            { TileSpeciality.FlowerPot, TileState.FlowerPot },
            { TileSpeciality.IceBar, TileState.IceBar },
            { TileSpeciality.DynamiteBox, TileState.DynamiteBox },
            { TileSpeciality.GiantPinata, TileState.GiantPinata },
            { TileSpeciality.MetalBar, TileState.MetalBar },
            { TileSpeciality.Shelf, TileState.Shelf },
            { TileSpeciality.JellyFish, TileState.JellyFish },
            { TileSpeciality.GoldenScarab, TileState.GoldenScarab },
            { TileSpeciality.Gondola, TileState.Gondola },
            { TileSpeciality.TukTuk, TileState.TukTuk },
            { TileSpeciality.FireWorks, TileState.FireWorks },
            { TileSpeciality.SlotMachine, TileState.SlotMachine },
            { TileSpeciality.BigMonkey, TileState.BigMonkey },
            { TileSpeciality.Stone, TileState.Stone },
            { TileSpeciality.Pouch, TileState.Pouch },
        };
        

        public static TileSpeciality GetAssetDefaultSpeciality(TileAsset tileAsset)
        {
            return AssetToSpecialityMap.GetValueOrDefault(tileAsset, TileSpeciality.None);
        }

        public static TileState GetSpecialityDefaultState(TileSpeciality tileSpeciality)
        {
            return SpecialityToStateMap.TryGetValue(tileSpeciality, out var state) 
                ? state 
                : throw new ArgumentOutOfRangeException(nameof(tileSpeciality), tileSpeciality, null);
        }
    }
}
