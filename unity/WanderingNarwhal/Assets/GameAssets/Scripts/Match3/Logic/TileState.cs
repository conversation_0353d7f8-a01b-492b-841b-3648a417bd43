using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB
{
    public static class TileStateExtensions
    {
        private static Dictionary<TileState, TileState> _cachedTileModFullStates;
        
        public static TileState[] ModStates { get; } = {
            TileState.ChainMod,
            TileState.IceCubeMod,
            TileState.AnimalMod,
            TileState.SandMod,
            TileState.ColorCrateMod,
            TileState.WatermelonMod,
            TileState.VaseMod,
            TileState.MoneyBagMod,
            TileState.PenguinMod,
            TileState.SheepMod,
            TileState.BananaMod,
            TileState.MonkeyMod,
            TileState.SkunkMod,
            TileState.MoleMod,
            TileState.SquidMod,
            TileState.SheepMod,
            TileState.ToadMod,
            TileState.BowlingMod,
            TileState.BushMod,
            TileState.SodaMod,
            TileState.MagicHatMod,
            TileState.SafeMod,
            TileState.IceBarMod,
            TileState.MetalBarMod,
            TileState.DynamiteBoxMod,
            TileState.GiantPinataMod,
            TileState.ShelfMod,
            TileState.JellyFishMod,
            TileState.GoldenScarabMod,
            TileState.GondolaMod,
            TileState.TukTukMod,
            TileState.FireWorksMod,
            TileState.SlotMachineMod,
            TileState.BigMonkeyMod,
            TileState.StoneMod,
            TileState.PouchMod
        };

        /// <summary>
        /// Convert Mod tile state to Full tile state.
        /// </summary>
        /// <remarks>
        /// Mod tile state is a simple tag,
        /// while full state contains functional flags, affecting tile behaviour.
        /// </remarks>
        public static Dictionary<TileState, TileState> ModFullStates()
        {
            if (_cachedTileModFullStates == null)
            {
                _cachedTileModFullStates = new Dictionary<TileState, TileState>
                {
                    { TileState.ChainMod, TileState.Chained },
                    { TileState.IceCubeMod, TileState.IceCube },
                    { TileState.SandMod, TileState.Sand },
                    { TileState.ColorCrateMod, TileState.ColorCrate },
                    { TileState.WatermelonMod, TileState.Watermelon },
                    { TileState.VaseMod, TileState.Vase },
                    { TileState.MoneyBagMod, TileState.MoneyBag },
                    { TileState.PenguinMod, TileState.Penguin },
                    { TileState.AnimalMod, TileState.AnimalMod },
                    { TileState.SheepMod, TileState.Sheep },
                    { TileState.BananaMod, TileState.Banana },
                    { TileState.MonkeyMod, TileState.Monkey },
                    { TileState.SkunkMod, TileState.Skunk },
                    { TileState.MoleMod, TileState.Mole },
                    { TileState.SquidMod, TileState.Squid },
                    { TileState.ToadMod, TileState.Toad },
                    { TileState.BowlingMod, TileState.Bowling },
                    { TileState.MagicHatMod, TileState.MagicHat },
                    { TileState.BushMod, TileState.Bush },
                    { TileState.SodaMod, TileState.Soda },
                    { TileState.SafeMod, TileState.Safe },
                    { TileState.IceBarMod, TileState.IceBar },
                    { TileState.DynamiteBoxMod, TileState.DynamiteBox },
                    { TileState.GiantPinataMod, TileState.GiantPinata },
                    { TileState.MetalBarMod, TileState.MetalBar },
                    { TileState.ShelfMod, TileState.Shelf },
                    { TileState.JellyFishMod, TileState.JellyFish },
                    { TileState.GoldenScarabMod, TileState.GoldenScarab },
                    { TileState.GondolaMod, TileState.Gondola },
                    { TileState.TukTukMod, TileState.TukTuk },
                    { TileState.FireWorksMod, TileState.FireWorks },
                    { TileState.SlotMachineMod, TileState.SlotMachine },
                    { TileState.BigMonkeyMod, TileState.BigMonkey },
                    { TileState.StoneMod, TileState.Stone },
                    { TileState.PouchMod, TileState.Pouch }
                };

#if UNITY_EDITOR || BBB_DEBUG
                // Failsafe check to help not forget about adding new values.
                var allMods = ModStates;
                foreach (var mod in allMods)
                {
                    if (!_cachedTileModFullStates.ContainsKey(mod))
                    {
                        Debug.LogError($"Mod '{mod}' is not present in full mod states dictionary");
                    }
                }

                foreach (var mod in _cachedTileModFullStates)
                {
                    var isExist = false;
                    foreach (var tileState in allMods)
                    {
                        if (tileState == mod.Key)
                        {
                            isExist = true;
                            break;
                        }
                    }

                    if (!isExist)
                    {
                        Debug.LogError($"Mod '{mod}' full state was not added to the list of mods");
                    }
                }
#endif
            }

            return _cachedTileModFullStates;
        }

        public static bool IsNull(this Tile tile)
        {
            return ReferenceEquals(tile, null);
        }
    }

    [Flags]
    public enum TileState : ulong
    {
        None                    = 0,

        //MODS. DO NOT modify these without proper level files migration
        ChainMod                = 1L << 0, //1
        IceCubeMod              = 1L << 1, //2
        AnimalMod               = 1L << 3, //8
        SandMod                 = 1L << 4, //16
        ColorCrateMod           = 1L << 5, //32
        WatermelonMod           = 1L << 6, //64
        VaseMod                 = 1L << 7, //128
        MoneyBagMod             = 1L << 8, //256
        PenguinMod              = 1L << 9, //512
        EggMod                  = 1L << 10, //1024
        BirdMod                 = 1L << 11, //2048
        SheepMod                = 1L << 12, //4096
        BananaMod               = 1L << 13, //8096

        MonkeyMod               = 1L << 22,
        SkunkMod                = 1L << 23,
        MoleMod                 = 1L << 24,
        SquidMod                = 1L << 25,
        ToadMod                 = 1L << 26,

        HenMod                  = 1L << 32,
        ChickenMod              = 1L << 33,
        HiveMod                 = 1L << 34,
        BeeMod                  = 1L << 35,
        BowlingMod              = 1L << 38,
        BushMod                 = 1L << 39,
        SodaMod                 = 1L << 40,
        MagicHatMod             = 1L << 41,
        SafeMod                 = 1L << 42,
        FlowerPotMod            = 1L << 43,
        IceBarMod               = 1L << 44,
        DynamiteBoxMod          = 1L << 45,
        GiantPinataMod          = 1L << 46,
        MetalBarMod             = 1L << 47,
        ShelfMod                = 1L << 48,
        JellyFishMod            = 1L << 49,
        GoldenScarabMod         = 1L << 50,
        GondolaMod              = 1L << 51,
        TukTukMod               = 1L << 52,
        FireWorksMod            = 1L << 53,
        SlotMachineMod          = 1L << 54,
        BigMonkeyMod            = 1L << 55,
        StoneMod                = 1L << 56,
        PouchMod                = 1L << 57,
        
        ArmorMod                     = ChainMod | IceCubeMod | SandMod | VaseMod,
        
        //STATES
        ZeroGravity             = 1L << 14, //16384,
        NotShufflable           = 1L << 15, //32768,
        NotSwappable            = 1L << 16, //65536,
        NotMatchable            = 1L << 18, //262144,
        Invincible              = 1L << 21, //2097152,
        //SPECIAL FLAGS

        GameEventLabel          = 1L << 27, //134217728,
        SupermatchProduct       = 1L << 28, //268435456,
        AutomatchProduct        = 1L << 29, //536870912,
        InTransition            = 1L << 30, //1073741824,
        IgnoreColorBombTargeting= 1L << 31, //2147483648,
        StealingHatLabel        = 1L << 36,
        TriggerByDTap           = 1L << 37,

        //STATEMODPACKS, DO NOT USE THEM DIRECTLY, USE STATES AND MODS ONLY
        Chained                 = ChainMod | ZeroGravity | NotShufflable | NotSwappable,
        IceCube                 = IceCubeMod | NotShufflable | NotSwappable | ZeroGravity,
        Bomb                    = NotShufflable | TriggerByDTap,
        LineBreaker             = NotShufflable | TriggerByDTap,
        ColorBomb               = NotMatchable | NotShufflable | TriggerByDTap,
        Sticker                 = NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Frame                   = NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        DropItem                = NotMatchable | NotShufflable | Invincible,
        Litter                  = NotMatchable | NotShufflable,
        Sand                    = NotMatchable | NotShufflable | ZeroGravity | NotSwappable | IgnoreColorBombTargeting | SandMod,
        Pinata                  = NotMatchable | NotShufflable,
        Blinking                = NotMatchable | NotShufflable | NotSwappable,
        ColorCrate              = NotMatchable | NotShufflable | ZeroGravity | NotSwappable | IgnoreColorBombTargeting | ColorCrateMod,
        Watermelon              = NotMatchable | NotShufflable | WatermelonMod,
        Vase                    = VaseMod | NotMatchable | NotShufflable,
        MoneyBag                = MoneyBagMod | NotShufflable,
        Penguin                 = PenguinMod | NotMatchable | NotShufflable | NotSwappable,
        Egg                     = EggMod | NotMatchable | NotShufflable,
        Bird                    = BirdMod | NotShufflable,
        Sheep                   = SheepMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Banana                  = BananaMod | NotMatchable | NotShufflable,
        Monkey                  = MonkeyMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Skunk                   = SkunkMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Hen                     = HenMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Chicken                 = ChickenMod | NotShufflable,
        Hive                    = HiveMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Bee                     = BeeMod | NotShufflable,
        Mole                    = MoleMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Squid                   = SquidMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Toad                    = ToadMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        MagicHat                = MagicHatMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Propeller               = NotShufflable | TriggerByDTap,
        Bowling                 = BowlingMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Bush                    = BushMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Soda                    = SodaMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Safe                    = SafeMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        FlowerPot               = FlowerPotMod | NotMatchable | NotShufflable,
        IceBar                  = IceBarMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        DynamiteBox             = DynamiteBoxMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        GiantPinata             = GiantPinataMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        MetalBar                = MetalBarMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Shelf                   = ShelfMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        JellyFish               = JellyFishMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        GoldenScarab            = GoldenScarabMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Gondola                 = GondolaMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        TukTuk                  = TukTukMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        FireWorks               = FireWorksMod | NotMatchable | NotShufflable,
        SlotMachine             = SlotMachineMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        BigMonkey               = BigMonkeyMod | NotMatchable | NotShufflable | NotSwappable | ZeroGravity,
        Stone                   = StoneMod | NotMatchable | NotShufflable | ZeroGravity | NotSwappable,
        Pouch                   = PouchMod | NotMatchable | NotShufflable
    }
}