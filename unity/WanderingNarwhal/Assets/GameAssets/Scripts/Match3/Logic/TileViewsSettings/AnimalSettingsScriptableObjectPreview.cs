#if UNITY_EDITOR
using BBB;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEditor;

/// <summary>
/// Preview for custom fly path for M3 tile Goal effect.
/// Not very precise (due to inconsistent scale) but allows to reduce amount game restarts when adjusting curves for some specific effect.
/// </summary>
[CustomPreview(typeof(GoalIconFlySettingsScriptableObject))]
public class AnimalSettingsScriptableObjectPreview : ObjectPreview
{
    private GoalIconFlySettingsScriptableObject _target;

    public override void Initialize(Object[] targets)
    {
        base.Initialize(targets);
        _target = target as GoalIconFlySettingsScriptableObject;
    }

    public override bool HasPreviewGUI()
    {
        return true;
    }

    public override void OnPreviewGUI(Rect r, GUIStyle background)
    {
        if (_target == null) return;
        if (_target.StartAnimCurve == null) return;
        if (_target.GoalFlyMode != GoalFlyMode.GoalFlyAnimator) return;
        
        Handles.BeginGUI();
        const float cellSize = 68f;
        const float defaultDistance = cellSize * 4;

        var totalDistance = _target.RelateiveOffset.magnitude + defaultDistance;
        Handles.color = Color.red;
        const float circleRadius = 14f;
        var xMid = (r.xMin + r.xMax) * 0.5f;
        var yMid = (r.yMin + r.yMax) * 0.5f;
        float margin = r.width * 0.12f;
        var startPoint = new Vector3(r.xMin + margin, yMid + cellSize * 2);
        var endPoint = new Vector3(r.xMax - margin, yMid - cellSize * 2);
        Handles.DrawLine(startPoint, endPoint);
        HandlesDrawCircle(startPoint, circleRadius, r);
        HandlesDrawCircle(endPoint, circleRadius, r);
        var midPointRatio = _target.DurationFirstPart > 0 ? _target.RelateiveOffset.magnitude / totalDistance : 0;
        var midPoint = startPoint + new Vector3(_target.RelateiveOffset.x, -_target.RelateiveOffset.y);
        Handles.DrawLine(midPoint + new Vector3(0, 3f), midPoint - new Vector3(0, 3f));
        Handles.DrawLine(midPoint + new Vector3(3, 0f), midPoint - new Vector3(3, 0f));
        const float cellScaleX = 1f;
        const float cellScaleY = -1f;
        const int pointsCount = 50;

        var lastPathPoint = startPoint;
        Vector3 pathPoint = lastPathPoint;

        Handles.color = Color.green;
        for (int i = 0; i < pointsCount; i++)
        {
            var totalRatio = i * midPointRatio / (pointsCount - 1f);
            var ratio = i / (pointsCount - 1f);
            var pathPointSecondary = new Vector3(_target.ParalelMotionXCurve.Evaluate(totalRatio) * cellScaleX, 
                _target.ParalelMotionYCurve.Evaluate(totalRatio) * cellScaleY);
            var lerpRatio = _target.StartAnimCurve.Evaluate(ratio);
            pathPoint = Util.Lerp(startPoint, midPoint, lerpRatio) + pathPointSecondary;
            if (r.Contains(lastPathPoint) && r.Contains(pathPoint))
            {
                Handles.DrawLine(lastPathPoint, pathPoint);
            }

            lastPathPoint = pathPoint;
        }

        lastPathPoint = midPoint + new Vector3(_target.ParalelSecondMotionXCurve.Evaluate(midPointRatio) * cellScaleX, 
            _target.ParalelSecondMotionYCurve.Evaluate(midPointRatio) * cellScaleY);
        HandlesDrawCircle(lastPathPoint, circleRadius * 0.5f, r);

        Handles.color = Color.yellow;
        for (int i = 0; i < pointsCount; i++)
        {
            var totalRatio = midPointRatio + i * (1 - midPointRatio) / (pointsCount - 1f);
            var ratio = i / (pointsCount - 1f);
            var pathPointSecondary = new Vector3(_target.ParalelSecondMotionXCurve.Evaluate(totalRatio) * cellScaleX, 
                _target.ParalelSecondMotionYCurve.Evaluate(totalRatio) * cellScaleY);
            pathPoint = Util.Lerp(midPoint, endPoint, _target.EndAnimCurve.Evaluate(ratio)) + pathPointSecondary;
            if (r.Contains(lastPathPoint) && r.Contains(pathPoint))
            {
                Handles.DrawLine(lastPathPoint, pathPoint);
                var normal = Vector3.Cross(pathPoint - lastPathPoint, new Vector3(0, 0, 1)).normalized;
                const float guideLineWidth = 5f;
                Handles.DrawLine(pathPoint - normal * guideLineWidth, pathPoint + normal * guideLineWidth);
            }

            lastPathPoint = pathPoint;
        }

        Handles.EndGUI();
    }

    private static void HandlesDrawCircle(Vector3 center, float radius, Rect view)
    {
        float angle = 0;
        const float pi2 = Mathf.PI * 2f;
        float prevX = Mathf.Cos(angle) * radius;
        float prevY = Mathf.Sin(angle) * radius;
        float x;
        float y;
        const int segments = 16;
        for (int i = 1; i <= segments; i++)
        {
            angle += pi2 / segments;
            x = Mathf.Cos(angle) * radius;
            y = Mathf.Sin(angle) * radius;
            var p0 = new Vector3(center.x + prevX, center.y + prevY);
            var p1 = new Vector3(center.x + x, center.y + y);
            if (view.Contains(p0) && view.Contains(p1))
            {
                Handles.DrawLine(p0, p1);
            }

            prevX = x;
            prevY = y;
        }
    }
}
#endif