using System;
using BBB;
using BBB.Audio;
using BBB.Core;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using UnityEngine.Serialization;

namespace GameAssets.Scripts.Match3.Logic
{
    /// <summary>
    /// Goal fly animation settings.
    /// </summary>
    [CreateAssetMenu(fileName = "GoalFlyAnimSettings", menuName = "BBB/M3/Goal Fly Settings", order = 1)]
    public class GoalIconFlySettingsScriptableObject : ScriptableObject
    {
        public enum AnimCenterPointType : byte
        {
            CenterOfScreen,
            Relative,
        }

        public enum ScaleCurveType
        {
            /// <summary>
            /// Curve represents relative curve, i.e. 0 value means initial value.
            /// </summary>
            Relative,

            Absolute,
        }

        [Header("First part of motion curve along distance vector. Starts at (0;0), ends at (1;1)")]
        [FormerlySerializedAs("_animalGoalAnimCurve")]
        [SerializeField] private AnimationCurve _animCurve;

        [Header("Motion curve along distance vector. Starts at (0;0), ends at (1;1)")]
        [FormerlySerializedAs("_animalGoalEndAnimCurve")]
        [SerializeField] private AnimationCurve _endAnimCurve;

        [Header("Second order first motion X-axis curve. Starts at (0,0), ends at (1, 0)")]
        [SerializeField] private AnimationCurve _paralelMoveXCurve = AnimationCurve.Linear(0, 0, 0, 0);

        [Header("Second order first motion Y-axis curve. Starts at (0,0), ends at (1, 0)")]
        [SerializeField] private AnimationCurve _paralelMoveYCurve = AnimationCurve.Linear(0, 0, 0, 0);
        
        [Header("Second order second motion X-axis curve. Starts at (0,0), ends at (1, 0)")]
        [SerializeField] private AnimationCurve _paralelSecondMoveXCurve = AnimationCurve.Linear(0, 0, 0, 0);

        [Header("Second order second motion Y-axis curve. Starts at (0,0), ends at (1, 0)")]
        [SerializeField] private AnimationCurve _paralelSecondMoveYCurve = AnimationCurve.Linear(0, 0, 0, 0);

        [FormerlySerializedAs("_animalFlyDurationFirstFirst")]
        [SerializeField] private float _flyDurationFirstSegment = 2f;

        [FormerlySerializedAs("_animalFlyDurationSecond")]
        [SerializeField] private float _flyDurationSecondSegment = 2f;

        [FormerlySerializedAs("_animalSoundDelay")]
        [SerializeField] private float _soundDelay = 0.5f;

        [SerializeField] private string _startSoundUid = Match3SoundIds.AnimalReleasedFromFrame;
        [SerializeField] private string _endSoundUid = Match3SoundIds.TileKindLanding;
        [SerializeField] private AnimCenterPointType _centerPointType = AnimCenterPointType.CenterOfScreen;
        [SerializeField] private Vector2 _relativeOffset;
        [SerializeField] private bool _useTrail;
        [SerializeField] private float _startDelay = 0.1f;
        [SerializeField] private ScaleCurveType _scaleCurveType = ScaleCurveType.Relative;

        [Header("DEPRECATED! DoFly animator will ignore all curves settings and will use own curve (this is used for regular tile goals and fly coins).")]
        [SerializeField] private bool _useRegularDoFlyAnimator;
        
        [Header("GoalFly Mode ")]
        [SerializeField] private GoalFlyMode _goalFlyMode;



        [Header("Scale curve, applied during both stages of motion. Starts at (0,0), ends at (1, 0)")]
        [FormerlySerializedAs("_animalGoalScaleCurve")]
        [SerializeField] private AnimationCurve _scaleCurve;
        [SerializeField] private bool _useSeparateScaleCurves;
        [SerializeField] private AnimationCurve _scaleCurveX;
        [SerializeField] private AnimationCurve _scaleCurveY;

        [Space()]
        [Header("Setting below used only in regular animator")]
        [SerializeField]
        private Ease _resourceFlyEaseType = Ease.OutQuad;

        [SerializeField]
        private FxType _previewFxType = FxType.DoFlyResource;

        [Header("A prefab specifying")]
        [SerializeField]
        private Sprite _previewSprite;
        
        [Header("Waypoints definitions. Prefab definition takes priority over manual waypoints.")]
        [SerializeField]
        private GameObject _relativeWaypointPrefab;
        [SerializeField]
        private Vector3[] _relativeWaypoints;

        [SerializeField] private float _additionalPerTileDelay;
        
        [Header("Override Settings for tile delay")]
        
        [SerializeField] private bool _disableXMirroring;
        
        [SerializeField] private bool _overridePerTileDelay;

        [SerializeField] private Vector2 _randomTileDelayLimits = new Vector2(0f, 1f);

        [Header("Shadow Settings for tile")]
        [SerializeField] private bool _useShadow;
        [SerializeField] private Vector3 _shadowOffset;

        [Header("Haptic Settings for tile")]
        [SerializeField] private float _hapticDelay;
        [SerializeField] private ImpactPreset _hapticPreset;

        public FxType PreviewFx => _previewFxType;

        public Sprite PreviewSprite => _previewSprite;

        public bool IsMiddlePointInCenterOfScreen => _centerPointType == AnimCenterPointType.CenterOfScreen;

        public float TotalDuration => _flyDurationFirstSegment + _flyDurationSecondSegment;
        public bool UseTrail => _useTrail;

        /// <summary>
        /// Used only if center point is Relative type.
        /// </summary>
        public Vector2 RelateiveOffset => _relativeOffset;

        public AnimationCurve StartAnimCurve => _animCurve;

        public AnimationCurve EndAnimCurve => _endAnimCurve;

        public AnimationCurve ScaleCurve => _scaleCurve;
        public AnimationCurve ScaleCurveX => _scaleCurveX;
        public AnimationCurve ScaleCurveY => _scaleCurveY;

        public AnimationCurve ParalelMotionXCurve => _paralelMoveXCurve;

        public AnimationCurve ParalelMotionYCurve => _paralelMoveYCurve;
        public AnimationCurve ParalelSecondMotionXCurve => _paralelSecondMoveXCurve;

        public AnimationCurve ParalelSecondMotionYCurve => _paralelSecondMoveYCurve;

        public float DurationFirstPart => _flyDurationFirstSegment;
        
        public float DurationSecondPart => _flyDurationSecondSegment;

        public string StartSoundUid => _startSoundUid;

        public string EndSoundUid => _endSoundUid;

        public float SoundDelay => _soundDelay;

        public GoalFlyMode GoalFlyMode => _goalFlyMode;
        
        public ScaleCurveType ScaleType => _scaleCurveType;
        
        public bool UseSeparateScaleCurves => _useSeparateScaleCurves;
        
        public float StartDelay => _startDelay;

        public bool UseShadow => _useShadow;
        public Vector3 ShadowOffset => _shadowOffset;

        public float HapticDelay => _hapticDelay;
        public ImpactPreset HapticPreset => _hapticPreset;

        /// <summary>
        /// Convert settings asset to fly animation data.
        /// </summary>
        /// <remarks>
        /// This method allows to merge all goal animation settings (including external parameters such as fx prefab, positions, sprite, etc.)
        /// into one data object that will be then used in GoalAnimator controller.
        /// The process of conversion is designed to be universal for all goal types and this
        /// should be used for every goal animation in the game. -VK 
        /// </remarks>
        public GoalIconFlySettings ToSettingsData(Vector2 from, Vector2 to, FxType fx, IGridController gridController, TileResourceSelector tileResources, FxRenderer fxRenderer, RendererContainers rendererContainers, Sprite goalIcon, int? skin, TilesResources tilesResources, Action onTargetReached, Action onEnd)
        {
            GameObject FactoryMethod()
            {
                var effect = rendererContainers.SpawnFx(fx);

                if (skin.HasValue)
                {
                    var configurableEffect = effect.GetComponent<ConfigurableEffectBase>();
                    if (configurableEffect != null)
                    {
                        configurableEffect.ApplyParameters(new FxOptionalParameters() { skin = skin.Value });
                    }
                    else
                    {
                        BDebug.Log(LogCat.Match3, $"Effect '{fx}' has skin parameter but the effect prefab missing component that allows to configurate skin.");
                    }
                }

                var animal = effect.GetComponent<AnimalReleaseEffect>();
                if (animal != null)
                {
                    animal.Setup(tileResources);
                }

                var trail = effect.GetComponent<TrailRenderer>();
                if (trail != null)
                {
                    trail.enabled = _useTrail;
                }
                else
                {
                    if (_useTrail)
                    {
                        BDebug.Log(LogCat.Match3, $"Effect '{fx}' config has trail enabled flag but the effect prefab missing trail renderer on root object.");
                    }
                }

                var normalTile = effect.GetComponent<NormalTileRenderer>();
                if (normalTile != null)
                {
                    normalTile.SetData(new TileViewData
                    {
                        Sprite = goalIcon,
                        CellSize = tilesResources.CellSize
                    });
                }

                var tf = effect.transform;
                if (_useSeparateScaleCurves && _scaleCurveType == ScaleCurveType.Relative)
                {
                    TweenScaleRelativeOnSeparateAxis(tf, _scaleCurveX, _scaleCurveY, TotalDuration);
                }
                else if (_scaleCurve != null && _scaleCurve.keys.Length > 1)
                {
                    if (_scaleCurveType == ScaleCurveType.Relative)
                    {
                        TweenScaleRelative(tf, _scaleCurve, TotalDuration);
                    }
                    else
                    {
                        TweenScaleAbsolute(tf, _scaleCurve, TotalDuration);
                    }
                }

                if (UseShadow && fxRenderer != null)
                {
                    fxRenderer.SpawnFlyShadow(tf as RectTransform, TotalDuration, ShadowOffset, ScaleCurve);
                }

                if (HapticPreset != ImpactPreset.None && fxRenderer != null)
                {
                    Rx.Invoke(HapticDelay, _ =>
                    {
                        fxRenderer.PlayHapticFeedback(HapticPreset);
                    });
                }

                return effect;
            }

            void OnTargetReachedCallback(int index)
            {
                onTargetReached?.Invoke();
                AudioProxy.PlaySound(_endSoundUid);
            }

            void OnEndCallback()
            {
                onEnd?.Invoke();
            }

            void ReleaseMethod(GameObject go)
            {
                go.Release();
            }

            return new GoalIconFlySettings()
            {
                midPos = IsMiddlePointInCenterOfScreen
                    ? gridController?.GetCenterPosition() ?? from + _relativeOffset
                    : from + _relativeOffset,
                fromPos = _useRegularDoFlyAnimator ? from + _relativeOffset : from,
                toPos = to,
                startDelay = _startDelay,
                stepsCount = 1,
                stepDelay = 0,
                flightDuration = TotalDuration,
                flightDurationFirstSegment = _flyDurationFirstSegment,
                flightDurationSecondSegment = _flyDurationSecondSegment,
                motionFirstSegment = _animCurve,
                motionSecondSegment = _endAnimCurve,
                motionSecondaryX = _paralelMoveXCurve,
                motionSecondaryY = _paralelMoveYCurve,
                motionSecondSecondaryX = _paralelSecondMoveXCurve,
                motionSecondSecondaryY = _paralelSecondMoveYCurve,
                startSoundDelay = _soundDelay,
                startSoundUid = _startSoundUid,
                flyMode = _goalFlyMode,
                relativeWaypointsPrefab = _relativeWaypointPrefab,
                relativeWaypoints = _relativeWaypoints,
                additionalPerTileDelay = _additionalPerTileDelay,
                factoryMethod = FactoryMethod,
                onTargetReachedCallback = OnTargetReachedCallback,
                onEndCallback = OnEndCallback,
                releaseMethod = ReleaseMethod,
                easeType = _resourceFlyEaseType,
                disableXMirroring = _disableXMirroring,
                overridePerTileDelay = _overridePerTileDelay,
                randomTileDelayLimits = _randomTileDelayLimits
            };
        }

        public static void TweenScaleRelative(Transform tf, AnimationCurve scaleCurve, float duration)
        {
            tf.localScale = Vector3.one;
            tf.DOScale(tf.localScale, duration)
                .SetEase(scaleCurve)
                .SetRelative();
        }

        public static void TweenScaleRelativeOnSeparateAxis(Transform tf, AnimationCurve scaleCurveX,
            AnimationCurve scaleCurveY, float duration)
        {
            tf.localScale = Vector3.one;

            if (scaleCurveX == null || scaleCurveX.keys.Length <= 1 || scaleCurveY == null ||
                scaleCurveY.keys.Length <= 1) return;
            
            DOTween.To(() => 0f, progress =>
            {
                tf.localScale = new Vector3(scaleCurveX.Evaluate(progress), scaleCurveY.Evaluate(progress), 1f);
            }, 1f, duration);
        }

        public static void TweenScaleAbsolute(Transform tf, AnimationCurve scaleCurve, float duration)
        {
            DOTween.To(Getter, Setter, endValue: 1f, duration: duration);

            float Getter()
            {
                return 0f;
            }

            void Setter(float t)
            {
                var scale = scaleCurve.Evaluate(t);
                tf.localScale = new Vector3(scale, scale, scale);
            }
        }
    }
}