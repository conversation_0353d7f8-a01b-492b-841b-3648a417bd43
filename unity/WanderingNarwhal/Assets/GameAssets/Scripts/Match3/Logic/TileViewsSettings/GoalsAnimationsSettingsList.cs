using System;
using BBB;
using BBB.Core;
using BBB.Match3.Renderer;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [Serializable]
    public class GoalsAnimationData
    {
        [GoalTypeName]
        public long goal;
        public FxType fx;
        public GoalIconFlySettingsScriptableObject settings;
    }

    [Serializable]
    public class GoalsIconsData
    {
        [GoalTypeName]
        public long goal;
        public Sprite sprite;
    }

    [Serializable]
    public class AppearAnimSettingsData
    {
        public TileAsset targetTile;
        public FxType fxType;
        public GoalIconFlySettingsScriptableObject settings;
    }

    /// <summary>
    /// Settings container for all m3 goals fly animations and icons sprites. Contains reference to settings assets and associated effect type.
    /// If some goal is not specified in this config, then default animation and fx will be used for it. 
    /// </summary>
    [CreateAssetMenu(fileName = "GoalsAnimSettingsList", menuName = "BBB/M3/GoalsAnimsList", order = 1)]
    public class GoalsAnimationsSettingsList : ScriptableObject
    {
        [SerializeField] private FxType _defaultGoalAnimationFx;
        [SerializeField] private GoalIconFlySettingsScriptableObject _defaultGoalAnimationSettings;
        [SerializeField] private GoalsAnimationData[] _customGoalsAnims;
        [SerializeField] private GoalsIconsData[] _goalsIcons;
        [SerializeField] private AppearAnimSettingsData[] _appearAnimSettings;

        public void GetSettinsForGoal(GoalType goal, out FxType fx, out GoalIconFlySettingsScriptableObject settins)
        {
            var goalNum = (long)goal;
            foreach (var item in _customGoalsAnims)
            {
#if UNITY_EDITOR
                item.goal.DebugValidateGoalIsSingular();
#endif
                if (item.goal == goalNum)
                {
                    fx = item.fx;
                    settins = item.settings;
                    return;
                }
            }

            fx = _defaultGoalAnimationFx;
            settins = _defaultGoalAnimationSettings;
        }

        public GoalType GetGoalForSettings(GoalIconFlySettingsScriptableObject settings)
        {
            foreach (var item in _customGoalsAnims)
            {
                if (item.settings == settings)
                {
                    return (GoalType)item.goal;
                }
            }

            return GoalType.None;
        }

        public Sprite GetGoalIcon(GoalType goal)
        {
            var goalNum = (long)goal;
            foreach (var item in _goalsIcons)
            {
#if UNITY_EDITOR
                item.goal.DebugValidateGoalIsSingular();
#endif
                if (item.goal == goalNum)
                {
                    return item.sprite;
                }
            }

            return null;
        }

        public void GetAppearFlySetting(TileAsset targetType, out FxType fx, out GoalIconFlySettingsScriptableObject settings)
        {
            foreach (var entry in _appearAnimSettings)
            {
                if (entry.targetTile == targetType)
                {
                    fx = entry.fxType;
                    settings = entry.settings;
                    return;
                }
            }

            BDebug.LogError(LogCat.Match3, "Appear animation not found for tile: " + targetType);
            fx = FxType.DoFlyResource;
            settings = _defaultGoalAnimationSettings;
        }
    }
}