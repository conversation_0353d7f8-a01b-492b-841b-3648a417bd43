using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [CreateAssetMenu(fileName = "SharedGoalVisualSettings", menuName = "BBB/M3/Shared Goal Visual Settings", order = 2)]
    public class SharedGoalVisualSettingsScriptableObject : ScriptableObject
    {
        [SerializeField]
        private List<string> _skipFlyGoals;

        private HashSet<GoalType> _skipFlyGoalsEnum;

        public bool IsGoalFlyingAllowed(GoalType goalType)
        {
            if (_skipFlyGoals == null)
                return true;

            if (_skipFlyGoalsEnum == null)
            {
                _skipFlyGoalsEnum = new HashSet<GoalType>();
                foreach(var goalTypeStr in _skipFlyGoals)
                    if (Enum.TryParse<GoalType>(goalTypeStr, out var outGoalType))
                        _skipFlyGoalsEnum.Add(outGoalType);
            }
            
            foreach(var workingGoalType in _skipFlyGoalsEnum)
                if (workingGoalType == goalType)
                    return false;

            return true;
        }
    }
}