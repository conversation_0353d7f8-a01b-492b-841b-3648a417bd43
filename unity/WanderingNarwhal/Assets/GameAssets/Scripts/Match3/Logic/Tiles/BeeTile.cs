using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BeeTile : Tile
    {
        private const string GeneralizedLayer = "Bee";
        
        public BeeTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Bee;
            State |= TileState.Bee;
            AddMandatoryParamsTile();
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
    }
}