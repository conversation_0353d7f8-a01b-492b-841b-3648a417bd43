using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BigMonkeyTile : MonkeyTile
    {
        private const string GeneralizedLayer = "BigMonkey";
        
        public BigMonkeyTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.BigMonkey;
            State = (State & ~TileState.Monkey) | TileState.BigMonkey;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
    }
}