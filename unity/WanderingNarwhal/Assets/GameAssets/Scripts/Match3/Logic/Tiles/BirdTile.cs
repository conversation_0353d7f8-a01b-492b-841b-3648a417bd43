using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BirdTile : Tile
    {
        private const int BirdHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.Bird, BirdHp);
        private const string GeneralizedLayer = "Bird";
        
        public BirdTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) : base(id,tileAsset,tileOrigin,tileKind,tileParams)
        {
            Speciality = TileSpeciality.Bird;
            State |= TileState.Bird;
            AddMandatoryParamsTile();
        }
        
        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
    }
}