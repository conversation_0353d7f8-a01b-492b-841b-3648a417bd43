using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ColumnBreakerTile : Tile
    {
        private const string GeneralizedLayer = "VerticalLb";
        
        public ColumnBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Kind = TileKinds.None;
            Speciality = TileSpeciality.ColumnBreaker;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.TapOrSwap;
            BoostersApplicability = BoosterItem.All & ~BoosterItem.CreateBomb;
            State |= TileState.LineBreaker;
            AddMandatoryParamsTile();
        }

        public override void CheckInvalidStates(ILevel level, Cell cell)
        {
            base.CheckInvalidStates(level, cell);

            if (IsAnyOf(TileState.ArmorMod))
            {
                BDebug.LogError(LogCat.Match3, $"Chained booster found on level '{level.Config.Uid}'");
            }
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
    }
}