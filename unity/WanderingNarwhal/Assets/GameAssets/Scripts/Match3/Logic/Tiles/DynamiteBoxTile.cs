using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class DynamiteBoxTile : Tile
    {
        private const int NumberOfSticks = 8;
        private const int BottomRowStickLimit = 4;
        private (long, int value) _assistState = ((long)GoalType.DynamiteStick, DefaultHp);
        private const string GeneralizedLayer = "DynamiteStick";

        public DynamiteBoxTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind,
            List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.DynamiteBox;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.DynamiteBox;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
        
        public override bool HasDieReaction()
        {
            return false;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (TryApplyDynamiteBoxTileDamage(simulationContext.InputParams, hitContext.MainCell, hitContext.Hit,
                    hitContext.HitWaitParams.DamageSource, simulationContext.Handler))
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            var reducedAdjacentHp = GetParam(TileParamEnum.DynamiteSticksCount);
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, reducedAdjacentHp) },
                hitContext.HitWaitParams));
            
            return true;
        }

        private static void RemoveRandomDynamiteStick(ref int state, ref int count)
        {
            var indices = new List<int>();
            for (var i = 0; i < NumberOfSticks; i++)
            {
                var subState = DynamiteBoxTileLayer.GetColorNumFromState(state, i);
                if (subState != 0 && i < BottomRowStickLimit)
                {
                    indices.Add(i);
                }
            }
            
            if (indices.Count == 0)
            {
                for (var i = BottomRowStickLimit; i < NumberOfSticks; i++)
                {
                    var subState = DynamiteBoxTileLayer.GetColorNumFromState(state, i);
                    if (subState != 0)
                    {
                        indices.Add(i);
                    }
                }
            }
            
            if (indices.Count == 0) return;

            var chosenIndex = indices[RandomSystem.Next(indices.Count)];
            var colorNum = DynamiteBoxTileLayer.GetColorNumFromState(state, chosenIndex);
            var newState = DynamiteBoxTileLayer.RemoveColorFromState(state, colorNum);
            
            if (newState == state) return;
            
            state = newState;
            count--;
        }

        private bool TryApplyDynamiteBoxTileDamage(SimulationInputParams inputParams, Cell cell, Hit hit,
            DamageSource damageSource, IRootSimulationHandler handler)
        {
            if (inputParams.InitialLoop) return false;
            if (!HasParam(TileParamEnum.DynamiteBoxColors)) return false;

            var dynamiteSticksCount = GetParam(TileParamEnum.DynamiteSticksCount);
            var count = dynamiteSticksCount;
            var state = GetParam(TileParamEnum.DynamiteBoxColors);
            var damageColorNum = DynamiteBoxTileLayer.ColorToEncodedNum(hit.SourceKind);

            if ((damageSource & DamageSource.AdjacentGeneral) != 0 && damageColorNum != 0)
            {
                var newState = DynamiteBoxTileLayer.RemoveColorFromState(state, damageColorNum);
                if (newState != state)
                {
                    state = newState;
                    count--;
                }
            }
            else
            {
                RemoveRandomDynamiteStick(ref state, ref count);
            }

            if (count >= dynamiteSticksCount) return false;
            SetParam(TileParamEnum.DynamiteBoxColors, state);
            SetParam(TileParamEnum.DynamiteSticksCount, count);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(TileParamEnum.AdjacentHp, count),
                new(TileParamEnum.DynamiteSticksCount, count),
                new(TileParamEnum.DynamiteBoxColors, state)
            };

            handler.AddAction(new ActionChangeTileParam(Id, cell.Coords, tileParamList, hit.GetHitParams()));
            return true;
        }

        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind,
            DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) &&
                HasParam(TileParamEnum.DynamiteBoxColors))
            {
                var damageColorNum = DynamiteBoxTileLayer.ColorToEncodedNum(damageTileKind);
                var state = GetParam(TileParamEnum.DynamiteBoxColors);
                if (!DynamiteBoxTileLayer.IsColorStateExistInState(state, damageColorNum))
                {
                    return false;
                }
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
    }
}