using BBB;
using BBB.CellTypes;
using BBB.Match3;
using BBB.Match3.Renderer;
using System.Collections.Generic;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class FlowerPotTile : Tile
    {
        private const int FlowerPotPurpleGrassSpawnCount = 9; //3x3 purple grass
        private static readonly (long, int) PetalAssistState = ((long) GoalType.Backgrounds, FlowerPotPurpleGrassSpawnCount);
        private (long, int value) _assistState = ((long) GoalType.FlowerPot, DefaultHp);
        private const string FlowerPotOne = "FlowerPotOne";
        private const string FlowerPotTwo = "FlowerPotTwo";
        private static readonly int[] FlowerPotSpreadPositions =
        {
            0, 1, 2,
            3, 4, 5,
            6, 7, 8
        };

        public FlowerPotTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.FlowerPot;
            State |= TileState.FlowerPot;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.FlowerPotLayerCount);
            yield return _assistState;
            yield return PetalAssistState;
        }

        public override void AddMandatoryParamsTile()
        {
            if (GetParam(TileParamEnum.FlowerPotLayerCount) <= 0)
            {
                BDebug.LogError(LogCat.Match3, "Spawned flower pot tile doesn't have flower pot layer parameter!");
                SetParam(TileParamEnum.FlowerPotLayerCount, 2);
            }

            base.AddMandatoryParamsTile();
        }
        
        public override bool SpawnsCellBackgrounds()
        {
            return true;
        }
        
        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var flowerPotLayerCount = GetParam(TileParamEnum.FlowerPotLayerCount);

            if (flowerPotLayerCount >= 1)
            {
                yield return FlowerPotOne;
            }

            if (flowerPotLayerCount >= 2)
            {
                yield return FlowerPotTwo;
            }
        }

        public override (CellState cellState, GoalType goalType, FxType fxType, int[] positionList, int positionCount,
            Coords minOffset, Coords maxOffset) GetSpawnConfiguration()
        {
            return (CellState.Petal, GoalType.Petal, FxType.PetalAnticipation, FlowerPotSpreadPositions,
                FlowerPotPurpleGrassSpawnCount, new Coords(-1, -1), new Coords(1, 1));
        }
    }
}