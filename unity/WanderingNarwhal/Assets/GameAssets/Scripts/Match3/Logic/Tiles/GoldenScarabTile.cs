using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class GoldenScarabTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.GoldenScarab, DefaultHp);
        private const string GoldenScarabFull = "GoldenScarabFull";
        private const string GoldenScarabEmpty = "GoldenScarabEmpty";
        
        
        public GoldenScarabTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.GoldenScarab;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.GoldenScarab;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }
            var reducedAdjacentHp = adjacentHp - 1;
    
            // Extract to local variables that can be passed by ref
            var coordsOffset = hitContext.CoordsOffset;
            var skin = hitContext.Skin;
    
            if (TryApplyGoldenScarabDamage(simulationContext.Grid, simulationContext.InputParams, hitContext.MainCell,
                    hitContext.Cell.Coords, ref coordsOffset, hitContext.Hit,
                    hitContext.HitWaitParams, reducedAdjacentHp, simulationContext.GoalSystem, ref skin,
                    simulationContext.Handler, simulationContext.ReactionHandler, simulationContext.PopSystem))
            {
                // Update the context properties with any changes made to the ref variables
                hitContext.CoordsOffset = coordsOffset;
                hitContext.Skin = skin;
                return true;
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        private bool TryApplyGoldenScarabDamage(Grid grid, SimulationInputParams inputParams, Cell mainCell,
            Coords coords, ref Coords coordsOffset, Hit hit, HitWaitParams hitWaitParams,
            int reducedAdjacentHp, GoalsSystem goalsSystem, ref int? skin, IRootSimulationHandler handler,
            TileHitReactionHandler reactionHandler, PopSystem popSystem)
        {
            if (!HasParam(TileParamEnum.GoldenScarabCount)) return false;

            var count = GetParam(TileParamEnum.GoldenScarabCount);
            if (count == 1 && reducedAdjacentHp == 0)
            {
                SetParam(TileParamEnum.AdjacentHp, 0);
                SetParam(TileParamEnum.GoldenScarabCount, 0);

                goalsSystem.TryReduceGoalIfNeeded(GoalType.GoldenScarab);
                handler.AddAction(new ActionCollectGoal(new GoalTypeTagPair(GoalType.GoldenScarab), coords, Id, skin, coordsOffset,
                    hitWaitParams, TileKinds.None));

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    new(TileParamEnum.AdjacentHp, 0),
                    new(TileParamEnum.GoldenScarabCount, 0)
                };

                handler.AddAction(new ActionChangeTileParam(Id, mainCell.Coords, tileParamList, hitWaitParams));
                RemoveAllScarabs(grid, handler, goalsSystem, popSystem, reactionHandler, inputParams);
                return true;
            }

            if (reducedAdjacentHp <= 0) return false;
            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            handler.AddAction(new ActionChangeTileParam(Id, mainCell.Coords,
                new List<(TileParamEnum, int)> {new(TileParamEnum.AdjacentHp, reducedAdjacentHp)},
                hit.GetHitParams()));
            return true;
        }

        private static void RemoveAllScarabs(Grid grid, IRootSimulationHandler events, GoalsSystem goalSystem,
            PopSystem popSystem, TileHitReactionHandler reactionHandler, SimulationInputParams inputParams)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.GoldenScarab);
            var isOutOfScarabs = targetLeft <= 0;

            if (!isOutOfScarabs) return;
            
            var allScarabs = new List<Coords>();

            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.GoldenScarabMod))
                {
                    allScarabs.Add(cell.Coords);
                }
            }

            if (allScarabs.Count == 0) return;

            events.AddAction(new ActionSyncCoords(allScarabs));
            foreach (var scarabCoords in allScarabs)
            {
                var cell = grid.GetCell(scarabCoords);
                popSystem.KillTile(grid, reactionHandler, inputParams, goalSystem, null,
                    null, cell, new HitWaitParams(), false);
            }
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var goldenScarabCount = GetParam(TileParamEnum.GoldenScarabCount);
            
            if (goldenScarabCount >= DefaultHp)
            {
                yield return GoldenScarabFull;
            }
            
            yield return GoldenScarabEmpty;
        }
    }
}