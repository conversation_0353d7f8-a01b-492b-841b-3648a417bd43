using BBB;
using BBB.Match3;
using System.Collections.Generic;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class HenTile : Tile
    {
        private const int ChickenSpawnCount = 3;
        private const int MaxChickenSkinIndex = 2;
        private (long, int value) _assistState = ((long)GoalType.Chicken, DefaultHp);
        private const string HenOne = "HenOne";
        private const string HenTwo = "HenTwo";

        public HenTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Hen;
            State |= TileState.Hen;
            AddMandatoryParamsTile();
        }

        public override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int)TileAsset.Chicken);
            SetParam(TileParamEnum.TileCreateCountForReaction, ChickenSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp) + ChickenSpawnCount;
            yield return _assistState;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            if (HasParam(TileParamEnum.TileToSpawnFromReaction))
            {
                var tileAsset = (TileAsset)GetParam(TileParamEnum.TileToSpawnFromReaction);
                var tileParams = new List<TileParam>
                {
                    new()
                    {
                        Param = TileParamEnum.Skin,
                        Value = Mathf.Clamp(index, 0, MaxChickenSkinIndex)
                    }
                };

                var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell),
                    TileKinds.None, tileParams);
                return (newTile, tileAsset, GoalType.None);
            }

            return base.CreateTileFromReaction(id, cell, index);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return HenOne;
            }

            if (adjacentHp >= 2)
            {
                yield return HenTwo;
            }
        }
    }
}