using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class IceBarTile : BarTile
    {
        private (long, int value) _assistState = ((long) GoalType.IceBar, DefaultHp);
        private const string GeneralizedLayer = "IceBar";
        
        public IceBarTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.IceBar;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.IceBar;
            AddMandatoryParamsTile();
            BarOrientation = TileParamEnum.IceBarOrientation;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
        
        public static void HandleIceBar(Grid grid, IRootSimulationHandler events)
        {
            foreach (var c in grid.Cells)
            {
                var mainCell = c.GetMainCellReference(out _);
                if (!mainCell.Tile.IsNull() && mainCell.Tile.Speciality == TileSpeciality.IceBar) continue;

                if (!c.IsAnyOf(CellState.IceBar) && !c.IceBarStatus) continue;
                c.IceBarStatus = false;
                c.Remove(CellState.IceBar);
                events.AddAction(new ActionFreeUpIceBarCells(c.Coords));
            }
        }
    }
}