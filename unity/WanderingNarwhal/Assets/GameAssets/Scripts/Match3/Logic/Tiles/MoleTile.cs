using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MoleTile : Tile
    {
        private const string MoleShown = "MoleShown";
        private const string MoleHidden = "MoleHidden";
        
        public MoleTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Mole;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Mole;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            
            if (adjacentHp >= 1)
            {
                yield return MoleShown;
            }
            
            if (adjacentHp >= 2)
            {
                yield return MoleHidden;
            }
        }
    }
}