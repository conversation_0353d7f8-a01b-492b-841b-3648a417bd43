using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class PouchTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.Pouch, DefaultHp);
        private const string GeneralizedLayer = "Pouch";

        public PouchTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Pouch;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Pouch;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var configuration = simulationContext.InputParams.Settings.PowerUpSpawnOutcomeConfiguration;
            var rewardConfiguration = configuration.PowerUpSpawnOutcomeConfigurations[Asset];
            TileSpawnHandler.HandlePowerUpSpawnOutcome(this, simulationContext, hitContext, rewardConfiguration);
            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);
            var tileAsset = tileAssets[index];
            var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));

            return (newTile, tileAsset, GoalType.None);
        }
        
        public override bool HasSpawnsTileStartDelay()
        {
            return false;
        }
        
        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
    }
}