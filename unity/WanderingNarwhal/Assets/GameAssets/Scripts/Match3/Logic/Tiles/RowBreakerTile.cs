using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class RowBreakerTile : Tile
    {
        private const string GeneralizedLayer = "HorizontalLb";
        
        public RowBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Kind = TileKinds.None;
            Speciality = TileSpeciality.RowBreaker;
            BoostersApplicability = BoosterItem.All & ~BoosterItem.CreateBomb;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.TapOrSwap;
            State |= TileState.LineBreaker;
            AddMandatoryParamsTile();
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override void CheckInvalidStates(ILevel level, Cell cell)
        {
            base.CheckInvalidStates(level, cell);

            if (IsAnyOf(TileState.ArmorMod))
            {
                BDebug.LogError(LogCat.Match3, $"Chained booster found on level '{level.Config.Uid}'");
            }
        }
    }
}