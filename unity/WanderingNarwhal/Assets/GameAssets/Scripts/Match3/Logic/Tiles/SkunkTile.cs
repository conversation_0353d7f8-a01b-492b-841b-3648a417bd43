using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SkunkTile : Tile
    {
        private const string GeneralizedLayer = "Skunk";
        
        public SkunkTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Skunk;
            State |= TileState.Skunk;
            AddMandatoryParamsTile();
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (simulationContext.CellsToDamageQueue == null)
            {
                return true;
            }

            var removeRandomTileQueue = SpecialTileSystem.RemoveWithSkunkHit(this, hitContext.Hit, hitContext.Cell,
                hitContext.HitWaitParams, simulationContext.SettleTileSystem, simulationContext.Handler);

            if (removeRandomTileQueue != null)
            {
                simulationContext.CellsToDamageQueue.Append(removeRandomTileQueue);
            }
            return true;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }

        public override bool PreventBackgroundInteraction()
        {
            return true;
        }
        
        public override bool IsIndestructibleBlocker()
        {
            return true;
        }

        public override void CheckInvalidStates(ILevel level, Cell cell)
        {
            base.CheckInvalidStates(level, cell);

            if (HasParam(TileParamEnum.RestoresCount))
            {
                BDebug.LogError(LogCat.Match3,
                    $"Level '{level.Config.Uid}' contains Skunk with {GetParam(TileParamEnum.RestoresCount)} HP parameter");
            }
        }
    }
}