using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SlotMachineTile : Tile
    {
        private const int SlotMachineHp = 4;
        private (long, int value) _assistState = ((long) GoalType.SlotMachine, DefaultHp);
        private const string SlotMachine = "SlotMachine";
        private const string SlotMachineCover = "SlotMachineCover";
        
        public SlotMachineTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.SlotMachine;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.SlotMachine;
            ShouldDelaySimulationOnReaction = true;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (GetParam(TileParamEnum.TileCreateCountForReaction) == 0)
            {
                var m3Settings = simulationContext.InputParams.Settings;
                var configuration = m3Settings.PowerUpSpawnOutcomeConfiguration;
                var rewardConfiguration = configuration.PowerUpSpawnOutcomeConfigurations[Asset];

                var rewardConfigurations = rewardConfiguration.RewardConfigurations;
                var keysToRemove = new List<TileAsset>();

                foreach (var kvp in rewardConfigurations)
                {
                    if (simulationContext.Grid.GetSlotMachineRewardCount(kvp.Key) >=
                        m3Settings.SlotMachineMaxSameSpawnsPerLevel)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    rewardConfigurations.Remove(key);
                }

                TileSpawnHandler.HandlePowerUpSpawnOutcome(this, simulationContext, hitContext, rewardConfiguration);

                var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);

                foreach (var tileAsset in tileAssets)
                {
                    simulationContext.Handler.AddAction(new ActionUpdateSlotRewards(tileAsset));
                }
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }
        
        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);
            var tileAsset = tileAssets[index];
            var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));

            return (newTile, tileAsset, GoalType.None);
        }

        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind,
            DamageSource totalAllowedDamageSource)
        {
            if ((damageSource & (DamageSource.AdjacentGeneral | DamageSource.RemoveColorTiles)) != 0)
            {
                if (GetParam(TileParamEnum.AdjacentHp) == SlotMachineHp)
                {
                    return false;
                }
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            yield return adjacentHp >= SlotMachineHp ? SlotMachineCover : SlotMachine;
        }
    }
}