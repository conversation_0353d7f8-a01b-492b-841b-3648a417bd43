using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class StickerTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.Stickers, DefaultHp);
        private const string CrateOne = "CrateOne";
        private const string CrateTwo = "CrateTwo";
        private const string CrateThree = "CrateThree";
        
        public StickerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Sticker;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
            State |= TileState.Sticker;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return CrateOne;
            }

            if (adjacentHp >= 2)
            {
                yield return CrateTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return CrateThree;
            }
        }
    }
}