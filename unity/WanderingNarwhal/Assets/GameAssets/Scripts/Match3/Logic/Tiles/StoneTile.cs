using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class StoneTile : Tile
    {
        public const int MinStoneHp = 1;
        public const int MaxStoneHp = 3;
        
        private (long, int value) _assistState = ((long) GoalType.Stone, DefaultHp);
        private const string StoneOne = "StoneOne";
        private const string StoneTwo = "StoneTwo";
        private const string StoneThree = "StoneThree";
        
        public StoneTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Stone;
            AllowedDamageSource = DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk |
                                  DamageSource.FireWorks;
            State |= TileState.Stone;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return StoneOne;
            }

            if (adjacentHp >= 2)
            {
                yield return StoneTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return StoneThree;
            }
        }
    }
}