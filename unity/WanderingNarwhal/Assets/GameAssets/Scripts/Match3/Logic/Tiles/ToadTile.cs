using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ToadTile : Tile
    {
        private const string GeneralizedLayer = "Toad";
        private static readonly List<Coords> AllToads = new(5);
        
        public ToadTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Toad;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Toad;
            AddMandatoryParamsTile();
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
        
        public static void MarkToadsOnGridOutOfFliesIfNeeded(Grid grid, IRootSimulationHandler events,
            GoalsSystem goalSystem,
            PopSystem popSystem, TileHitReactionHandler reactionHandler, SimulationInputParams inputParams)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.Toad);
            var isOutOfFlies = targetLeft <= 0;

            if (!isOutOfFlies) return;

            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.ToadMod))
                {
                    AllToads.Add(cell.Coords);
                }
            }

            if (AllToads.Count == 0) return;

            reactionHandler.NewBusyCells ??= new List<Cell>(3);

            events.AddAction(new ActionSyncCoords(new List<Coords>(AllToads)));
            foreach (var toadCoords in AllToads)
            {
                var cell = grid.GetCell(toadCoords);
                popSystem.KillTile(grid, reactionHandler, inputParams, goalSystem, null,
                    null, cell, new HitWaitParams(), false);

                reactionHandler.NewBusyCells.Add(cell);
            }

            AllToads.Clear();
        }
    }
}