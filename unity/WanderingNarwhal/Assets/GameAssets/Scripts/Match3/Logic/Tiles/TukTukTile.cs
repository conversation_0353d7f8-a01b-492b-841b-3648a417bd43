using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class TukTukTile : Tile
    {
        private static int _tukTukId;
        private const string GeneralizedLayer = "TukTuk";
        
        public TukTukTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.TukTuk;
            State |= TileState.TukTuk;
            AddMandatoryParamsTile();
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var count = GetParam(TileParamEnum.TukTukCount);
            var damageSource = hitContext.HitWaitParams.DamageSource;
            if (count == 0 && damageSource == DamageSource.TukTuk)
            {
                MarkAsDead();
                return false;
            }

            return true;
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            return (State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0;
        }
        
        public static void NotifyTukTuk(IRootSimulationHandler events,
            Cell tuktuk, Coords sourceCoords, HitWaitParams hitWaitParams)
        {
            var currentCount = tuktuk.Tile.GetParam(TileParamEnum.TukTukCount);
            if(currentCount <= 0) return;
            var reducedCount = --currentCount;
            tuktuk.Tile.SetParam(TileParamEnum.TukTukCount, reducedCount);

            if (reducedCount <= 0)
            {
                TileCollectorsHandler.AddToDieReactionCache(tuktuk);
            }
            events.AddAction(new ActionReduceTukTukCount(tuktuk.Coords, sourceCoords, hitWaitParams));
        }
        
        public static List<Cell> HandleTukTukDamage(Cell cell, Grid grid, IRootSimulationHandler events, 
            Queue queue, HitWaitParams hitWaitParams)
        {
           var busyCells = new List<Cell>();
            
            if (!cell.HasTile() || cell.Tile.Speciality != TileSpeciality.TukTuk) return busyCells;
            var currentCount = cell.Tile.GetParam(TileParamEnum.TukTukCount);
            if (currentCount > 0) return busyCells;

            var currentColor = cell.Tile.GetParam(TileParamEnum.TukTukColor);

            var currentDirection = cell.Tile.GetParam(TileParamEnum.TukTukOrientation);

            var cardinalDirection = currentDirection switch
            {
                0 => CardinalDirections.E,
                90 => CardinalDirections.N,
                270 => CardinalDirections.S,
                180 => CardinalDirections.W,
                _ => CardinalDirections.E
            };

            var allCoords = CardinalDirectionsHelper.GetCoordsInDirection(cell.Coords.X, cell.Coords.Y, cardinalDirection, grid);
            _tukTukId++;
            
            events.AddAction(new ActionMoveTukTuk(cell.Coords, currentColor, cardinalDirection, _tukTukId, allCoords, hitWaitParams));
               
            foreach (var coords in allCoords)
            {
                if (!grid.TryGetCell(coords, out var damageCell)) continue;
                    
                var damageMainCell = damageCell.GetMainCellReference(out _);
                
                var currentHp = 0;

                if (damageMainCell.HasTile())
                {
                    // Skip other TukTuk tiles in the path
                    if (cell != damageMainCell && damageMainCell.HasTile() &&
                        damageMainCell.Tile.Speciality == TileSpeciality.TukTuk)
                    {
                        continue;
                    }
                    
                    currentHp = damageMainCell.Tile.GetParam(TileParamEnum.AdjacentHp);

                    var isArmouredTile = damageMainCell.Tile.HasAnyArmor();
                    if (isArmouredTile)
                    {
                        foreach (var tileParam in ArmorParams)
                        {
                            if (damageMainCell.Tile.HasParam(tileParam))
                            {
                                currentHp += damageMainCell.Tile.GetParam(tileParam);
                            }

                            if (tileParam == TileParamEnum.VaseLayerCount)
                            {
                                currentHp++;
                            }
                        }
                    }

                    if (damageMainCell.Tile.HasParam(TileParamEnum.BowlingOpened) &&
                        damageMainCell.Tile.GetParam(TileParamEnum.BowlingOpened) == 0)
                    {
                        currentHp++;
                    }

                    if (damageMainCell.Tile.IsBoost)
                    {
                        currentHp++;
                    }

                    if (damageMainCell.Tile.Speciality is TileSpeciality.Monkey or TileSpeciality.BigMonkey)
                    {
                        currentHp += damageMainCell.Tile.GetParam(TileParamEnum.RestoresCount);
                    }

                    if (damageMainCell.Tile.Speciality == TileSpeciality.Squid)
                    {
                        currentHp += damageMainCell.Tile.GetParam(TileParamEnum.SquidsCount);
                    }

                    if (currentHp == 0 && damageMainCell.Tile.Speciality != TileSpeciality.DropItem)
                    {
                        currentHp++;
                    }
                }

                if (damageMainCell.IvyCount > 0)
                {
                    currentHp += damageMainCell.IvyCount;
                }

                if (damageMainCell.BackgroundCount > 0)
                {
                    currentHp += damageMainCell.BackgroundCount;
                }

                var hitSourceUid = 0;
                
                for (var i = 0; i < currentHp; i++)
                {
                    var hit = new Hit<Cell>(
                        null,
                        damageMainCell,
                        1,
                        DamageSource.TukTuk,
                        coords: damageMainCell.Coords,
                        boostInfo: new TukTukInfo(_tukTukId, cardinalDirection),
                        hitSourceUid: ++hitSourceUid);
                    queue.AppendHit(1, hit);
                    busyCells.Add(damageMainCell);
                }

                if (damageCell == damageMainCell) continue;
                if(damageMainCell.HasTile() && damageMainCell.Tile.SpawnsCellBackgrounds()) continue;
                {
                    var damageCellHp = 0;

                    if (damageCell.IvyCount > 0)
                    {
                        damageCellHp += damageCell.IvyCount;
                    }

                    if (damageCell.BackgroundCount > 0)
                    {
                        damageCellHp += damageCell.BackgroundCount;
                    }

                    var damageCellHitSourceId = 0;
                    for (var i = 0; i < damageCellHp; i++)
                    {
                        var hit = new Hit<Cell>(
                            null,
                            damageCell,
                            1,
                            DamageSource.TukTuk,
                            coords: damageCell.Coords,
                            boostInfo: new TukTukInfo(_tukTukId, cardinalDirection),
                            hitSourceUid: ++damageCellHitSourceId);
                        queue.AppendHit(2, hit);
                        busyCells.Add(damageCell);
                    }
                }
            }
            
            return busyCells;
        }
    }
}