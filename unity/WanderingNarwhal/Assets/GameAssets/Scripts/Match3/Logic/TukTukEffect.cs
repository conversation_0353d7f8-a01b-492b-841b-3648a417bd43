using UnityEngine;
using BBB.Core;
using BBB.Core.Volume;
using Spine.Unity;

namespace BBB
{
    public sealed class TukTukEffect : ActivePoolItem
    {
        [SerializeField] private VolumeBase _volume;
        [SerializeField] private SkeletonGraphic _tukTukSkeletonGraphic;
        private RectTransform _rectTransform;

        public void Init(Vector2 dir, int color)
        {
            float angle;

            if (dir.y == 0f)
            {
                angle = dir.x > 0f ? 0f : 180f;
            }
            else
            {
                angle = dir.y > 0f ? 90f : -90f;
            }

            _tukTukSkeletonGraphic.Skeleton.SetSkin(((TileKinds)color).GetSkinColor());
            
            _rectTransform.localEulerAngles = new Vector3(0, 0, angle);
        }

        public bool OverlapPoint(Vector2 point)
        {
            return _volume.OverlapPoint(point);
        }

        public override void OnInstantiate()
        {
            _rectTransform = (RectTransform)transform;
            base.OnInstantiate();
        }
    }
}
