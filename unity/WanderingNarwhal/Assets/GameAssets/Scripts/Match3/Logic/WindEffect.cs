using BebopBee.Core;
using UnityEngine;

namespace BBB
{
    public class WindEffect : BbbMonoBehaviour, IPoolItem
    {
        [SerializeField] private TransformFollower _follower;
        [SerializeField] private ParticleSystem _particleSystem;
        
        public void Init(Transform followTarget)
        {
            _follower.FollowThis(followTarget);
            
            _particleSystem.Play();

            Rx.Invoke(1.5f, _ =>
            {
                _particleSystem.Stop();
                _follower.FollowThis(null);
            });
        }
        
        public void OnInstantiate()
        {
            gameObject.SetActive(false);
        }
        
        public void OnSpawn()
        {
            gameObject.SetActive(true);
        }

        public void OnRelease()
        {
            gameObject.SetActive(false);
        }

    }
}