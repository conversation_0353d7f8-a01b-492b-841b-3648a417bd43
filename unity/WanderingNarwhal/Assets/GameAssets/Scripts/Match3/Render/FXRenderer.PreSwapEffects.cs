using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public sealed partial class FxRenderer : IPreSwapInitialtedEffectRenderer
    {
        private readonly Dictionary<PreSwapInitialtedEffectId, IPreSwapInitiatedEffect>
            _preSwapInitialtedEffects = new Dictionary<PreSwapInitialtedEffectId, IPreSwapInitiatedEffect>(); 
            
        bool IPreSwapInitialtedEffectRenderer.LaunchPreSwapInitiatedEffect(
            Coords firstCoord, Coords secondCoord,
            TileView firstTileView,
            TileView secondTileView)
        {
            var grid = _gameController.Grid;
            if (grid.TryGetCell(firstCoord, out var firstCell) 
                && grid.TryGetCell(secondCoord, out var secondCell)
                && firstCell.HasTile() && secondCell.HasTile())
            {
                var firstSpec = firstCell.Tile.Speciality;
                var secondSpec = secondCell.Tile.Speciality;

                bool firstSpecIsHigherInPriority = TileSpecialityHelper.TileSpecialityComparison(firstSpec, secondSpec);

                var highSpec = firstSpecIsHigherInPriority ? firstSpec : secondSpec;
                var lowSpec = firstSpecIsHigherInPriority ? secondSpec : firstSpec;

                switch (highSpec)
                {
                    case TileSpeciality.Propeller:
                    {
                        switch (lowSpec)
                        {
                            case TileSpeciality.Bomb:
                            case TileSpeciality.RowBreaker:
                            case TileSpeciality.ColumnBreaker:
                            {
                                var highCoord = firstSpecIsHigherInPriority ? firstCoord : secondCoord;
                                var lowCoord = firstSpecIsHigherInPriority ? secondCoord : firstCoord;

                                var highTileView = firstSpecIsHigherInPriority ? firstTileView : secondTileView;
                                var lowTileView = firstSpecIsHigherInPriority ? secondTileView : firstTileView;
                                LaunchPropellerCombo(highCoord, 
                                    lowCoord, 
                                    highSpec, 
                                    lowSpec, 
                                    highTileView.GetSpecialRendererGlobalScale(), 
                                    lowTileView.GetSpecialRendererGlobalScale());
                                return true;
                            }
                        }
                        break;
                    }
                }
            }
            return false;
        }

        private void LaunchPropellerCombo(Coords propellerCoord, 
            Coords otherCoord,
            TileSpeciality propellerSpec,
            TileSpeciality otherSpec, 
            Vector3 propellerRendererGlobalScale,
            Vector3 otherRendererGlobalScale)
        {
            var ctrl = _rendererContainers.SpawnFx<PropellerComboFlightEffect>(FxType.PropellerComboFlight);
            var propellerLocalCoord = _gridController.ToLocalPosition(propellerCoord);
            var otherLocalCoord = _gridController.ToLocalPosition(otherCoord);

            var propellerWorldPosition = _gridController.ToDisplacedWorldPosition(propellerCoord.ToUnityVector2());
            var otherWorldPosition = _gridController.ToDisplacedWorldPosition(otherCoord.ToUnityVector2());
            
            ctrl.transform.localPosition = (propellerLocalCoord + otherLocalCoord) * 0.5f;

            var dir = CardinalDirectionsHelper.GetCardinalDirectionFromCoords(propellerCoord, otherCoord);

            var settings = _settings.GetSettingsForEffect<PropellerComboFlightEffectSettings>(FxType.PropellerComboFlight);
            ctrl.LaunchRotation(propellerWorldPosition, otherWorldPosition, 
                propellerSpec, otherSpec, 
                propellerRendererGlobalScale, otherRendererGlobalScale, dir, settings);
            
            var propellerEffect = new PropellerComboEffect(ctrl);

            _preSwapInitialtedEffects[new PreSwapInitialtedEffectId(propellerCoord, otherCoord)] = propellerEffect;
        }
    }
}