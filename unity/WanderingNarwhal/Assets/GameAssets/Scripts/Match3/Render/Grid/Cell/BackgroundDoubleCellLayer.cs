using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class BackgroundDoubleCellLayer : CellLayerBase
    {
        public override CellOverlayType OverlayType { get { return CellOverlayType.StandardOverlay; } }
        public override CellLayerState State { get { return CellLayerState.BackDouble; } }

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.BackDouble) && cell.BackgroundCount == 2;
        }
    }
}