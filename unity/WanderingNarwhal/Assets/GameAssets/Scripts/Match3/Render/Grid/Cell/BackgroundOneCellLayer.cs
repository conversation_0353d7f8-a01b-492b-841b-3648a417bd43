using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class BackgroundOneCellLayer : CellLayerBase
    {
        public override CellOverlayType OverlayType { get { return CellOverlayType.StandardOverlay; } }
        public override CellLayerState State { get { return CellLayerState.BackOne; } }

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.BackOne) && cell.BackgroundCount == 1;
        }
    }
}