using System;
using System.Collections.Generic;
using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using Object = UnityEngine.Object;

namespace BBB.Match3.Renderer
{
    public class CellController : ICellController, IContextInitializable, IContextReleasable, IRevealablesContainer
    {
        private readonly Dictionary<Coords, CellView> _cells = new Dictionary<Coords, CellView>();
        private Dictionary<CellLayerState, ICellLayer> _layers;
        private GoPool _pool;
        private IGridController _gridController;
        private IContext _context;
        private readonly List<CellLayerState> _cellLayersStatesTemp = new List<CellLayerState>();
        private readonly List<ICellLayer> _cellLayersTemp = new List<ICellLayer>();
        private readonly Comparison<ICellLayer> _sorting = (a, b) => a.CurrentOrder.CompareTo(b.CurrentOrder);
        private readonly Dictionary<CellOverlayType, GoPool> _overlayPools = new Dictionary<CellOverlayType, GoPool>();

        public void InsertRevealablesInto(List<IRevealableBoardElement> list)
        {
            if (list == null) return;
            foreach (var item in _cells)
            {
                list.Add(item.Value);
            }
        }

        public void InitializeByContext(IContext context)
        {
            if (_layers == null)
            {
                _layers = new Dictionary<CellLayerState, ICellLayer>();
                var layersList = context.Resolve<IList<ICellLayer>>();
                foreach (var layer in layersList)
                {
                    _layers[layer.State] = layer;
                }

                _gridController = context.Resolve<IGridController>();
            }

            var resources = context.Resolve<TilesResources>();
            var floorsContainer = context.Resolve<RendererContainers>().FloorsContainer;
            _pool ??= new GoPool(resources.CellPrefab, floorsContainer, 81);
            _context = context;
        }

        public CellView GetCellView(Coords coords, bool noError = false)
        {
            CellView result = null;
            if (!_cells.TryGetValue(coords, out result) && !noError)
                UnityEngine.Debug.LogErrorFormat("Cell view not found in {0}", coords);
            return result;
        }

        public ICellLayer GetLayer(CellLayerState state)
        {
            return _layers[state];
        }

        public IEnumerable<CellLayerState> ValidateLayers(Cell cell)
        {
            _cellLayersTemp.Clear();
            foreach (var layer in _layers)
            {
                if (layer.Value.CheckIfRendererAndCustomize(cell))
                {
                    _cellLayersTemp.Add(layer.Value);
                }
            }

            _cellLayersTemp.Sort(_sorting);
            _cellLayersStatesTemp.Clear();
            foreach (var layer in _cellLayersTemp)
            {
                _cellLayersStatesTemp.Add(layer.State);
            }

            return _cellLayersStatesTemp;
        }

        public void SetGrid(Grid grid)
        {
            if (_cells.Count != 0)
                ClearGrid();

            foreach (var cell in grid.Cells)
            {
                var go = _pool.Spawn();
                var pos = _gridController.ToLocalPosition(cell.Coords);
                var cellView = new CellView(_context, go, pos, _overlayPools);
                _cells.Add(cell.Coords, cellView);
                cellView.Update(_context, cell);
            }
        }

        //use for debug only
        public void Update(Cell cell)
        {
            var cellView = _cells.GetSafe(cell.Coords);
            cellView?.Update(_context, cell);
        }
        
        public void ClearGrid(bool cleanPools = false)
        {
            foreach (var tileView in _cells.Values)
            {
                tileView.Release();
                if (!cleanPools)
                {
                    _pool.Release(tileView.GameObject);
                }
                else
                {
                    Object.Destroy(tileView.GameObject);
                }
            }

            _cells.Clear();
            if (cleanPools)
            {
                _pool.Cleanup();
                foreach (var pool in _overlayPools.Values)
                {
                    pool.Cleanup();
                }
            }
        }

        public void ReleaseByContext(IContext context)
        {
            _context = null;
        }

        /// <summary>
        /// Notify target cells about matched tiles near them.
        /// </summary>
        public void NotifyCellsAboutEnteredTile(HashSet<Coords> cells)
        {
            foreach (var c in cells)
            {
                CellView cell;
                if (_cells.TryGetValue(c, out cell))
                {
                    cell.OnTileEnteredCellCell();
                }
            }
        }

        /// <summary>
        /// Notify target cells about matched tiles near them.
        /// </summary>
        public void NotifyCellsAboutNearMatchedTiles(HashSet<Coords> cells)
        {
            foreach (var c in cells)
            {
                CellView cell;
                if (_cells.TryGetValue(c, out cell))
                {
                    cell.OnTileMatchedNearCell();
                }
            }
        }
    }
}