using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class CellLayerBase : ICellLayer, IContextInitializable
    {
        private int _currentOrder;
        public abstract CellLayerState State { get; }
        public virtual CellOverlayType OverlayType { get { return CellOverlayType.None; } }
        public Sprite Sprite { get; protected set; }
        public Material Material { get; private set; }
        public Coords Coords { get; protected set; }
        public virtual int WallCount => 0;
        public int CurrentOrder { get { return _currentOrder; } }
        
        public virtual bool IsAnimated
        {
            get { return false; }
        }

        private TilesResources TilesResources;
        protected M3Settings M3Settings;
        
        public void InitializeByContext(IContext context)
        {
            TilesResources = context.Resolve<TilesResources>();
            M3Settings = context.Resolve<M3Settings>();
            
            var data = TilesResources.Get(State);
            if (data != null)
            {
                Sprite = data.Sprite;
                Material = data.Material;
                _currentOrder = data.DefaultOrder;
            }
        }

        public bool CheckIfRendererAndCustomize(Cell cell)
        {
            var result = IsCondition(cell);
            
            if (result)
                Customize(cell);
            
            return result;
        }

        protected virtual void Customize(Cell cell)
        {
            
        }

        protected abstract bool IsCondition(Cell cell);
    }
}