using System.Collections.Generic;
using BBB.DI;
using BBB.UI.Level;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class CellLayerView
    {
        private readonly SpriteRenderer _spriteRenderer;
        private readonly GameObject _gameObject;
        private readonly RectTransform _parentTransform;
        private readonly RectTransform _selfTransform;
        private bool _isAnimated;
        private Dictionary<CellOverlayType, GoPool> _cellOverlayPools;
        private CellOverlayView _cellOverlayView;
        public CellOverlayView CellOverlayView => _cellOverlayView;
        public CellLayerState State { get; private set; }
        public bool Applied { get; private set; }

        public CellLayerView(Sprite<PERSON>enderer spriteRenderer, RectTransform selfTransform, RectTransform parentTransform, Dictionary<CellOverlayType, GoPool> cellOverlayPools)
        {
            _spriteRenderer = spriteRenderer;
            _selfTransform = selfTransform;
            _parentTransform = parentTransform;
            _cellOverlayPools = cellOverlayPools;

            if (spriteRenderer != null)
                _gameObject = spriteRenderer.gameObject;
        }

        public void Apply(CellLayerState state, ICellLayer layer, IContext context)
        {
            if (layer.OverlayType != CellOverlayType.None)
                ApplyCellOverlay(layer, context);
            else
            {
                _spriteRenderer.sprite = layer.Sprite;
                _spriteRenderer.sortingOrder = layer.CurrentOrder;
                if (layer.Material != null)
                    _spriteRenderer.material = layer.Material;
            }

            _isAnimated = layer.IsAnimated;
            State = state;
            Applied = true;
        }

        private void ApplyCellOverlay(ICellLayer layer, IContext context)
        {
            var overlayType = layer.OverlayType;
            if (overlayType != CellOverlayType.None)
            {
                var resourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
                var rendererContainers = context.Resolve<RendererContainers>();
                if (!_cellOverlayPools.TryGetValue(overlayType, out var pool))
                {
                    var prefabName = overlayType.ToPrefabName();
                    var prefab = resourceProvider.GetPrefab(prefabName);
                    pool = new GoPool(prefab, rendererContainers.FloorsOverlay, 1);
                    _cellOverlayPools[overlayType] = pool;
                }
                _cellOverlayView = pool.Spawn<CellOverlayView>();
                var tf = _cellOverlayView.GetComponent<RectTransform>();
                tf.sizeDelta = _parentTransform.sizeDelta;
                tf.localPosition = _parentTransform.localPosition;

                var canvas = _cellOverlayView.GetComponent<Canvas>();
                canvas.sortingOrder = layer.CurrentOrder;

                _cellOverlayView.InitByContext(context);
                _cellOverlayView.Init(layer);
            }
        }

        public void Animate(CellAnimation anim)
        {
            if (_isAnimated && _cellOverlayView != null)
            {
                _cellOverlayView.Animate(anim);
            }
        }

        /// <summary>
        /// Called when tile is destroyed near cell, used for visual feedback.
        /// </summary>
        public void OnTileDestroyedNear()
        {
            if (_isAnimated && _cellOverlayView != null)
            {
                _cellOverlayView.OnTileDestroyedNear();
            }
        }

        /// <summary>
        /// Called when some tile entered this cell, used for visual feedback.
        /// </summary>
        public void OnTileEntered()
        {
            if (_isAnimated && _cellOverlayView != null)
            {
                _cellOverlayView.OnTileEntered();
            }
        }

        public void OnTileTap()
        {
            if (_isAnimated && _cellOverlayView != null)
            {
                _cellOverlayView.Animate(CellAnimation.TapFeedback);
            }
        }

        public void UnApply()
        {
            Applied = false;

            if (_gameObject != null)
                _gameObject.SetActive(false);

            if (_cellOverlayView != null)
            {
                _cellOverlayPools.TryGetValue(_cellOverlayView.OverlayType, out var pool);
                _cellOverlayView.DestroySelfWithFx(pool);
                _cellOverlayView = null;
            }
        }

        public void Update(int siblingIndex, ICellLayer layer)
        {
            Applied = true;

            if (_gameObject != null)
                _gameObject.SetActive(true);

            if (_selfTransform != null)
                _selfTransform.SetSiblingIndex(siblingIndex);

            if (_cellOverlayView != null)
            {
                _cellOverlayView.transform.SetSiblingIndex(siblingIndex);
                _cellOverlayView.OnUpdate(layer);
            }
        }

        public void Release()
        {
            if (_cellOverlayView != null)
            {
                if (_cellOverlayPools.TryGetValue(_cellOverlayView.OverlayType, out var pool))
                {
                    pool.Release(_cellOverlayView.gameObject);
                }
                else
                {
                    Object.Destroy(_cellOverlayView.gameObject);
                }
                _cellOverlayView = null;
            }

            if (_gameObject != null)
            {
                Object.Destroy(_gameObject);
            }
        }

        public void Reveal(float time)
        {
            if (_spriteRenderer != null)
            {
                _spriteRenderer.DoAlpha(1.0f, time);
            }

            if (_cellOverlayView != null)
                _cellOverlayView.Reveal(time);
        }

        public void Hide()
        {
            if (_spriteRenderer != null)
            {
                var color = _spriteRenderer.color;
                color.a = 0f;
                _spriteRenderer.color = color;
            }

            if (_cellOverlayView != null)
                _cellOverlayView.Hide();
        }
        
        public void HideForTnt()
        {
            if (_cellOverlayView != null)
                _cellOverlayView.HideForTnt();
        }
    }
}