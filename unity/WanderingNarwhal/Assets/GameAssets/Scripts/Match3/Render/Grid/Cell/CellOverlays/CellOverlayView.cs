using BBB.Core;
using BBB.DI;
using BBB.MMVibrations;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public enum CellAnimation
    {
        IvyNearMatchReaction,
        IvyFallTroughReaction,
        TapFeedback,
        Preview,
        FlagEndCollect
    }

    public abstract class CellOverlayView : ActivePoolItem
    {
        [SerializeField] protected SpriteRenderer SpriteRenderer;
        public CellOverlayType OverlayType = CellOverlayType.None;
        public IVibrationsWrapper Vibrations;

        public virtual void InitByContext(IContext context)
        {
            Vibrations = context.Resolve<IVibrationsWrapper>();
        }

        public virtual void Init(ICellLayer layer)
        {
            if (SpriteRenderer != null)
            {
                SpriteRenderer.sortingOrder = layer.CurrentOrder;
            }

            OverlayType = layer.OverlayType;
        }

        public virtual void Hide()
        {
            if (SpriteRenderer == null)
                return;
            
            var color = SpriteRenderer.color;
            color.a = 0f;
            SpriteRenderer.color = color;
        }
        
        public virtual void HideForTnt()
        {
            if (SpriteRenderer == null) return;
            var color = SpriteRenderer.color;
            color.a = 0f;
            SpriteRenderer.color = color;
        }

        public virtual void Reveal(float time)
        {
            if (SpriteRenderer != null)
            {
                SpriteRenderer.DoAlpha(1.0f, time);
            }
        }

        public virtual void Animate(CellAnimation anim)
        {
        }

        public virtual void OnUpdate(ICellLayer layer)
        {
        }

        /// <summary>
        /// Called when some tile has entered cell.
        /// </summary>
        public virtual void OnTileEntered()
        {

        }

        /// <summary>
        /// Called when tile was destroyed at some small distance from cell (constant is 2).
        /// </summary>
        public virtual void OnTileDestroyedNear()
        {

        }

        /// <summary>
        /// Called when overlay is destroyed in match3 simulation.
        /// This should play destroy FX if needed.
        /// </summary>
        public virtual void DestroySelfWithFx(GoPool pool)
        {
            if (pool == null)
            {
                Destroy(gameObject);
            }
            else
            {
                pool.Release(gameObject);
            }
        }
    }
}
