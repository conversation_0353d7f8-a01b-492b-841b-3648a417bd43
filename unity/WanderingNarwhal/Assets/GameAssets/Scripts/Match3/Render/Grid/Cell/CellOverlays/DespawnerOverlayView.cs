using Bebopbee.Core;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class DespawnerOverlayView : CellOverlayView
    {
        [SerializeField] private float _relativeDistance = 0.1f;
        [SerializeField] private YoyoAnimator _yoyoAnimator;
        [SerializeField] private Image[] _arrowImages;
        
        public override void Init(ICellLayer layer)
        {
            var rectTf = GetComponent<RectTransform>();
            _yoyoAnimator.SetDistance(rectTf.sizeDelta.y * _relativeDistance);
            base.Init(layer);
        }

        public override void Hide()
        {
            foreach (var image in _arrowImages)
            {
                if (image == null) continue;
                var color = image.color;
                color.a = 0f;
                image.color = color;
            }
            base.Hide();
        }

        public override void Reveal(float time)
        {
            foreach (var image in _arrowImages)
            {
                if (image == null) continue;
                var color = image.color;
                color.a = 1f;
                image.DOColor(color, time);
            }
            base.Reveal(time);
        }
    }
}