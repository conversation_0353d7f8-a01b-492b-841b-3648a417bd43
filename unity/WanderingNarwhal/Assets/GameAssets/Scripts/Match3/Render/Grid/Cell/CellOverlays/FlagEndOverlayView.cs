using BBB.Audio;
using BebopBee.Core.Audio;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class FlagEndOverlayView : CellOverlayView
    {
        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private string _idleAnimName = "idle";
        [SerializeField] private string _collectAnimName = "win";
        [SerializeField] private ParticleSystem[] _particleSystems;

        public override void Init(ICellLayer layer)
        {
            _sk.gameObject.SetActive(true);
            _sk.AnimationState.SetAnimation(0, _idleAnimName, true);
            StopParticleSystem();
        }

        private void StopParticleSystem()
        {
            foreach (var ps in _particleSystems)
            {
                if (ps == null) continue;
                ps.Stop(false);
                ps.Clear();
                ps.gameObject.SetActive(false);
            }
        }

        private void PlayParticleSystem()
        {
            foreach (var ps in _particleSystems)
            {
                ps.gameObject.SetActive(true);
                ps.Play(false);
            }
        }
        
        public override void Animate(CellAnimation anim)
        {
            if (anim == CellAnimation.FlagEndCollect)
            {
                AudioProxy.PlaySound(Match3SoundIds.GondolaFlag);
                _sk.AnimationState.SetAnimation(0, _collectAnimName, false);
                _sk.AnimationState.Event += HandleEvent;
            }
        }

        private void HandleEvent (TrackEntry trackEntry, Spine.Event e)
        {
            if (e.Data.Name == _collectAnimName)
            {
                PlayParticleSystem();
            }
        }

        public override void Reveal(float time)
        {
            _sk.gameObject.SetActive(true);
            _sk.AnimationState.SetAnimation(0, _idleAnimName, true);
        }

        public override void Hide()
        {
            StopParticleSystem();
            _sk.gameObject.SetActive(false);
        }
    }
}