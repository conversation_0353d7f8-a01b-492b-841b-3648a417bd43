using System.Collections;
using BBB.MMVibrations.Plugins;
using BebopBee.Core.Audio;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class IvyOverlayView : CellOverlayView
    {
        [SerializeField] private string _feedbackSoundUid;

        [SerializeField] private SkeletonGraphic _sk;

        [SerializeField] private float _revealDelay = 1f;

        [SerializeField] private string _revealAnimName = "Appear";
        [SerializeField] private string _idleAnimName = "Idle";
        [SerializeField] private string _feedbackToMachAnimName = "Feedback";
        [SerializeField] private string _feedbackToFallAnimName = "Feedback_falling_through";
        private Coroutine _revealRoutine;

        public override void Init(ICellLayer layer)
        {
            _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: Random.Range(0, 3f));
        }

        protected override void OnDisable()
        {
            _revealRoutine = null;
        }

        public override void Animate(CellAnimation anim)
        {
            if (anim is not (CellAnimation.IvyNearMatchReaction or CellAnimation.IvyFallTroughReaction or CellAnimation.TapFeedback or CellAnimation.Preview))
                return;
            
            AudioProxy.PlaySound(_feedbackSoundUid);
            
            if (_sk == null || _sk.AnimationState == null)
                return;
            
            var feedbackName = anim == CellAnimation.IvyFallTroughReaction ? _feedbackToFallAnimName : _feedbackToMachAnimName;
            _sk.AnimationState.SetAnimation(0, feedbackName, loop: false);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: Random.Range(0, 3f));
        }

        [ContextMenu("Reveal")]
        private void DebugReveal()
        {
            Reveal(0);
        }

        public override void Reveal(float time)
        {
            if (_revealRoutine != null)
            {
                StopCoroutine(_revealRoutine);
            }

            if (gameObject.activeInHierarchy)
            {
                _revealRoutine = StartCoroutine(RevealRoutine(time));
            }
            else
            {
                _sk.gameObject.SetActive(true);
            }
        }

        private IEnumerator RevealRoutine(float externalTime)
        {
            var duration = externalTime == 0f ? 0f : Mathf.Max(externalTime, _revealDelay);
            float timer = 0;
            while (timer < duration)
            {
                timer += Time.deltaTime;
                yield return null;
            }

            _revealRoutine = null;
            _sk.gameObject.SetActive(true);
            _sk.AnimationState.SetAnimation(0, _revealAnimName, loop: false);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: Random.Range(0, 3f));
        }

        public override void OnTileDestroyedNear()
        {
            Animate(CellAnimation.IvyNearMatchReaction);
        }

        public override void OnTileEntered()
        {
            Animate(CellAnimation.IvyFallTroughReaction);
        }

        public override void DestroySelfWithFx(GoPool pool)
        {
            base.DestroySelfWithFx(pool);
        }

        public override void Hide()
        {
            _sk.gameObject.SetActive(false);
        }
        
        public override void HideForTnt()
        {
            _sk.gameObject.SetActive(false);
        }
        
    }
}