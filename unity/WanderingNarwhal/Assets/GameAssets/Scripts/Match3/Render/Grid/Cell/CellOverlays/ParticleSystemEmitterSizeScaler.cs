using BBB;
using UnityEngine;

namespace GameAssets.Scripts
{
    /// <summary>
    /// Component that changes PS' shape size on demand (public method call).
    /// For Spehere shapes radius parameter is changed,
    /// For Box shapes (variables called rectangles) the scale parameter is changed.
    /// Burst particles count is also scaled proportionaly.
    /// </summary>
    /// <remarks>
    /// Initial parameters of particle systems taken as their defult.
    /// When SetSize public method is called, default size is multiplied by provided arguments.
    ///
    /// Used for TNT mechanic, when FX should scale to match size of TNT tile (which may be any size from 1x1 to 5x5 or more). -VK
    /// </remarks>
    public class ParticleSystemEmitterSizeScaler : BbbMonoBehaviour
    {
        [SerializeField] private ParticleSystem[] _psSpheres;
        [SerializeField] private ParticleSystem[] _psRectangles;

        private float[] _defaultScalesSpheres;
        private int[] _defaultParticlesCountSpheres;

        private Vector3[] _defaultScalesRectangles;
        private int[] _defaultParticlesCountRectangles;

        public void SetParticlesEmitterSize(int x, int y)
        {
            if (_defaultScalesSpheres == null)
            {
                _defaultScalesSpheres = new float[_psSpheres.Length];
                for (var i = 0; i < _psSpheres.Length; i++)
                {
                    _defaultScalesSpheres[i] = _psSpheres[i].shape.radius;
                }

                _defaultParticlesCountSpheres = new int[_psSpheres.Length];
                for (int i = 0; i < _psSpheres.Length; i++)
                {
                    if (_psSpheres[i].emission.burstCount > 0)
                        _defaultParticlesCountSpheres[i] = (int)_psSpheres[i].emission.GetBurst(0).count.constant;
                }

                _defaultScalesRectangles = new Vector3[_psRectangles.Length];
                for (int i = 0; i < _psRectangles.Length; i++)
                {
                    _defaultScalesRectangles[i] = _psRectangles[i].shape.scale;
                }

                _defaultParticlesCountRectangles = new int[_psRectangles.Length];
                for (int i = 0; i < _psRectangles.Length; i++)
                {
                    if (_psRectangles[i].emission.burstCount > 0)
                        _defaultParticlesCountRectangles[i] = (int)_psRectangles[i].emission.GetBurst(0).count.constant;
                }
            }

            var sphereScaleFactor = Mathf.Min(x, y);
            var sphereParticlesCountFactor = sphereScaleFactor;

            for (int i = 0; i < _psSpheres.Length; i++)
            {
                var shape = _psSpheres[i].shape;
                shape.radius = _defaultScalesSpheres[i] * sphereScaleFactor;
                var emission = _psSpheres[i].emission;
                if (emission.burstCount > 0)
                {
                    var burst = emission.GetBurst(0);
                    burst.count = _defaultParticlesCountSpheres[i] * sphereParticlesCountFactor * 2f - _defaultParticlesCountSpheres[i];
                    emission.SetBurst(0, burst);
                }
            }

            var rectangleScaleFactorX = x;
            var rectangleScaleFactorY = y;
            var rectangleParticlesCountFactor = x * y;

            for (int i = 0; i < _psRectangles.Length; i++)
            {
                var shape = _psRectangles[i].shape;
                var defaultScale = _defaultScalesRectangles[i];
                shape.scale = new Vector3(defaultScale.x * rectangleScaleFactorX, defaultScale.y * rectangleScaleFactorY, defaultScale.z * rectangleScaleFactorY);
                var emission = _psRectangles[i].emission;
                if (emission.burstCount > 0)
                {
                    var burst = emission.GetBurst(0);
                    burst.count = _defaultParticlesCountRectangles[i] * rectangleParticlesCountFactor;
                    emission.SetBurst(0, burst);
                }
            }
        }
    }
}