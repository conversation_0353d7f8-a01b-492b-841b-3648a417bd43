using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class StandardOverlayView : CellOverlayView
    {
        public override void Init(ICellLayer layer)
        {
            SpriteRenderer.sprite = layer.Sprite;
            
            switch (layer.State)
            {
                case CellLayerState.BackOne:
                case CellLayerState.BackDouble:
                case CellLayerState.Petal:
                {
                    var scale = SpriteRenderer.RectTransform().rect.size;
                    SpriteRenderer.transform.localScale = new Vector3(scale.x, scale.y, 1f);
                    break;
                }
            }
            
            base.Init(layer);
        }
    }
}