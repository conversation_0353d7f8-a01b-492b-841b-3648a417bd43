using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Crash;
using BBB.DI;
using BBB.MMVibrations;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts;
using GameAssets.Scripts.Match3.Settings;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class TntOverlayView : CellOverlayView
    {
        [Serializable]
        public struct BadgeSpriteEntry
        {
            public TileKinds color;
            public Sprite[] badgeSprite;
            public Color rgb;
        }

        [Serializable]
        public struct TargetSpriteEntry
        {
            public TntTargetType target;
            public Sprite sprite;
        }

        [SerializeField] private string _tick0Sfx = "tnt_tick0";
        [SerializeField] private string _tick1Sfx = "tnt_tick1";
        [SerializeField] private string _tick2Sfx = "tnt_tick2";
        [SerializeField] private string _tick3Sfx = "tnt_tick3";
        [SerializeField] private string _explosionSfx = "tnt_explosion";
        [SerializeField] private string _ranOutSfx = "tnt_ran_out";

        [SerializeField] private Animator _animator;
        [SerializeField] private GameObject _colorTileBadgeCountHolder;
        [SerializeField] private GameObject _uncoloredTileBadgeCountHolder;
        [SerializeField] private TextMeshProUGUI _countTextColoredTile;
        [SerializeField] private TextMeshProUGUI _countTextUncoloredTile;
        [SerializeField] private bool _autoChangeTextColor;
        [SerializeField] private BadgeSpriteEntry[] _badgesColorsMap = Array.Empty<BadgeSpriteEntry>();
        [SerializeField] private TargetSpriteEntry[] _targetsSprites = Array.Empty<TargetSpriteEntry>();
        [SerializeField] private TileKinds _uncoloredTileBadgeColor = TileKinds.White;
        [SerializeField] private Image[] _coloredTileBadgeImage;
        [SerializeField] private Image[] _uncoloredTileBadgeImage;

        [SerializeField] private Image _uncoloredTargetImage;
        
        [SerializeField] private TntTiledImage _tiledImageTemplate;
        [SerializeField] private TileResourceSelector _resourceSelector;
        [SerializeField] private float _localScaleOnPreview = 1f;
        [SerializeField] private RectTransform _scaledPanel;
        [SerializeField] private float _revealDelay = 1f;

        [SerializeField] private List<TntTiledImage> _tiledImagesInstances = new List<TntTiledImage>(4);
        [SerializeField] private string _revealAnimState = "appear";
        [SerializeField] private string _collectAnimState = "collect";
        [SerializeField] private string _destroyAnimState = "destroy";
        [SerializeField] private string _previewAnimState = "preview";
        [SerializeField] private float _shakeBoardDuration = 1f;
        [SerializeField] private ParticleSystemEmitterSizeScaler _particleSystemScaler;

        [Tooltip("Offset for tile borders. This allows to make TNT little bigger than the tile.")]
        [SerializeField] private float _borderTileMarginX = 0f;
        [SerializeField] private float _borderTileMarginY = 0f;

        private FxRenderer _fxRenderer;
        private IGridController _gridController;

        private Coroutine _revealRoutine;
        private int _lastCount;
        private int _sizeX;
        private int _sizeY;
        private Coords _coords;
        private bool _isStartedDestroyAnim;
        private IVibrationsWrapper _vibrations;
        private GoPool _pool;

        [Serializable]
        private struct TileColorData
        {
            public TileKinds tileKind;
            public Color color;
        }

        private void Awake()
        {
            _tiledImageTemplate.gameObject.SetActive(false);
        }

        public override void InitByContext(IContext context)
        {
            base.InitByContext(context);
            _fxRenderer = context.Resolve<FxRenderer>();
            _gridController = context.Resolve<IGridController>();
            _vibrations = context.Resolve<IVibrationsWrapper>();
        }

        public override void Hide()
        {
            base.Hide();
            ResetAnimator();
            _scaledPanel.gameObject.SetActive(false);
        }
        
        private void ResetAnimator()
        {
            if (_animator)
            {
                _animator.ResetAllParameters();
                _animator.Rebind();
                _animator.Update(0f);
                _animator.enabled = false;
            }
        }

        public override void HideForTnt()
        {
            
        }

        public override void Reveal(float time)
        {
            if (_revealRoutine != null)
            {
                StopCoroutine(_revealRoutine);
            }

            if (gameObject.activeInHierarchy)
            {
                _revealRoutine = StartCoroutine(RevealRoutine(time));
            }
            else
            {
                _scaledPanel.gameObject.SetActive(true);
                _animator.enabled = true;
            }
        }

        private IEnumerator RevealRoutine(float externalTime)
        {
            var duration = Mathf.Max(externalTime, _revealDelay);
            float timer = 0;
            while (timer < duration)
            {
                timer += Time.deltaTime;
                yield return null;
            }

            _revealRoutine = null;
            _scaledPanel.gameObject.SetActive(true);
            _animator.enabled = true;
            _animator.Play(_revealAnimState);
        }

        public override void Init(ICellLayer layer)
        {
            Refresh(layer);
        }

        public override void OnUpdate(ICellLayer layer)
        {
            Refresh(layer);
        }

        public override void Animate(CellAnimation anim)
        {
            if (anim == CellAnimation.Preview)
            {
                RefreshBadges(TntTargetType.Simple, TileKinds.Blue, countStr: "5", sizeX: 2, sizeY: 2);
                RefreshWallTiles(2, 2);

                // Offset rect transform because tile is 2x2 and not centered.
                var rectTransform = GetComponent<RectTransform>();
                rectTransform.pivot = new Vector2(1f, 1f);
                rectTransform.anchoredPosition = new Vector2();
                transform.localScale = new Vector3(_localScaleOnPreview, _localScaleOnPreview, _localScaleOnPreview);
                _animator.Play(_revealAnimState, 0, 0f);
            }

            if (anim == CellAnimation.TapFeedback)
            {
                _animator.Play(_previewAnimState);
            }

            base.Animate(anim);
        }

        private void Refresh(ICellLayer layer)
        {
            var tntLayer = layer as TntCellLayer;
            if (tntLayer is null) return;

            if (_lastCount != tntLayer.Count)
            {
                if (_lastCount > 0)
                {
                    AudioProxy.PlaySound(_tick0Sfx);
                    _animator.Play(_collectAnimState, 0, 0f);
                }

                _lastCount = tntLayer.Count;
            }

            _coords = tntLayer.Coords;
            _sizeX = tntLayer.SizeX;
            _sizeY = tntLayer.SizeY;

            RefreshBadges(tntLayer.Target, tntLayer.Kind, tntLayer.Count.ToString(), tntLayer.SizeX, tntLayer.SizeY);
            RefreshWallTiles(tntLayer.SizeX, tntLayer.SizeY);
        }

        private void RefreshBadges(TntTargetType targetType, TileKinds color, string countStr, int sizeX, int sizeY)
        {
            if (targetType == TntTargetType.Simple)
            {
                for (int i = 0; i < _coloredTileBadgeImage.Length; i++)
                {
                    _coloredTileBadgeImage[i].sprite = FindBadgeSprite(color, i);
                }
                _colorTileBadgeCountHolder.SetActive(true);
                _uncoloredTileBadgeCountHolder.SetActive(false);
                _countTextColoredTile.text = countStr;
                _countTextUncoloredTile.text = countStr;
                if (_autoChangeTextColor)
                {
                    var rgbColor = GetColorForKind(color);
                    _countTextColoredTile.color = rgbColor;
                    _countTextUncoloredTile.color = rgbColor;
                }

                //_filledColorImageSimpleTile.color = GetColorForKind(color);
            }
            else
            {
                for (int i = 0; i < _uncoloredTileBadgeImage.Length; i++)
                {
                    _uncoloredTileBadgeImage[i].sprite = FindBadgeSprite(_uncoloredTileBadgeColor, i);
                }
                _uncoloredTargetImage.sprite = FindTargetSprite(targetType);
                _colorTileBadgeCountHolder.SetActive(false);
                _uncoloredTileBadgeCountHolder.SetActive(true);
                _countTextColoredTile.text = countStr;
                _countTextUncoloredTile.text = countStr;
                if (_autoChangeTextColor)
                {
                    var rgbColor = GetColorForKind(TileKinds.Blue);
                    _countTextColoredTile.color = rgbColor;
                    _countTextUncoloredTile.color = rgbColor;
                }

                //_filledColorImage.color = GetColorForKind(TileKinds.White);
            }
            
            // var coords = new Vector2(sizeX * 0.5f, sizeY * 0.5f);
            // _countPanel.anchorMin = coords;
            // _countPanel.anchorMax = coords;
            // _countPanel.anchoredPosition = new Vector2();
        }

        private Color GetColorForKind(TileKinds kind)
        {
            foreach (var item in _badgesColorsMap)
            {
                if (item.color == kind)
                {
                    return item.rgb;
                }
            }

            return Color.white;
        }

        private void RefreshWallTiles(int sizeX, int sizeY)
        {
            if (sizeX <= 0) sizeX = 1;
            if (sizeY <= 0) sizeY = 1;

            var segments = sizeX * sizeY;
            Util.SetListItemInstancesExactCount(_tiledImagesInstances, segments, _tiledImageTemplate, _tiledImageTemplate.transform.parent);
            var cellRect = transform.GetComponent<RectTransform>();
            var cellSize = cellRect.sizeDelta;
            var center = new Vector2(sizeX * 0.5f, sizeY * 0.5f);
            _scaledPanel.anchorMin = center;
            _scaledPanel.anchorMax = center;
            _scaledPanel.sizeDelta = new Vector2(cellSize.x * sizeX + _borderTileMarginX * 2f, cellSize.y * sizeY + _borderTileMarginY * 2f);
            _scaledPanel.anchoredPosition = new Vector2();
            int i = 0;

            var tiledCellSizeRatioX = 1f / sizeX;
            var tiledCellSizeRatioY = 1f / sizeY;
            foreach (var item in _tiledImagesInstances)
            {
                int x = i % sizeX;
                int y = i / sizeX;
                var itemRect = item.GetComponent<RectTransform>();
                itemRect.anchorMin = new Vector2(x * tiledCellSizeRatioX, y * tiledCellSizeRatioY);
                itemRect.anchorMax = new Vector2((x + 1) * tiledCellSizeRatioX, (y + 1) * tiledCellSizeRatioY);
                itemRect.offsetMin = new Vector2();
                itemRect.offsetMax = new Vector2();
                item.Refresh(edgeTop: y >= sizeY - 1, edgeRight: x >= sizeX - 1, edgeBottom: y <= 0, edgeLeft: x <= 0);
                item.gameObject.SetActive(true);
                i++;
            }

            if (_particleSystemScaler != null)
            {
                _particleSystemScaler.SetParticlesEmitterSize(sizeX, sizeY);
            }
        }

        private Sprite FindBadgeSprite(TileKinds color, int index)
        {
            foreach (var entry in _badgesColorsMap)
            {
                if (entry.color == color)
                {
                    return entry.badgeSprite[index];
                }
            }

            BDebug.LogError(LogCat.Match3, "Color badge sprite not found for tileKinds: " + color);
            if (_badgesColorsMap.Length > 0)
            {
                return _badgesColorsMap[0].badgeSprite[index];
            }

            return null;
        }

        private Sprite FindTargetSprite(TntTargetType target)
        {
            if (target == TntTargetType.Litter)
            {
                if (_resourceSelector != null)
                {
                    return _resourceSelector.SelectedLitterVariant.FullSprite;
                }
            }
            else if (target == TntTargetType.Sticker)
            {
                if (_resourceSelector != null)
                {
                    return _resourceSelector.SelectedStickerVariant.CurrentGoalSprite;
                }
            }

            foreach (var entry in _targetsSprites)
            {
                if (entry.target == target)
                {
                    return entry.sprite;
                }
            }

            BDebug.LogError(LogCat.Match3, "Target sprite not found for tntTarget: " + target);
            return null;
        }

        public override void DestroySelfWithFx(GoPool pool)
        {
            AudioProxy.PlaySound(_ranOutSfx);
            _countTextColoredTile.text = "0";
            _countTextUncoloredTile.text = "0";
            _isStartedDestroyAnim = true;
            _pool = pool;
            _animator.Play(_destroyAnimState, 0, 0f);
        }

        /// <summary>
        /// Called from animation clip.
        /// </summary>
        private void OnDestroyAnimationEnd()
        {
            if (_isStartedDestroyAnim)
            {
                _isStartedDestroyAnim = false;
                DestroyAndPlayFx();
            }
        }

        private void DestroyAndPlayFx()
        {
            if (_pool == null)
            {
                CrashLoggerService.Log("DestroyAndPlayFx called");
                Destroy(gameObject);
                CrashLoggerService.Log("tnt explosion sfx called");
            }
            else
            {
                ResetAnimator();
                _pool.Release(gameObject);
            }
            
            AudioProxy.StopSound(_ranOutSfx);
            AudioProxy.PlaySound(_explosionSfx);
            var tntTopRightCornerCoords = _coords + new Coords(_sizeX - 1, _sizeY - 1);
            var tntCenterCoords = Vector2.Lerp(_coords.ToUnityVector2(), tntTopRightCornerCoords.ToUnityVector2(), 0.5f);
            
            CrashLoggerService.Log("tnt spawn effect called");
            _fxRenderer.SpawnConfigurableEffectWithCustomParameters(tntCenterCoords, FxType.TntDestroy, new FxOptionalParameters() { x = _sizeX, y = _sizeY }, releaseTime: 3f);

            if (_shakeBoardDuration > 0)
            {
                CrashLoggerService.Log("tnt shake board called");
                _gridController.DoTweenShakeBoard(_shakeBoardDuration);
            }
        }
    }
}