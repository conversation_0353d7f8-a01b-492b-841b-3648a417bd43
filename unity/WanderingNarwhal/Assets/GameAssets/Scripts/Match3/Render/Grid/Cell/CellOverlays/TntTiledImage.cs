using BBB;
using UnityEngine;
using UnityEngine.UI;

public class TntTiledImage : BbbMonoBehaviour
{
    [SerializeField] private Image _tntTileImage;
    
    [Space]
    [SerializeField] private Sprite _bottomLeft;
    [SerializeField] private Sprite _bottomCenter;
    [SerializeField] private Sprite _bottomRight;
    
    [SerializeField] private Sprite _centerLeft;
    [SerializeField] private Sprite _centerCenter;
    [SerializeField] private Sprite _centerRight;
    
    [SerializeField] private Sprite _topLeft;
    [SerializeField] private Sprite _topCenter;
    [SerializeField] private Sprite _topRight;
    
    public void Refresh(bool edgeTop, bool edgeRight, bool edgeBottom, bool edgeLeft)
    {
        if (edgeBottom && !edgeTop)
        {
            _tntTileImage.sprite = edgeRight switch
            {
                false when edgeLeft => _bottomLeft,
                true when !edgeLeft => _bottomRight,
                _ => _bottomCenter
            };
        }
        else if (edgeTop && !edgeBottom)
        {
            _tntTileImage.sprite = edgeRight switch
            {
                false when edgeLeft => _topLeft,
                true when !edgeLeft => _topRight,
                _ => _topCenter
            };
        }
        else
        {
            _tntTileImage.sprite = edgeRight switch
            {
                false when edgeLeft => _centerLeft,
                true when !edgeLeft => _centerRight,
                _ => _centerCenter
            };
        }
    }
}