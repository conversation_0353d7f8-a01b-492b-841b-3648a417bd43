using BBB.DI;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using DG.Tweening;

namespace BBB.Match3.Renderer
{
    public class WaterOverlayView : CellOverlayView
    {
        [SerializeField] private List<SpriteRenderer> _quarterImages;
        [SerializeField] private Sprite _edgeCornerSprite;
        [SerializeField] private Sprite _edgeRecessSprite;
        [SerializeField] private Sprite _edgeFaceSprite;
        [SerializeField] private Image _waterSpriteRenderer;
        [SerializeField] private Sprite _waterSprite;
        [SerializeField] private Sprite _waterWithShadowSprite;
        [SerializeField] private Sprite _waterLeftCornerShadowSprite;
        [SerializeField] private Sprite _waterRightCornerShadowSprite;
        [SerializeField] private ParticleSystem _particleSystem;
        [SerializeField] private RectTransform _rectTransform;

        private IGridController _gridController;
        private Vector3 _defaultScale;
        private Transform _particleSystemTransform;
        private static readonly Coords[] NeighborCoords = new Coords[8];

        private void Awake()
        {
            _particleSystemTransform = _particleSystem.transform;
            _defaultScale = _particleSystemTransform.localScale;
        }

        public override void InitByContext(IContext context)
        {
            base.InitByContext(context);
            _gridController = context.Resolve<IGridController>();
        }

        public override void Init(ICellLayer layer)
        {
            GenerateEdge(layer);
        }

        private void GenerateEdge(ICellLayer layer)
        {
            var neighbourExistenceFlags = new bool[8];

            var index = 0;
            CardinalDirectionsHelper.PopulateNeighborCoords(NeighborCoords, layer.Coords.X, layer.Coords.Y);
            foreach (var coordAround in NeighborCoords)
            {
                neighbourExistenceFlags[index] = _gridController.IsWaterState(coordAround);
                index++;
            }

            var shadow = true;

            var list = new EdgeQuarterState[4];

            for (var i = 0; i < 4; i++)
            {
                var firstIndex = i * 2;
                var secondIndex = i * 2 + 1;
                var thirdIndex = i * 2 + 2;

                if (thirdIndex > 7)
                    thirdIndex -= 8;

                var quarterState = GenerateQuarterState(
                    neighbourExistenceFlags[firstIndex],
                    neighbourExistenceFlags[secondIndex],
                    neighbourExistenceFlags[thirdIndex], i * 90f);

                SetImage(i, quarterState);
                list[i] = quarterState;

                //Disabling Water with Shadows for every cell which is not a Face
                if (quarterState.Angle is 90 or -90 ||
                    quarterState.QuarterType is EdgeQuarterType.Corner or EdgeQuarterType.Recess)
                {
                    shadow = false;
                }
            }

            //Enable shadow for top left
            if (list[1].QuarterType == EdgeQuarterType.Recess && list[2].QuarterType == EdgeQuarterType.Face)
            {
                shadow = true;
            }

            //Enable shadow for top right
            if (list[1].QuarterType == EdgeQuarterType.Face && list[2].QuarterType == EdgeQuarterType.Recess)
            {
                shadow = true;
            }

            //Enable shadow for starting point which goes up to down
            if (list[1].QuarterType == EdgeQuarterType.Recess && list[2].QuarterType == EdgeQuarterType.Recess)
            {
                shadow = true;
            }

            _waterSpriteRenderer.sprite = shadow ? _waterWithShadowSprite : _waterSprite;

            //Different Sprite for bottom left
            if (list[1].QuarterType == EdgeQuarterType.Face && list[2].QuarterType == EdgeQuarterType.Corner)
            {
                _waterSpriteRenderer.sprite = _waterLeftCornerShadowSprite;
            }

            //Different Sprite for bottom right
            if (list[1].QuarterType == EdgeQuarterType.Corner && list[2].QuarterType == EdgeQuarterType.Face)
            {
                _waterSpriteRenderer.sprite = _waterRightCornerShadowSprite;
            }
        }

        private static EdgeQuarterState GenerateQuarterState(bool firstNeighbourExists, bool cornerNeighbourExists,
            bool secondNeighbourExists, float baseAngle)
        {
            if (firstNeighbourExists)
            {
                return secondNeighbourExists
                    ? new EdgeQuarterState(EdgeQuarterType.Corner, 270f - baseAngle)
                    : new EdgeQuarterState(EdgeQuarterType.Face, -90f - baseAngle);
            }

            if (cornerNeighbourExists)
            {
                return secondNeighbourExists
                    ? new EdgeQuarterState(EdgeQuarterType.Face, 0 - baseAngle)
                    : new EdgeQuarterState(EdgeQuarterType.Recess, 270 - baseAngle);
            }

            return secondNeighbourExists
                ? new EdgeQuarterState(EdgeQuarterType.Face, 360 - baseAngle)
                : new EdgeQuarterState(EdgeQuarterType.Recess, 270 - baseAngle);
        }

        private Sprite GetEdgeSprite(EdgeQuarterType type)
        {
            return type switch
            {
                EdgeQuarterType.Corner => _edgeCornerSprite,
                EdgeQuarterType.Face => _edgeFaceSprite,
                EdgeQuarterType.Recess => _edgeRecessSprite,
                _ => null
            };
        }

        private void SetImage(int i, EdgeQuarterState state)
        {
            _quarterImages[i].gameObject.SetActive(false);
            var image = _quarterImages[i];
            var sprite = GetEdgeSprite(state.QuarterType);
            image.sprite = sprite;
            image.transform.localRotation = Quaternion.Euler(0f, 0f, state.Angle);
            image.gameObject.SetActive(true);
            image.transform.localScale = _rectTransform.sizeDelta / 1.85f;
        }

        public override void Hide()
        {
            base.Hide();

            if (_waterSpriteRenderer)
            {
                _waterSpriteRenderer.SetAlpha(0f);
            }

            if (_quarterImages is { Count: > 0 })
            {
                foreach (var image in _quarterImages)
                {
                    if (image)
                    {
                        image.SetAlpha(0f);
                    }
                }
            }

            if (_particleSystem)
            {
                _particleSystem.Stop();
                _particleSystem.Clear();
            }
        }

        public override void Reveal(float time)
        {
            base.Reveal(time);

            if (_waterSpriteRenderer)
            {
                _waterSpriteRenderer.DoAlpha(1f, time);
            }

            if (_quarterImages is { Count: > 0 })
            {
                foreach (var image in _quarterImages)
                {
                    if (image)
                    {
                        image.DoAlpha(1f, time);
                    }
                }
            }

            if (_particleSystem)
            {
                _particleSystemTransform.localScale = Vector3.zero;
                _particleSystem.Play();
                _particleSystemTransform.DOKill();
                _particleSystemTransform.DOScale(_defaultScale, time);
            }
        }
    }
}