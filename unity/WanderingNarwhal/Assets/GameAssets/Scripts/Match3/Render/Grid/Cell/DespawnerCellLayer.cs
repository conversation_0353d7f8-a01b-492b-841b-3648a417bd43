using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class DespawnerCellLayer : CellLayerBase
    {
        public override CellLayerState State { get { return CellLayerState.Despawner; } }
        public override CellOverlayType OverlayType { get { return CellOverlayType.DespawnerOverlay; } }

        public override bool IsAnimated
        {
            get { return false; }
        }

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Despawner);
        }
    }
}