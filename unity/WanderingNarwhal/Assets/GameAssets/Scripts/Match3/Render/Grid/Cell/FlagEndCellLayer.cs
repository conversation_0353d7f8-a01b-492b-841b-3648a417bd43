using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class FlagEndCellLayer : CellLayerBase
    {
        public override CellOverlayType OverlayType => CellOverlayType.FlagEndOverlay;
        
        public override CellLayerState State => CellLayerState.FlagEnd;

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.FlagEnd);
        }
        
        public override bool IsAnimated => true;
    }
}