using System.Collections.Generic;

namespace BBB.Match3.Renderer
{
    public interface ICellController
    {
        void SetGrid(Grid grid);
        ICellLayer GetLayer(CellLayerState state);
        CellView GetCellView(Coords coords, bool noError = false);
        IEnumerable<CellLayerState> ValidateLayers(Cell cell);
        void Update(Cell cell);
        void ClearGrid(bool cleanPool = false);
        void NotifyCellsAboutEnteredTile(HashSet<Coords> cells);
        void NotifyCellsAboutNearMatchedTiles(HashSet<Coords> cells);
    }
}