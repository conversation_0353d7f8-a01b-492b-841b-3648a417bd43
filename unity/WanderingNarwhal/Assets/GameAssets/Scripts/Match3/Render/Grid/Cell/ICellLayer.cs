using UnityEngine;

namespace BBB.Match3.Renderer
{
    public interface ICellLayer
    {
        CellLayerState State { get; }
        
        CellOverlayType OverlayType { get; }
        Sprite Sprite { get; }
        Material Material { get; }
        Coords Coords { get; }
        int WallCount { get; }

        bool IsAnimated { get; }
        int CurrentOrder { get; }

        bool CheckIfRendererAndCustomize(Cell cell);
    }
}