using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public class IvyCellLayer : CellLayerBase
    {
        public override CellLayerState State
        {
            get { return CellLayerState.Ivy; }
        }

        public override CellOverlayType OverlayType
        {
            get { return CellOverlayType.IvyOverlay; }
        }

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Ivy);
        }

        public override bool IsAnimated
        {
            get { return true; }
        }
    }
}