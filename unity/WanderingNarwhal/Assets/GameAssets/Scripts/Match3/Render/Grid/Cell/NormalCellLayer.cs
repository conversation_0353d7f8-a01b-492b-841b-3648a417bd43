using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    //TODO Remove after Mesh
    public sealed class NormalCellLayer : CellLayerBase
    {
        public override CellLayerState State { get { return CellLayerState.None; } }
  
        protected override bool IsCondition(Cell cell)
        {
            return cell.Is(CellState.None);
        }

        protected override void Customize(Cell cell)
        {
            Sprite = M3Settings.GetCellBackgroundSprite(cell.Coords);
        }
    }
}