using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class PetalCellLayer : CellLayerBase
    {
        public override CellOverlayType OverlayType { get { return CellOverlayType.StandardOverlay; } }
        public override CellLayerState State { get { return CellLayerState.Petal; } }

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Petal) && cell.BackgroundCount == 1;
        }
    }
}