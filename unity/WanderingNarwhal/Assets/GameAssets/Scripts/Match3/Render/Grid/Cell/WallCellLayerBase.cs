using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public abstract class WallCellLayerBase : CellLayerBase
    {
        private CellOverlayType _cellOverlayType = CellOverlayType.WallOverlay;
        public override CellOverlayType OverlayType => _cellOverlayType;
        protected abstract CardinalDirections Directions { get; }
        private int _count;
        public override int WallCount => _count;
      
        protected override bool IsCondition(Cell cell)
        {
            var hasWall = cell.IsAnyOf(CellState.Wall) && cell.Walls != null && 
                          (cell.Walls.Directions & Directions) != 0;
            
            var hasInvisibleWall = cell.IsAnyOf(CellState.InvisibleWall) && 
                                   cell.InvisibleWalls != null && (cell.InvisibleWalls.Directions & Directions) != 0;
            
            var hasDestructibleWall = cell.IsAnyOf(CellState.DestructibleWall) && cell.DestructibleWalls != null && 
                                      cell.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(Directions)].Count > 0;
            
            return hasWall || hasInvisibleWall || hasDestructibleWall;
        }

        protected override void Customize(Cell cell)
        {
            if (cell.IsAnyOf(CellState.Wall))
                _cellOverlayType = CellOverlayType.WallOverlay;
            else if (cell.IsAnyOf(CellState.InvisibleWall))
                _cellOverlayType = CellOverlayType.InvisibleWallOverlay;
            else if (cell.IsAnyOf(CellState.DestructibleWall))
            {
                _count = cell.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(Directions)].Count;
                _cellOverlayType = CellOverlayType.DestructibleWallsOverlay;
            }
        }
    }
}
