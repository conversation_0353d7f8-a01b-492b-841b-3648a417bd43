using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class WaterCellLayer : CellLayerBase
    {
        public override CellOverlayType OverlayType => CellOverlayType.WaterOverlay;
        
        public override CellLayerState State => CellLayerState.Water;

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Water);
        }
        
        protected override void Customize(Cell cell)
        {
            base.Customize(cell);
            Coords = cell.Coords;
        }
    }
}