using System;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using DG.Tweening;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class EdgeRenderer : BbbMonoBehaviour, IRevealableBoardElement
    {
        [Serializable]
        public struct StageEdgeSpritesPair
        {
            public Stage Stage;
            public Sprite EdgeCornerSprite;
            public Sprite EdgeRecessSprite;
            public Sprite EdgeFaceSprite;
        }

        [SerializeField] private List<SpriteRenderer> _quarterImages;

        [SerializeField] private StageEdgeSpritesPair[] _stageEdgeSpritesPairs;
        private Sprite _edgeCornerSprite;
        private Sprite _edgeRecessSprite;
        private Sprite _edgeFaceSprite;

        public void Reveal(float time)
        {
            foreach (var image in _quarterImages)
            {
                var color = image.color;
                color.a = 1f;
                image.DOColor(color, time);
            }
        }

        public void Hide()
        {
            foreach (var image in _quarterImages)
            {
                var color = image.color;
                color.a = 0f;
                image.color = color;
            }
        }

        public Vector2 GetPosition()
        {
            return transform.position;
        }

        private Sprite GetEdgeSprite(EdgeQuarterType type)
        {
            if (type == EdgeQuarterType.Corner) return _edgeCornerSprite;
            if (type == EdgeQuarterType.Face) return _edgeFaceSprite;
            if (type == EdgeQuarterType.Recess) return _edgeRecessSprite;

            return null;
        }

        private void SetEdgeSprites(Stage stage)
        {
            var currentStageSettings = _stageEdgeSpritesPairs[(int)stage];
            _edgeCornerSprite = currentStageSettings.EdgeCornerSprite;
            _edgeFaceSprite = currentStageSettings.EdgeFaceSprite;
            _edgeRecessSprite = currentStageSettings.EdgeRecessSprite;
        }

        public void Init(Edge edge, IGridController gridController, ILevel level, Vector2 cellSize)
        {
            SetEdgeSprites(level.GetPaletteStage());
            for (int i = 0; i < edge.States.Length; i++)
            {
                var state = edge.States[i];

                if (state == null)
                {
                    _quarterImages[i].gameObject.SetActive(false);
                    continue;
                }

                var image = _quarterImages[i];
                var sprite = GetEdgeSprite(state.QuarterType);
                image.sprite = sprite;
                image.transform.localRotation = Quaternion.Euler(0f, 0f, state.Angle);
                image.gameObject.SetActive(true);
                image.transform.localScale = cellSize / 2f;
            }

            var rectTransform = (RectTransform)transform;
            rectTransform.anchoredPosition = gridController.ToDisplacedLocalPosition(edge.Coords);
        }
    }
}