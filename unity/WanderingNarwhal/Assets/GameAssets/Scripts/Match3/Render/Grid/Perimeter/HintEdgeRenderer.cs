using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class EdgeSubTypeRenderer
    {
        public readonly GameObject GameObjectReference;
        public readonly GoPool PoolReference;

        public EdgeSubTypeRenderer(GameObject gameObjectReference,  GoPool poolReference)
        {
            GameObjectReference = gameObjectReference;
            PoolReference = poolReference;
        }
    }
    
    public class HintEdgeRenderer : BbbMonoBehaviour, IRevealableBoardElement
    {
        [SerializeField] private List<GameObject> _quarterGameObjects;

        [SerializeField] private GameObject _edgeCornerSprite;
        [SerializeField] private GameObject _edgeRecessSprite;
        [SerializeField] private GameObject _edgeFaceSprite;
        [SerializeField] private Vector3 _offset;
        
        private GoPool _edgeCornerPool;
        private GoPool _edgeRecessPool;
        private GoPool _edgeFacePool;
        private readonly List<EdgeSubTypeRenderer> _poolList = new();
        private IRevealableBoardElement _revealableBoardElementImplementation;

        private void InitializePools()
        {
            var parentTransform = transform;
            _edgeCornerPool ??= new GoPool(_edgeCornerSprite, parentTransform, 1);  
            _edgeRecessPool ??= new GoPool(_edgeRecessSprite, parentTransform, 1); 
            _edgeFacePool ??= new GoPool(_edgeFaceSprite, parentTransform, 1);
        }

        public void Reveal(float time)
        {
            
        }

        public void Hide()
        {
            
        }

        public Vector2 GetPosition()
        {
            return transform.position;
        }

        public void Clear()
        {
            foreach (var item in _poolList)
            {
                var go = item.GameObjectReference;
                item.PoolReference.Release(go);
            }

            _poolList.Clear();
        }
        private GameObject GetEdgeSprite(EdgeQuarterType type)
        {
            var tempPoolRef = type switch
            {
                EdgeQuarterType.Corner => _edgeCornerPool,
                EdgeQuarterType.Face => _edgeFacePool,
                EdgeQuarterType.Recess => _edgeRecessPool,
                _ => null
            };
            
            var temp = new EdgeSubTypeRenderer(tempPoolRef?.Spawn(), tempPoolRef);
            _poolList.Add(temp);
            return temp.GameObjectReference;
        }

        public void Init(Edge edge, IGridController gridController)
        {
            Clear();
            InitializePools();
            for (var i = 0; i < edge.States.Length; i++)
            {
                var state = edge.States[i];

                if (state == null)
                {
                    _quarterGameObjects[i].gameObject.SetActive(false);
                    continue;
                }

                var gb = _quarterGameObjects[i];
                var prefab= GetEdgeSprite(state.QuarterType).transform;
                Transform gbTransform;
                (gbTransform = gb.transform).localRotation = Quaternion.Euler(0f,0f,state.Angle);

                var prefabTransform = prefab.transform;
                prefabTransform.parent = gbTransform;
                prefabTransform.localPosition= _offset;
                prefabTransform.localRotation= Quaternion.identity;
                
                gb.gameObject.SetActive(true);
            }

            var rectTransform = (RectTransform)transform;
            rectTransform.anchoredPosition = gridController.ToDisplacedLocalPosition(edge.Coords);
        }
    }
}