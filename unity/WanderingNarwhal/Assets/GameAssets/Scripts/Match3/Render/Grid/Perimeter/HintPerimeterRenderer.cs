using System;
using System.Collections.Generic;
using UnityEngine;
using BBB.DI;
using BBB.Core;

namespace BBB.Match3.Renderer
{
    public class HintPerimeterRenderer : RendererBase, IHintPerimeterRenderer, IContextReleasable
    {
        [SerializeField] 
        private float _perimeterWidth = 1f;
        [SerializeField] 
        private ActivePoolItem _perimeterEffect;
        
        private Vector3 _offset;
        private readonly List<Vector3> _vertices = new();
        private ParticleSystem[] _particleSystems;
        private Vector2 _halfCellSize;

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            if (_perimeterEffect != null)
            {
                _perimeterEffect.OnRelease();
                _particleSystems = _perimeterEffect.GetComponentsInChildren<ParticleSystem>();
            }
        }

        public void SetupSize()
        {
            RectTransform rectTransform = (RectTransform)transform;
            foreach (var system in _particleSystems)
            {
                Transform systemTransform = system.transform;
                systemTransform.localScale = CellSize;
                
                var sh = system.shape;
                sh.scale = rectTransform.lossyScale / CellSize;
            }
            
            _halfCellSize = CellSize / 2f;
            rectTransform.sizeDelta = GridSize;
            _offset = -GridSize / 2f;
        }

        public void ReleaseByContext(IContext context)
        {
        }

        public void Clear()
        {
            if (_perimeterEffect != null)
                _perimeterEffect.OnRelease();
        }

        public void RenderGrid(Grid grid, IGridController gridController)
        {
            Clear();

            var mesh = GeneratePerimeterMesh(grid, gridController);
            if (IsZeroSurface(mesh))
            {
                BDebug.LogError(LogCat.Match3, "Generated hint particle mesh has zero surface area");
                return;
            }
            foreach (var particle in _particleSystems)
            {
                if (particle is null)
                {
                    BDebug.LogError(LogCat.Match3,$"Particle system is null in {name}");
                    continue;
                }
                
                var sh = particle.shape;
                
                if (sh.IsNull())
                {
                    BDebug.LogError(LogCat.Match3,"Shape Module in ParticleSystem component is empty");
                    continue;
                }
                
                sh.enabled = true;
                sh.shapeType = ParticleSystemShapeType.Mesh;
                sh.mesh = mesh;
            }
            
            if (_perimeterEffect != null)
                _perimeterEffect.OnSpawn();
        }

        private static bool IsZeroSurface(Mesh mesh)
        {
            var triangles = mesh.triangles;
            var vertices = mesh.vertices;
            for(int i = 0; i < triangles.Length; i += 3) {
                var corner = vertices[triangles[i]];
                var a = vertices[triangles[i + 1]] - corner;
                var b = vertices[triangles[i + 2]] - corner;

                if (Vector3.Cross(a, b).magnitude > 0f)
                    return false;
            }

            return true;
        }
        
        private void AddEdgeQuads(Edge edge, IGridController gridController, List<Vector3> vertices)
        {
            
            foreach (var state in edge.States)
            {
                if (state is not { QuarterType: EdgeQuarterType.Face })
                    continue;

                var position = gridController.ToLocalPosition(edge.Coords);
                switch (state.Angle)
                {
                    case 0:
                    case 360:
                        vertices.Add(new Vector3(position.x  - _halfCellSize.x - _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x - _halfCellSize.x - _perimeterWidth,
                            position.y - _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x  + _halfCellSize.x + _perimeterWidth,
                            position.y - _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x + _halfCellSize.x + _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        break;
                    case 90:
                        vertices.Add(new Vector3(position.x + _halfCellSize.x - _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x + _halfCellSize.x - _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x  + _halfCellSize.x + _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x + _halfCellSize.x + _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        break;
                    case 180:
                        vertices.Add(new Vector3(position.x - _halfCellSize.x - _perimeterWidth,
                            position.y + _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x - _halfCellSize.x - _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x  + _halfCellSize.x + _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x + _halfCellSize.x + _perimeterWidth,
                            position.y + _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        break;
                    case 270:
                        vertices.Add(new Vector3(position.x - _halfCellSize.x - _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x - _halfCellSize.x - _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x  - _halfCellSize.x + _perimeterWidth,
                            position.y + _halfCellSize.y + _perimeterWidth, 0f) + _offset);
                        vertices.Add(new Vector3(position.x - _halfCellSize.x + _perimeterWidth,
                            position.y - _halfCellSize.y - _perimeterWidth, 0f) + _offset);
                        break;
                }
            }
        }

        private Mesh GeneratePerimeterMesh(Grid grid, IGridController gridController)
        {
            var gridPerimeterData = new GridPerimeterData(grid);

            _vertices.Clear();
            foreach (var edge in gridPerimeterData.Edges)
                AddEdgeQuads(edge, gridController, _vertices);

            var mesh = new Mesh {
                name = "Hint Perimeter Mesh",
                vertices = _vertices.ToArray()
            };

            Vector3[] normals = new Vector3[_vertices.Count];
            Array.Fill(normals, Vector3.back);
            mesh.normals = normals;

            Vector4[] tangents = new Vector4[_vertices.Count];
            Array.Fill(tangents, new Vector4(1f, 0f, 0f, -1f));
            mesh.tangents = tangents;

            Vector2[] uvs = new Vector2[_vertices.Count];
            for (int i = 0; i < uvs.Length; i += 4)
            {
                uvs[i] = Vector2.zero;
                uvs[i + 1] = Vector2.up;
                uvs[i + 2] = Vector2.one;
                uvs[i + 3] = Vector2.right;
            }
            mesh.uv = uvs;
            
            int[] triangles = new int[_vertices.Count * 3 / 2];
            int index = 0;
            for (int i = 0; i < triangles.Length; i += 6)
            {
                triangles[i] = index;
                triangles[i + 1] = index + 1;
                triangles[i + 2] = index + 2;
                triangles[i + 3] = index;
                triangles[i + 4] = index + 2;
                triangles[i + 5] = index + 3;
                index += 4;
            }
            mesh.triangles = triangles;
            
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            _vertices.Clear();

            return mesh;
        }
    }
}

