using System.Collections.Generic;
using BBB.DI;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class PerimeterRenderer : RendererBase, IPerimeterRenderer, IContextReleasable, IRevealablesContainer
    {
        private GoPool _pool;
        private readonly List<EdgeRenderer> _edgeRenderers = new List<EdgeRenderer>();

        public void InsertRevealablesInto(List<IRevealableBoardElement> list)
        {
            if (list == null) return;
            foreach (var rend in _edgeRenderers)
            {
                list.Add(rend);
            }
        }

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            if (_pool == null)
            {
                var prefab = TilesResourcesRef.EdgePrefab;
                _pool = new GoPool(prefab, transform, 20);
            }

            gameObject.SetActive(false);
        }

        public void SetupSizes()
        {
            GetComponent<RectTransform>().sizeDelta = GridSize;
        }

        public void ReleaseByContext(IContext context)
        {
        }

        public void Clear()
        {
            foreach (var item in _edgeRenderers)
            {
                var go = item.gameObject;
                go.SetActive(false);
                _pool.Release(go);
            }

            _edgeRenderers.Clear();
        }

        public void RenderGrid(Grid grid, GridController gridController, ILevel level)
        {
            gameObject.SetActive(true);
            Clear();
            var gridPerimeterData = new GridPerimeterData(grid);

            foreach (var edge in gridPerimeterData.Edges)
            {
                var edgeGo = _pool.Spawn();
                var edgeRenderer = edgeGo.GetComponent<EdgeRenderer>();
                edgeRenderer.Init(edge, gridController, level, CellSize);
                _edgeRenderers.Add(edgeRenderer);
                edgeRenderer.GetComponent<RectTransform>().sizeDelta = CellSize;
                edgeGo.SetActive(true);
            }
        }
    }
}