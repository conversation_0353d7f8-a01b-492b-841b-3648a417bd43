using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class IceCubeTileLayer : TileLayerBase
    {
        public override TileLayerState State { get { return TileLayerState.IceCube; } }

        public override void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            base.ConfigurateRenderer(tile, rendererTransform);
            rendererTransform.sizeDelta = TilesResources.TileSize * 1.5f;
        }

        protected override bool IsCondition(TileLayerState state)
        {
            return (state & TileLayerState.IceCube) != 0;
        }
    }
}