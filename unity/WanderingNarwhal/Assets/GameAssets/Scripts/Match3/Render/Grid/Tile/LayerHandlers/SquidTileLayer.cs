using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SquidTileLayer : TileLayerBase
    {
        public const int MAX_SUBITEMS_COUNT = 10;

        public override TileLayerState State
        {
            get { return TileLayerState.Squid; }
        }

        protected override bool IsCondition(TileLayerState state)
        {
            return (state & TileLayerState.Squid) != 0;
        }

        /// <summary>
        /// Convert integer number to array of colors for each squid inside container.
        /// </summary>
        /// <param name="stateNum">Source integer.</param>
        /// <param name="output">Output array with max size length.</param>
        /// <param name="count">Count of items that must be less or equal to max size.</param>
        /// <remarks>
        /// Max dimensions of squid container tile is 3x3, so there may be max 9 items.
        /// each item needs a color that is encoded with 3 bits inside state integer (27 bits total).
        /// It is possible to create containers with dimensions less that 3x3 (2x3 or 1x1, for example),
        /// and also it is possible to set active items count less than max available fitting spots. -VK
        /// </remarks>
        public static void ExtractTileKindsFromInt(int stateNum, TileKinds[] output, int count)
        {
            for (int i = 0; i < count; i++)
            {
                var colorNum = GetColorNumFromState(stateNum, i);
                var color = EncodedNumToColor(colorNum);
                output[i] = color;
            }
        }

        public static int CoordsOffsetToStateIndex(Coords coordsOffset, int sizeX, int sizeY)
        {
            var x = coordsOffset.X;
            var y = coordsOffset.Y;
            if (x < 0 || x >= sizeX) return -1;
            if (y < 0 || y >= sizeY) return -1;
            return y * sizeX + x;
        }

        public static Vector2 ConvertNumIndexToCoordsOffset(int index, int sizeX, int sizeY, int count)
        {
            var result = new Vector2();
            if (count <= 1)
            {
                result.x = sizeX > 1 ? (sizeX - 1) * 0.5f : 0f;
                result.y = sizeY > 1 ? (sizeY - 1) * 0.5f : 0f;
            }
            else
            {
                result.x = index % sizeX;
                result.y = index / sizeX;
            }

            return result;
        }

        /// <summary>
        /// Convert 3-bit number to tile color.
        /// </summary>
        private static TileKinds EncodedNumToColor(int bitMaskNum)
        {
            var masked = bitMaskNum & 0b111;
            var result = (TileKinds)masked;
            if (result == 0) result = TileKinds.Undefined;
            return result;
        }

        /// <summary>
        /// Convert tile color to 3-bit number.
        /// </summary>
        public static int ColorToEncodedNum(TileKinds color)
        {
            var result = (int)color;
            if (result < 0) result = 0;
            return result;
        }

        /// <summary>
        /// Set 3 bit number in 32-bit integer at specific position with given offset.
        /// </summary>
        public static int SetColorNumInState(int state, int colorNum, int index)
        {
            if (index < 0 || index >= SquidTileLayer.MAX_SUBITEMS_COUNT) return state;
            var offset = index * 3;

            // Erase 3 bits in state number at target position.
            state &= (~(0b111 << offset));

            // Write 3 bits in target spot.
            var offsetNum = colorNum << offset;
            state |= offsetNum;
            return state;
        }

        /// <summary>
        /// Take 3 bits number at specific offset in 32-bit integer.
        /// </summary>
        public static int GetColorNumFromState(int state, int index)
        {
            if (index < 0 || index >= SquidTileLayer.MAX_SUBITEMS_COUNT) return state;

            var offset = index * 3;
            var result = (state & (0b111 << offset)) >> offset;
            return result;
        }

        public static bool IsColorStateExistInState(int state, int colorNum)
        {
            for (int i = 0; i < SquidTileLayer.MAX_SUBITEMS_COUNT; i++)
            {
                var subState = GetColorNumFromState(state, i);
                if (subState == colorNum)
                {
                    return true;
                }
            }

            return false;
        }
    }
}