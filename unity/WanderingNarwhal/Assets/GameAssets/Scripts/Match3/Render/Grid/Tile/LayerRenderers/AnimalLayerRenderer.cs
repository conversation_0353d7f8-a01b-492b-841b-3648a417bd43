using BebopBee.Core;
using DG.Tweening;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class AnimalLayerRenderer : TileLayerRendererBase, IPoolItem
    {
        private SkeletonGraphicController _skeletonGraphicController;
        private Tweener _hidingTweener;
        private Vector3 _defaultPosition;
        private Vector3 _defaultScale;
        [SerializeField] private Vector3 _previewScale;

        public void Init(GameObject animalPrefab)
        {
            if (_skeletonGraphicController != null)
            {
                Destroy(_skeletonGraphicController.gameObject);
            }
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{animalPrefab.name}]");
            var go = Instantiate(animalPrefab, transform);
            UnityEngine.Profiling.Profiler.EndSample();
            _skeletonGraphicController = go.GetComponent<SkeletonGraphicController>();
            var tf = _skeletonGraphicController.transform;
            _defaultPosition = tf.localPosition;
            _defaultScale = tf.localScale;
        }
        public override void PlayPreview()
        {
            var rect = transform.GetComponent<RectTransform>();
            rect.localScale = _previewScale;
            base.PlayPreview();
        }

        public void AnimateHide()
        {
            if (!gameObject.activeSelf)
                return;

            if (_skeletonGraphicController != null)
            {
                if (_hidingTweener != null)
                {
                    _hidingTweener.Kill();
                    _hidingTweener = null;
                }

                var tf = _skeletonGraphicController.transform;
                var worldPos = tf.position;
                tf.SetParent(null);
                tf.position = worldPos;
                tf.localScale = _defaultScale;
                tf.localRotation = Quaternion.identity;
                _skeletonGraphicController.SetCurrentIdleAnimation("Hidden");
                _skeletonGraphicController.PlayAnimation("PopIn");

                _hidingTweener = Rx.Invoke(1.0f, _ =>
                {
                    AdjustPositionForOneFrameAnimal();
                    tf.SetParent(transform, false);
                    tf.localPosition = _defaultPosition;
                    tf.localScale = _defaultScale;
                    tf.localRotation = Quaternion.identity;
                    _skeletonGraphicController.SetCurrentIdleAnimation("Idle");
                    _skeletonGraphicController.PlayAnimation("PopOut");
                    _hidingTweener = null;
                });
            }
        }

        public void AnimateAppear()
        {
            if (_hidingTweener != null)
            {
                _hidingTweener.Complete();
                _hidingTweener = null;
            }
            else
            {
                if (_skeletonGraphicController != null)
                {
                    var tf = _skeletonGraphicController.transform;
                    tf.SetParent(transform, false);
                    tf.localPosition = _defaultPosition;
                    tf.localScale = _defaultScale;
                    tf.localRotation = Quaternion.identity;

                    _skeletonGraphicController.SetCurrentIdleAnimation("Idle");
                    _skeletonGraphicController.PlayAnimation("PopOut");
                    AdjustPositionForOneFrameAnimal();
                }
            }
        }

        public void AdjustPositionForOneFrameAnimal()
        {
            var frameHolder = transform.parent.GetComponentInChildren<FrameLayerRenderer>();
            if (frameHolder == null) return;
            var activeLayerCount = 0;
            for (var i = 0; i < frameHolder.transform.childCount; ++i)
            {
                var childGo = frameHolder.transform.GetChild(i).gameObject;
                if (childGo.activeSelf && childGo.CompareTag(SheetTileLayerViewBase.SheetLayerTag))
                    ++activeLayerCount;
            }
            if (activeLayerCount != 1) return;
            ((RectTransform)transform).anchoredPosition = new Vector2(0f, -2.3f);
        }

        public void OnInstantiate()
        {
        }

        public void OnSpawn()
        {
        }

        public void OnRelease()
        {
            if(_skeletonGraphicController != null)
                _skeletonGraphicController.transform.SetParent(transform, false);
        }
    }
}
