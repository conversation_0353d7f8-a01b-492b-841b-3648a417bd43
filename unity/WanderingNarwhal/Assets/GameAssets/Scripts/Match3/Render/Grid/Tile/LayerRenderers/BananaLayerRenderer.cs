using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BananaLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string _idleAnimName;

        [SerializeField]
        private string _tapFeedbackName = "Landing";

        [SerializeField]
        private string _previewAnimName;

        public void OnSpawn()
        {
            UnHide();
            if (!_spawnAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.ClearTracks();
                _sk.AnimationState.AddAnimation(0, _spawnAnimName, loop: false, delay: 0);
            }

            if (!_idleAnimName.IsNullOrEmpty())
            {
                if (_spawnAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.ClearTracks();
                }

                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            UnHide();
            if (_previewAnimName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: true);
        }
    }
}