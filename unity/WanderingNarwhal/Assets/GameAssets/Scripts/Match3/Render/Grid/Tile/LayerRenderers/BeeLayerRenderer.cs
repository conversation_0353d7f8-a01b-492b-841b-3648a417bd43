using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BeeLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string[] _idleAnimName;

        [SerializeField]
        private string _tapFeedbackName = "Match";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        [SerializeField]
        private string[] _skinName;

        [SerializeField]
        private int _currentSkinIndex;

        public int CurrentSkinIndex
        {
            get { return _currentSkinIndex; }
            set
            {
                _currentSkinIndex = value; 
                RefreshSkin();
            }
        }

        public void RefreshSkinRelatedAnim()
        {
            if (!_idleAnimName.IsNullOrEmpty())
            {
                var idleName = _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)];
                if (_spawnAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, idleName, loop: true);
                }
                else
                {
                    _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
                }
            }
        }

        public void OnSpawn()
        {
            UnHide();

            if (!_spawnAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _spawnAnimName, loop: false);
            }
        }

        private void RefreshSkin()
        {
            var skin = _sk.SkeletonData.FindSkin(_skinName[Mathf.Clamp(CurrentSkinIndex, 0, _skinName.Length - 1)]);
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetSlotsToSetupPose();
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty())
            {
                return;
            }

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);

            if (!_idleAnimName.IsNullOrEmpty())
            {
                var idleName = _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)];
                _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            UnHide();
            RefreshSkin();
            var idleName = _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)];
            _sk.AnimationState.SetAnimation(0, idleName, loop: true);
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }
                CurrentSkinIndex = 0;
                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }
    }
}