using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BirdLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string _idleAnimName;

        [SerializeField]
        private string _tapFeedbackName = "tap";

        [SerializeField]
        private string _previewAnimName = "";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        public void OnSpawn()
        {
            UnHide();
            if (!_spawnAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.ClearTracks();
                _sk.AnimationState.AddAnimation(0, _spawnAnimName, loop: false, delay: 0);
            }

            if (!_idleAnimName.IsNullOrEmpty())
            {
                if (_spawnAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
                }
                else
                {
                    _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
                }
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty())
            {
                return;
            }

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            UnHide();
            if (_previewAnimName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: true);
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }
    }
}