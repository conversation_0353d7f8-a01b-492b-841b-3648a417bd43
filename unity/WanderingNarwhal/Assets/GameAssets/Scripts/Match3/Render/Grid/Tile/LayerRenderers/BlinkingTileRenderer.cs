using BBB.Match3.Renderer;
using DG.Tweening;
using UnityEngine;

namespace BBB
{
    public class BlinkingTileRenderer : BbbMonoBehaviour
    {
        [SerializeField] private SpriteRenderer _tileImage;
        
        private Tween _tweener;
        private Transform _cachedTransform;

        private void Awake()
        {
            _cachedTransform = transform;
        }

        public void SetData(TileViewData data)
        {
            _tileImage.sprite = data.Sprite;
        }

        public void StartBlinking()
        {
            var initialScale = _cachedTransform.localScale.x;
            var sequence = DOTween.Sequence();
            sequence.Append(_cachedTransform.DOScale(1.05f * initialScale, 0.15f).SetEase(Ease.InOutQuad));
            sequence.Append(_cachedTransform.DOScale(1.00f * initialScale, 0.15f).SetEase(Ease.InOutQuad));
            sequence.SetLoops(-1).OnKill(() =>
                {
                    _cachedTransform.localScale = Vector3.one;
                    _tweener = null;
                })
                .OnComplete(() =>
                {
                    _cachedTransform.localScale = Vector3.one;
                    _tweener = null;
                });
            
            _tweener = sequence;
        }

        public void StopBlinking()
        {
            _tweener?.Kill();
        }
    }
}
