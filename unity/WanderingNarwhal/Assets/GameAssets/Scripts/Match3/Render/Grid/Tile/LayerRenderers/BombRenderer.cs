using System;
using System.Collections.Generic;
using BBB.Match3.Renderer;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB
{
    public interface ISpecialTileRenderer
    {
        void Setup(M3Settings m3Settings, TileSpeciality tileSpeciality);
        void SetAlpha(float value);
        int SortingLayer { set; }
        void PlayTapFeedback(ITileLayerView layerView);
        void PlayPreview();
        void PlayPreactivate();
        void SetupCallbackOnIntro(Action introCallback);
        Vector3 GetGlobalScale();
        void PlayFlight();
        void PlaySwap();
    }

    public interface IFormableTileRenderer
    {
        void PlayStandardFormation();
        void PlayFormationByBolt();
        void PlayBoltPreactivate();
    }

    public class BombRenderer : TileLayerRendererBase, ISpecialTileRenderer, IFormableTileRenderer
    {

        [SerializeField] private List<ParticleSystemRenderer> _psRenderers;
        [SerializeField] private List<ParticleSystem> _particleSystems;
        [SerializeField] private List<SpriteRenderer> _spriteRenderers;
        [SerializeField] private ParticleSystem _splashParticleSystem;
        [SerializeField] private Animator _animator;
        private Action _introCallback;


        public Vector3 GetGlobalScale()
        {
            return transform.lossyScale;
        }

        public void PlayFlight()
        {
            
        }

        public void PlaySwap()
        {
            
        }

        public void SetAlpha(float value)
        {
            foreach (var ps in _particleSystems)
            {
                ps.SetAlpha(value);
            }

            _spriteRenderers.ForEach(sr =>
            {
                var color = sr.color;
                color.a = value;
                sr.color = color;
            });
        }

        public int SortingLayer
        {
            set
            {
                foreach (var psRenderer in _psRenderers)
                {
                    psRenderer.sortingLayerID = value;
                }
                
                _spriteRenderers.ForEach(sr => sr.sortingLayerID = value);
            }
        }

        public void PlayPreactivate()
        {
            _animator.SetTrigger(ComboTriggerHash);
        }

        public void PlayBoltPreactivate()
        {
            _animator.SetTrigger(BoltComboTriggerHash);
        }

        public void Setup(M3Settings m3Settings, TileSpeciality tileSpeciality)
        {
            
        }

        public void PlayStandardFormation()
        {
            _animator.SetTrigger(FormationTriggerHash);
            if(_splashParticleSystem != null)
                _splashParticleSystem.Play(false);
        }

        public void PlayFormationByBolt()
        {
            _animator.SetTrigger(FormationByBoltTriggerHash);
        }

        public void SetupCallbackOnIntro(Action introCallback)
        {
            _introCallback = introCallback;
        }

        //called from animation clip
        public void IntroDoneEventHandler()
        {
            _introCallback.SafeInvoke();
            _introCallback = null;
        }
    }
}