using System;
using System.Collections;
using System.Collections.Generic;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using JetBrains.Annotations;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BowlingLayerRenderer : DelayedAppearLayerRenderer
    {
        public event Action OnCurtainOpened;

        private const string PinObjectName = "BowlingPin";
        private const int StayingPinZOrder = 120;
        private const int FlyingPinZOrder = 1199;
        private static readonly int OpenCurtain = Animator.StringToHash("OpenCurtain");
        private static readonly int ShakePins = Animator.StringToHash("ShakePins");
        private List<Transform> _idlePins;
        private bool _isShakeAnimationPlaying;

        [SerializeField] private List<GameObject> _turnOffOnDestroy;
        [SerializeField] private Animator _animator;
        [SerializeField] private Canvas _curtain;
        [SerializeField] private Transform _pinContainer;
        [SerializeField] private float _tileReleaseDuration = 1.0f;
        [SerializeField] private float _tileHelpMinOffset = 0.35f;
        [SerializeField] private float _tileHelpMaxOffset = 1.3f;
        public float destroyPinFXDuration = 1.0f;
        public float destroyTileFXDuration = 1.0f;


        public void InitialSetup()
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            rect.pivot = new Vector2(0, 0);
            rect.anchorMin = new Vector2();
            rect.anchorMax = new Vector2(2.15f, 2.15f);
            rect.offsetMin = new Vector2();
            rect.offsetMax = new Vector2();

            _curtain.overrideSorting = true;
            _curtain.sortingOrder = StayingPinZOrder;
            _idlePins = new List<Transform>();
            _isShakeAnimationPlaying = false;
            
            for (var i = 1; i <= 6; ++i)
            {
                var pin = _pinContainer.Find($"{PinObjectName}{i}");
                pin.gameObject.SetActive(true);
                _idlePins.Add(pin);
                var pinCanvas = pin.GetComponent<Canvas>();
                pinCanvas.overrideSorting = true;
                pinCanvas.sortingOrder = StayingPinZOrder - i;
            }
        }
        
        [UsedImplicitly]
        public void ShakeAnimationReset()
        {
            _isShakeAnimationPlaying = false;
        }

        public void PlayCurtainOpening()
        {
            _curtain.overrideSorting = true;
            _curtain.sortingOrder = FlyingPinZOrder;
            _animator.SetTrigger(OpenCurtain);
        }

        [UsedImplicitly]
        public void CurtainAnimationFinished()
        {
            OnCurtainOpened?.Invoke();
        }
        
        public override void PlayPreview()
        {
            Show();
            UpdateSize(1);
            base.PlayPreview();
        }

        protected override void OnAppearAfterDelayBeforeBaseCall()
        {
            gameObject.SetActive(true);
        }

        public void PlayHit()
        {
            if (_idlePins.Count > 0)
            {
                if (!_isShakeAnimationPlaying)
                {
                    _animator.SetTrigger(ShakePins);
                    _isShakeAnimationPlaying = true;
                }
                _idlePins[0].gameObject.SetActive(false);
                _idlePins.RemoveAt(0);
            }
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            _turnOffOnDestroy.ForEach(go => go.SetActive(false));
            yield return WaitCache.Seconds(_tileReleaseDuration);
            onDone.SafeInvoke();
        }

        public override void Show()
        {
            base.Show();
            _turnOffOnDestroy.ForEach(go => go.SetActive(true));
            _animator.StopPlayback();
            _animator.ResetAllParameters();
        }

        public void UpdateSize(int size)
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            if (size == 1)
            {
                // This is used for the preview in tile help panel
                rect.anchorMin = -Vector2.one * _tileHelpMinOffset;
                rect.anchorMax = Vector2.one * _tileHelpMaxOffset;
            }
            else
            {
                rect.anchorMin = Vector2.zero;
                rect.anchorMax = Vector2.one * 2;
            }
            rect.anchoredPosition = Vector2.zero;
            FitBaseRectToExactCellSize();
        }
    }
}
