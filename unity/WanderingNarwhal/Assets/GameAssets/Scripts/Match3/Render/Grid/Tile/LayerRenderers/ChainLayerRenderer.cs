using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ChainLayerRenderer : TileLayerRendererBase
    {
        private static List<List<float>> _chainYPosisions = new List<List<float>>
        {
            new List<float> {  -2f },
            new List<float> { -9f, 6f },
            new List<float> { -9f, 6f, -4f }
        };

        [SerializeField] private List<Transform> _chainTransforms;

        public void Refresh(int sheetLevel)
        {
            var index = sheetLevel - 1;

            if (index < 0)
            {
                for (int i = 0; i < _chainTransforms.Count; i++)
                {
                    _chainTransforms[i].gameObject.SetActive(false);
                }
                
                return;
            }

            if (index > 2)
            {
                UnityEngine.Debug.LogErrorFormat("ChainLayerRenderer: unexpected index {0}", index);
                return;
            }

            var chainYs = _chainYPosisions[index];
            for (int i = 0; i < _chainTransforms.Count; i++)
            {
                bool indexWithinRange = i <= index;
                _chainTransforms[i].gameObject.SetActive(indexWithinRange);

                if (indexWithinRange)
                {
                    var localPos = _chainTransforms[i].localPosition;
                    localPos.y = chainYs[i];
                    _chainTransforms[i].localPosition = localPos;
                }
            }
        }
    }
}