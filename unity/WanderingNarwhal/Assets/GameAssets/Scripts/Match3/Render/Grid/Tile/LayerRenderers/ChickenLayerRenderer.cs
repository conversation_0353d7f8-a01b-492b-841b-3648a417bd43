using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ChickenLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string[] _idleAnimName;

        [SerializeField]
        private string[] _skinName;

        [SerializeField]
        private string _tapFeedbackName = "tap";

        [SerializeField]
        private string _previewAnimName = "flying";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        private int _currentSkinIndex;

        public int CurrentSkinIndex
        {
            get
            {
                return _currentSkinIndex;
            }
            set
            {
                _currentSkinIndex = value;
                RefreshSkin();
            }
        }

        public void OnSpawn()
        {
            UnHide();
            if (!_spawnAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _spawnAnimName, loop: false);
            }
        }

        public void RefreshSkinRelatedAnim()
        {
            if (!_idleAnimName.IsNullOrEmpty())
            {
                if (_spawnAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)], loop: true);
                }
                else
                {
                    _sk.AnimationState.AddAnimation(0, _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)], loop: true, delay: 0);
                }
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName[Mathf.Clamp(CurrentSkinIndex, 0, _idleAnimName.Length - 1)], loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            RefreshSkin();
            UnHide();
            if (!_previewAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: true);
            }
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                CurrentSkinIndex = 0;
                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        private void RefreshSkin()
        {
            var skin = _sk.SkeletonData.FindSkin(_skinName[Mathf.Clamp(CurrentSkinIndex, 0, _skinName.Length - 1)]);
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetToSetupPose();
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }
    }
}