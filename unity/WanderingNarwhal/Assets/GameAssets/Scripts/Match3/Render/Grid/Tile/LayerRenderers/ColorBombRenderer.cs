using System;
using System.Collections.Generic;
using BBB.Match3.Renderer;
using GameAssets.Scripts.Match3.Settings;
using Spine.Unity;
using UnityEngine;

namespace BBB
{
    public class ColorBombRenderer : TileLayerRendererBase, ISpecialTileRenderer, IPoolItem
    {
        private const string AppearAnimation = "appearance";
        private const string IdleAnimation = "idle";

        [SerializeField] private List<ParticleSystem> _particleSystems;
        [SerializeField] private List<ParticleSystemRenderer> _particleSystemRenderers;
        [SerializeField] private ParticleSystem _splashParticleSystem;
        [SerializeField] private SkeletonGraphic _skeletonGraphic;

        private Action _introCallback;
        private M3Settings _m3Settings;

        public Vector3 GetGlobalScale()
        {
            return transform.lossyScale;
        }

        public void PlayFlight()
        {
            
        }

        public void PlaySwap()
        {
            
        }

        public void SetAlpha(float value)
        {
            foreach (var ps in _particleSystems)
            {
                ps.SetAlpha(value);
            }

            if (_skeletonGraphic != null)
            {
                _skeletonGraphic.SetAlpha(value);
            }
        }

        public int SortingLayer
        {
            set
            {
                if (_skeletonGraphic != null && _skeletonGraphic.canvas != null)
                {
                    _skeletonGraphic.canvas.overrideSorting = true;
                    _skeletonGraphic.canvas.sortingLayerID = value;
                }
                foreach (var psr in _particleSystemRenderers)
                {
                    psr.sortingLayerID = value;
                }
            }
        }

        public void PlayPreactivate()
        {
            
        }

        public void Setup(M3Settings m3Settings, TileSpeciality tileSpeciality)
        {
            _m3Settings = m3Settings;
            _m3Settings.SuperDiscoBallChanged -= UpdateSkin;
            _m3Settings.SuperDiscoBallChanged += UpdateSkin;
            
            UpdateSkin();
            ResetSpineAnimation();
            InitSkeleton();
        }

        private void UpdateSkin()
        {
            if (_skeletonGraphic != null)
            {
                _skeletonGraphic.Skeleton.SetSkin(_m3Settings.GetDiscoBallSkin());
            }
        }

        private void InitSkeleton()
        {
            if (_skeletonGraphic == null) return;
            _skeletonGraphic.AnimationState.SetAnimation(0, AppearAnimation, false).Complete += _ =>
            {
                IntroDoneEventHandler();
                _skeletonGraphic.AnimationState.SetAnimation(0, IdleAnimation, true);
            };
        }

        public void OnInstantiate()
        {
            gameObject.SetActive(false);
            ResetSpineAnimation();
        }

        public void OnSpawn()
        {
            gameObject.SetActive(true);
            ResetSpineAnimation();
            if (_splashParticleSystem != null)
            {
                _splashParticleSystem.Play(false);
            }
        }

        public void OnRelease()
        {
            _m3Settings.SuperDiscoBallChanged -= UpdateSkin;
            gameObject.SetActive(false);
        }

        private void ResetSpineAnimation()
        {
            if (_skeletonGraphic == null) return;
            _skeletonGraphic.AnimationState.ClearTracks();
        }
        
        public void SetupCallbackOnIntro(Action introCallback)
        {
            _introCallback = introCallback;
        }

        //called from animation clip
        public void IntroDoneEventHandler()
        {
            _introCallback.SafeInvoke();
            _introCallback = null;
        }

        protected override void OnDestroy()
        {
            if (_m3Settings != null)
            {
                _m3Settings.SuperDiscoBallChanged -= UpdateSkin;
            }
        }
    }
}