using UnityEngine;

namespace BBB.M3Editor
{
    public class DebugInvisibleWallRenderer : BbbMonoBehaviour
    {
        [SerializeField] private GameObject N_Image;
        [SerializeField] private GameObject E_Image;
        [SerializeField] private GameObject S_Image;
        [SerializeField] private GameObject W_Image;

        public void UpdateWalls(CardinalDirections directions)
        {
            N_Image.SetActive((directions & CardinalDirections.N) != 0);
            E_Image.SetActive((directions & CardinalDirections.E) != 0);
            S_Image.SetActive((directions & CardinalDirections.S) != 0);
            W_Image.SetActive((directions & CardinalDirections.W) != 0);
        }
    }
}
