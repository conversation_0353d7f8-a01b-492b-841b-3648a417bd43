using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Render.Grid.Tile.LayerRenderers;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class DynamiteBoxLayerRenderer : DelayedAppearLayerRenderer
    {
        private const string StickObjectName = "DynamiteStick";

        [SerializeField] private Animator _animator;
        [SerializeField] private Transform _stickContainer;
        [SerializeField] private float _destroyDuration = 1.0f;
        [SerializeField] private TileKinds[] _helpPanelColors = new TileKinds[8];
        [SerializeField] private float _destroyTileFXDuration = 3.0f;
        [SerializeField] private float _tileHelpMinOffset = 0.35f;
        [SerializeField] private float _tileHelpMaxOffset = 1.3f;
        [SerializeField] private Vector2 _anchorMax = new(2.15f, 2.15f);
        [NonSerialized] public const int NumberOfDynamiteSticks = 8;
        [SerializeField] private float _preDestroyDelay = 0.3f;

        private List<DynamiteBoxLayerSubItemRenderer> _sticksSubRenderer;

        private static readonly int ShakeTile = Animator.StringToHash("Shake");
        private static readonly int DestroyTile = Animator.StringToHash("Destroy");
        private static readonly int BlinkTile = Animator.StringToHash("Blink");
        private int _baseCanvasSortOrder;
        public float DestroyTileFXDuration => _destroyTileFXDuration;

        public void InitialSetup()
        {
            _baseCanvasSortOrder = GetComponent<Canvas>().sortingOrder;
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            rect.pivot = Vector2.zero;
            rect.anchorMin = new Vector2();
            rect.anchorMax = _anchorMax;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            _sticksSubRenderer = new List<DynamiteBoxLayerSubItemRenderer>();
            for (var i = 0; i < NumberOfDynamiteSticks; ++i)
            {
                var dynamiteStick = _stickContainer.Find($"{StickObjectName}{i}");
                dynamiteStick.gameObject.SetActive(true);
                var stickSubItemRenderer = dynamiteStick.GetComponent<DynamiteBoxLayerSubItemRenderer>();
                stickSubItemRenderer.ItemIndex = i;
                _sticksSubRenderer.Add(stickSubItemRenderer);
                stickSubItemRenderer.SkeletonGraphic.Initialize(true);

                var dynamiteStickAnim = dynamiteStick.GetComponent<Animator>();
                dynamiteStickAnim.ResetAllParameters();
                dynamiteStickAnim.Rebind();
                dynamiteStickAnim.Update(0f);
            }
        }

        public void Setup(TileKinds[] tileKinds)
        {
            if (tileKinds.Length < _sticksSubRenderer.Count)
                return;
            var index = 0;
            foreach (var stick in _sticksSubRenderer)
            {
                stick.Setup(tileKinds[index], _baseCanvasSortOrder);
                index++;
            }
        }

        public void PlayHit(int index, int hp)
        {
            if (_sticksSubRenderer.Count <= 0) return;

            _animator.SetTrigger(ShakeTile);

            foreach (var stick in _sticksSubRenderer)
            {
                if (stick.ItemIndex != index)
                {
                    continue;
                }
                stick.PlayDestroyAnimation();
                stick.PlayJumpAnimation();
            }

            if (hp != 0)
            {
                return;
            }
            
            AudioProxy.PlaySound(Match3SoundIds.DynamiteDestroyPart1);
            Rx.Invoke(_preDestroyDelay, _ =>
            {
                _animator.SetTrigger(BlinkTile);
                AudioProxy.PlaySound(Match3SoundIds.DynamiteDestroyPart2);
                foreach (var dynamiteStick in _sticksSubRenderer)
                {
                    dynamiteStick.PlayEndParticleEffect();
                }
            });
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_destroyDuration);
            AudioProxy.PlaySound(Match3SoundIds.DynamiteDestroyPart3);
            _animator.SetTrigger(DestroyTile);
            onDone.SafeInvoke();
        }

        public override void PlayPreview()
        {
            Setup(_helpPanelColors);
            Show();
            UpdateSize(1);
            base.PlayPreview();
        }

        private void UpdateSize(int size)
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            if (size == 1)
            {
                // This is used for the preview in tile help panel
                rect.anchorMin = -Vector2.one * _tileHelpMinOffset;
                rect.anchorMax = Vector2.one * _tileHelpMaxOffset;
            }
            else
            {
                rect.anchorMin = Vector2.zero;
                rect.anchorMax = Vector2.one * 2;
            }

            rect.anchoredPosition = Vector2.zero;
            FitBaseRectToExactCellSize();
        }

        public override void Show()
        {
            base.Show();
            _animator.StopPlayback();
            _animator.ResetAllParameters();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            
            Hide();
            ResetToDefault();
        }
    }
}