using BBB;
using BBB.Audio;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Render.Grid.Tile.LayerRenderers
{
    public class DynamiteBoxLayerSubItemRenderer : BbbMonoBehaviour
    {
        private const int ParticleSystemSortOrder = 100;
        public SkeletonGraphic SkeletonGraphic;
        public string JumpAnimation;
        public string DestroyAnimation;
        private static readonly int LightUp = Animator.StringToHash("LightUp");
        private static readonly int FinalDestroy = Animator.StringToHash("FinalDestroy");
        [SerializeField]private Canvas bottleCanvas;
        [SerializeField]private int bottleIndex;
        [SerializeField]private Animator _animator;
        [SerializeField]private Renderer _particleSystemRenderer;
        [HideInInspector] public int ItemIndex;
        private bool _isDestroyed;
        
        public void Setup(TileKinds tileColor, int sortOrder)
        {
            SkeletonGraphic.Skeleton.SetSkin(tileColor.GetSkinColor());
            sortOrder += bottleIndex;
            bottleCanvas.sortingOrder = sortOrder;
            _particleSystemRenderer.sortingOrder = sortOrder + ParticleSystemSortOrder;
            _isDestroyed = false;
            _animator.ResetAllParameters();
            _animator.Rebind();
        }

        public void PlayJumpAnimation()
        {
            if (_isDestroyed) return;
            SkeletonGraphic.AnimationState.SetAnimation(0, JumpAnimation, false);
        }

        public void PlayDestroyAnimation()
        {
            if (_isDestroyed) return;
            _isDestroyed = true;
            _animator.SetTrigger(LightUp); 
            SkeletonGraphic.AnimationState.SetAnimation(0, DestroyAnimation, false);
            AudioProxy.PlaySound(Match3SoundIds.DynamiteHitPart1);
            AudioProxy.PlaySound(Match3SoundIds.DynamiteHitPart2);
        }

        public void PlayEndParticleEffect()
        {
            _animator.SetTrigger(FinalDestroy);
        }
    }
}
