using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB
{
    public class FireWorksLayerRenderer : TileLayerRendererBase
    {
        [SerializeField] private GameObject[] _images;
        [SerializeField] private Vector3 _previewScale;
        [SerializeField] private Vector3 _defaultScale;

        [SerializeField]
        private string _takeHitSoundUid = Match3SoundIds.StickerDestroy;

        
        public void ResetView()
        {
            foreach (var image in _images)
            {
                image.transform.localScale = _defaultScale;
                image.SetActive(false);
            }

            transform.localRotation = Quaternion.identity;
        }

        public void UpdateView(int count)
        {
            for (var i = 0; i < _images.Length; i++)
            {
                _images[i].SetActive(i < count);
            }
            
            transform.localRotation = Quaternion.identity;
        }

        public override void PlayPreview()
        {
            foreach (var t in _images)
            {
                t.transform.localScale = _previewScale;
            }

            base.PlayPreview();
        }

        public void PlayFx()
        {
            AudioProxy.PlaySound(_takeHitSoundUid);
        }
    }
}
