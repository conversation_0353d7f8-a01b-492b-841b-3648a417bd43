using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class FlowerPotLayerRenderer : TileLayerRendererBase
    {
        private const string Intro = "pot_with_flower_intro";
        private const string IdleClosed = "pot_with_flower_closed_idle";
        private const string Reveal = "pot_with_flower_reveal";
        private const string OpenIdle = "pot_with_flower_open_idle";

        
        [SerializeField] public float _destroyDuration = 2.0f;
        
        [SerializeField] public SkeletonGraphic SkeletonGraphic;

        
        public void ResetView()
        {
            PlayPreview();
        }

        public override void PlayPreview()
        {
            SkeletonGraphic.AnimationState.SetAnimation(0, Intro, false);
            SkeletonGraphic.AnimationState.AddAnimation(0,IdleClosed, true, 0);
        }

        public void PlayReveal()
        {
            SkeletonGraphic.AnimationState.SetAnimation(0, Reveal, false);
            SkeletonGraphic.AnimationState.AddAnimation(0,OpenIdle, true, 0);
        }

        public void PlayDestroy()
        { 
            gameObject.SetActive(false);
        }
    }
}