namespace BBB.Match3.Renderer
{
    public class GiantPinataLayerRenderer : DelayedAppearLayerRenderer
    {
        private const string GiantPinataShowTile = "Show";
        
        public override void Init()
        {
            base.Init();
            if (SpineAnimator != null)
            {
                SpineAnimator.Skeleton.SetToSetupPose();
                SpineAnimator.AnimationState.ClearTracks();
            }
        }

        public override void Show()
        {
            base.Show();
            if (SpineAnimator != null)
            {
                SpineAnimator.AnimationState.SetAnimation(0, GiantPinataShowTile, false);
            }
        }
    }
}