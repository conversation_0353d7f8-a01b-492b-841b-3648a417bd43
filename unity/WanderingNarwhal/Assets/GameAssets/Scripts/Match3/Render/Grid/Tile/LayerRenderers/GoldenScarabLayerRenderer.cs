using System;
using System.Collections;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class GoldenScarabLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private RectTransform _content;
        [SerializeField] private Transform _layers;
        [SerializeField] private float _destroyDuration = 1.0f;

        private static readonly int LayerIndexHash = Animator.StringToHash("LayerIndex");
        private static readonly int HideLayer = Animator.StringToHash("HideLayer");
        
        public float DestroyDuration => _destroyDuration;

        public void PlayHit(int hp)
        {
            if (hp > 0 && hp <= _layers.childCount)
            {
                _animator.SetInteger(LayerIndexHash, hp);
                _animator.SetTrigger(HideLayer);
            }
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            _animator.SetInteger(LayerIndexHash, 1);
            _animator.SetTrigger(HideLayer);
            yield return WaitCache.Seconds(_destroyDuration);
            onDone.SafeInvoke();
        }

        public override void Show()
        {
            base.Show();
            _animator.StopPlayback();
            _animator.ResetAllParameters();
        }
    }
}
