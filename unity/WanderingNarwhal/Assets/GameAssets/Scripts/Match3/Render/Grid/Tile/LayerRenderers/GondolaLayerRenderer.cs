using System;
using BBB.Core;
using DG.Tweening;
using Spine.Unity;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Utils;
using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Renderer
{
    public class GondolaLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private GameObject _waterFx;
        [SerializeField] private float _idleDelay = 1f;
        [SerializeField] private float _celebrationDelay = 0.3f;
        [SerializeField] private Vector2 _anchorMax = new(2.15f, 1f);

        [Header("Gondola Speed Controls")] 
        [SerializeField] private float _gondolaSpeedNormal = 1f;
        [SerializeField] private float _gondolaBaseSpeedMultiplier = 0.25f;
        [SerializeField] private float _gondolaMaxSpeedLimit = 3f;
        [SerializeField] private float _gondolaTurnDelay = 0.7f;
        
        [Header("Preview State")]
        [SerializeField] private string _preview = "board/board-right-idle";
        
        [Header("0 angle states")]
        [SerializeField] private string _rightIdle = "charAnim/right-idle";
        [SerializeField] private string _rightRowing = "charAnim/right-rowing";
        [SerializeField] private string _rightVictory = "charAnim/right-victory";
        [SerializeField] private string _rightUp = "right-up";
        [SerializeField] private string _rightDown = "right-down";
        
        [Header("180 angle states")]
        [SerializeField] private string _leftIdle = "charAnim/left-idle";
        [SerializeField] private string _leftRowing = "charAnim/left-rowing";
        [SerializeField] private string _leftVictory = "charAnim/left-victory";
        [SerializeField] private string _leftUp = "left-up";
        [SerializeField] private string _leftDown = "left-down";

        [Header("90 angle states")] 
        [SerializeField] private string _upIdle = "charAnim/up-idle";
        [SerializeField] private string _upRowing = "charAnim/up-rowing";
        [SerializeField] private string _upVictory = "charAnim/up-victory";
        [SerializeField] private string _upLeft = "up-left";
        [SerializeField] private string _upRight = "up-right";
        
        [Header("270 angle states")]
        [SerializeField] private string _downIdle = "charAnim/down-idle";
        [SerializeField] private string _downRowing = "charAnim/down-rowing";
        [SerializeField] private string _downVictory = "charAnim/down-victory";
        [SerializeField] private string _downLeft = "down-left";
        [SerializeField] private string _downRight = "down-right";
        
        private int _currentOrientation;
        private const int MinimumPathLength = 2;

        public void InitialSetup()
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            rect.anchorMin = new Vector2();
            rect.anchorMax = _anchorMax;
            rect.offsetMin = new Vector2();
            rect.offsetMax = new Vector2();
        }

        public override void PlayPreview()
        {
            _sk.AnimationState.SetAnimation(0, _preview, true);
            base.PlayPreview();
        }
        
        public void SetupGondola(int orientation)
        {
            _sk.gameObject.SetActive(true);
            _waterFx.gameObject.SetActive(true);
            GetAnimations(orientation, orientation, out _, out var afterAnimations);
            _currentOrientation = orientation;
            _sk.AnimationState.SetAnimation(0, afterAnimations.Idle, true);
        }
        
        public void MoveGondola(RectTransform rectTransform, List<Vector3> path, List<int> orientation, bool won, bool fastSpeed, FxRenderer fxRenderer, Coords goalCoords, Action callback)
        {
            var duration = Mathf.Min(path.Count * _gondolaBaseSpeedMultiplier, _gondolaMaxSpeedLimit);
            
            if (fastSpeed)
            {
                duration = _gondolaSpeedNormal;
            }
            
            path[0] = rectTransform.localPosition;
            var tweener = rectTransform.DOLocalPath(path.ToArray(), duration);
            tweener.OnWaypointChange(AnimationUpdates);
            tweener.OnComplete(CompleteAction);
            fxRenderer.PlayHapticSequence(ImpactPreset.LightImpact, path.Count - 1, duration);
            return;

            void AnimationUpdates(int x)
            {
                var beforeOrientation = _currentOrientation;
                _currentOrientation = orientation[x];

                GetAnimations(beforeOrientation, _currentOrientation, out _, out var afterAnimations);
                AudioProxy.PlaySound(Match3SoundIds.GondolaPaddle);
                _sk.AnimationState.SetAnimation(0,
                    afterAnimations.Turn == string.Empty ? afterAnimations.Rowing : afterAnimations.Turn, false);

                PlaySupplementalAnimation(afterAnimations);

                //Trigger Flag Animation just before the gondola reaches the goal cell
                if (won && x == path.Count - MinimumPathLength)
                {
                    fxRenderer.PlayGondolaFlagAnimation(goalCoords);
                }
            }

            void PlaySupplementalAnimation(Animations afterAnimations)
            {
                if (won)
                {
                    _sk.AnimationState.AddAnimation(0, afterAnimations.Won, false, _idleDelay);
                }
                else
                {
                    _sk.AnimationState.AddAnimation(0, afterAnimations.Idle, true, _idleDelay);
                }
            }
            

            void CompleteAction()
            {
                try
                {
                    callback.SafeInvoke();
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.General, "Exception in Move Gondola " + e.Message + " Stack: " + e.StackTrace);
                }
            }
        }
        
        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action callback)
        {
            yield return WaitCache.Seconds(_celebrationDelay);
            _sk.gameObject.SetActive(false);
            _waterFx.gameObject.SetActive(false);
            callback?.SafeInvoke();
        }
        
        private void GetAnimations(int before, int after, out Animations beforeAnimations, out Animations afterAnimations)
        {
            var animations = new Dictionary<int, (string rowing, string won, string idle)>
            {
                {0, (_rightRowing, _rightVictory, _rightIdle)},
                {90, (_upRowing, _upVictory, _upIdle)},
                {180, (_leftRowing, _leftVictory,  _leftIdle)},
                {270, (_downRowing, _downVictory, _downIdle)}
            };
            
            var turns = new Dictionary<int, Dictionary<int, string>>
            {
                {0, new Dictionary<int, string> {{90, _rightUp}, {270, _rightDown}}},
                {90, new Dictionary<int, string> {{0, _upRight}, {180, _upLeft}}},
                {180, new Dictionary<int, string> {{90, _leftUp}, {270, _leftDown}}},
                {270, new Dictionary<int, string> {{0, _downRight}, {180, _downLeft}}}
            };
            
            var turn = before == after ? string.Empty : turns[before][after];
            
            beforeAnimations = new Animations
            {
                Won = animations[before].won,
                Rowing = animations[before].rowing,
                Idle = animations[before].idle,
                Turn = turn
            };
            
            afterAnimations = new Animations
            {
                Won = animations[after].won,
                Rowing = animations[after].rowing,
                Idle = animations[after].idle,
                Turn = turn
            };
        }
        
        private struct Animations
        {
            public string Won;
            public string Rowing;
            public string Idle;
            public string Turn;
        }
    }
}
