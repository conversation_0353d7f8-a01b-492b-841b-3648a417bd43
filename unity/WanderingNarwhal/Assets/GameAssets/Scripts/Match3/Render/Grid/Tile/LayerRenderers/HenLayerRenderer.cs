using System;
using System.Collections;
using BBB.Audio;
using BebopBee.Core.Audio;
using Spine;
using Spine.Unity;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class HenLayerRenderer : TileLayerRendererBase
    {
        private const int HenLayerIndex = 2;
        private const int EggLayerIndex = 1;

        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _appearAnimName = "Appear";

        [SerializeField]
        private string _hitLayer2AnimName = "Match";

        [SerializeField]
        private string _idleLayer2AnimName = "Idle";

        [SerializeField]
        private string _idleLayer1AnimName = "Idle_eggs";

        [SerializeField]
        private string _destroyAnimName = "Brake_eggs";

        [SerializeField]
        private ParticleSystem _layer2HitParticles;

        [SerializeField]
        private float _zCoordFromYPosition = 0.01f;

        [SerializeField]
        private string _tapFeedbackName = "Tap";

        [SerializeField]
        private string _previewAnimName = "";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        [SerializeField]
        private string _destroySoundUid = "StickerDestroy";
        
        private int _prevLayer;
        private Action _currentOnDestroyCallback;

        public void SetLayer(int layer)
        {
            switch (layer)
            {
                case >= HenLayerIndex:
                    PlayIdle();
                    break;
                case EggLayerIndex:
                {
                    AudioProxy.PlaySound(Match3SoundIds.HenHit);
                    
                    if (layer < _prevLayer)
                    {
                        _layer2HitParticles.Play();
                        _sk.AnimationState.SetAnimation(0, _hitLayer2AnimName, loop: false);
                        _sk.AnimationState.AddAnimation(0, _idleLayer1AnimName, loop: true, delay: 0);
                    }
                    else
                    {
                        _sk.AnimationState.SetAnimation(0, _idleLayer1AnimName, loop: true);
                    }

                    break;
                }
            }

            _prevLayer = layer;
        }

        public void PlayHenAppear()
        {
            _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
        }

        private void PlayIdle()
        {
            var idleName = _prevLayer >= HenLayerIndex ? _idleLayer2AnimName : _idleLayer1AnimName;
            _sk.AnimationState.SetAnimation(0, idleName, loop: true);
            _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: Random.Range(0f, 3f));
        }

        public void PlayDestroy(Action onDone)
        {
            AudioProxy.PlaySound(Match3SoundIds.HenHitEgg);
            _currentOnDestroyCallback = onDone;
            _sk.AnimationState.ClearTracks();
            _sk.AnimationState.SetAnimation(0, _destroyAnimName, loop: false);
            _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            _sk.AnimationState.Complete += OnDestroyAnimationEnd;
            AudioProxy.PlaySound(_destroySoundUid);
        }

        private void OnDestroyAnimationEnd(TrackEntry entry)
        {
            if (_sk != null)
            {
                _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            }

            if (_layer2HitParticles.isPlaying)
            {
                StartCoroutine(WaitParticlesDisappearAndCallDestroyCallback());
            }
            else
            {
                CallOnDestroyCallback();
            }
        }

        private IEnumerator WaitParticlesDisappearAndCallDestroyCallback()
        {
            Hide();
            do
            {
                yield return null;
            } while (_layer2HitParticles.isPlaying && _layer2HitParticles.particleCount > 0);

            CallOnDestroyCallback();
        }

        private void CallOnDestroyCallback()
        {
            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }

        protected override void OnDisable()
        {
            if (_currentOnDestroyCallback == null)
                return;
            
            _currentOnDestroyCallback.Invoke();
            _currentOnDestroyCallback = null;
        }

        public void OnSpawn()
        {
            UnHide();
            if (_sk.AnimationState == null)
            {
                _sk.Initialize(true);
            }

            var idleName = _prevLayer >= HenLayerIndex ? _idleLayer2AnimName : _idleLayer1AnimName;
            if (!_appearAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, idleName, loop: true);
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_prevLayer == HenLayerIndex)
            {
                if (_tapFeedbackName.IsNullOrEmpty()) return;

                _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
                if (!_idleLayer2AnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.AddAnimation(0, _idleLayer2AnimName, loop: true, delay: 0);
                }
            }
            else
            {
                base.PlayTapFeedback(layerView);
            }
        }

        public override void PlayPreview()
        {
            UnHide();
            if (_previewAnimName.IsNullOrEmpty())
            {
                PlayIdle();
            }
            else
            {
                var idleName = _prevLayer >= HenLayerIndex ? _idleLayer2AnimName : _idleLayer1AnimName;
                _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
            }

            if (_layer2HitParticles != null)
            {
                _layer2HitParticles.Stop();
            }
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }
    }
}