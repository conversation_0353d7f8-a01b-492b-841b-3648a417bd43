using System;
using System.Collections;
using BBB.Core;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class IceBarLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private RectTransform _content;
        [SerializeField] private RectTransform _bucket;
        [SerializeField] private GameObject _iceBarClip;
        [SerializeField] private RectTransform _iceBar;
        [SerializeField] private ActivePoolItem _iceBarActivePoolItem;
        [SerializeField] public float _destroyDuration = 1f;
        [SerializeField] public float _iceBarShrinkTime = 1f;
        
        private static readonly int Shake = Animator.StringToHash("Shake");
        private int _orientation;
        
        public void PlayDestroy(Action onDone)
        {
            UpdateSize(1, 1, _orientation, true);
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        public void InitialSetup()
        {
            if (_iceBarActivePoolItem != null)
            {
                _iceBarActivePoolItem.OnSpawn();
            }
        }
        
        public override void PlayPreview()
        {
            UpdateSize(1, 1, 0, false);
            base.PlayPreview();
        }
        
        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_iceBarShrinkTime);
            if (_iceBarActivePoolItem != null)
            {
                _iceBarActivePoolItem.OnRelease();
            }
            onDone.SafeInvoke();
        }

        public void UpdateSize(int sizeX, int sizeY, int orientation, bool animate, bool showClip = true)
        {
            _orientation = orientation;
            _content.pivot = new Vector2(0.5f, 0.5f);
            
            var sizeDelta = _iceBar.sizeDelta;
            var multiplier = sizeDelta;
            
            var iceBarDefaultLength =  CellSize.x * 0.96f;
            var iceBarPerTileLength =  CellSize.x * 0.96f + 2;

            _iceBarClip.SetActive(showClip);

            _content.rotation = Quaternion.Euler(0, 0, orientation);

            var rotation = _bucket.rotation;
            var rot = Quaternion.Euler(rotation.x, rotation.y, 0);
            _bucket.rotation = rot;

            if (sizeX == 1 && sizeY > 0)
            {
                multiplier.x = iceBarDefaultLength + (sizeY - 2) * iceBarPerTileLength;
            }
            if (sizeY == 1 && sizeX > 0 )
            {
                multiplier.x = iceBarDefaultLength + (sizeX - 2) * iceBarPerTileLength;
            }

            if (sizeX == 0 || sizeY == 0)
            {
                return;
            }
            
            if (animate)
            {
                _animator.enabled = true;
                _animator.SetTrigger(Shake);
                StartCoroutine(MoveObject(_iceBar, sizeDelta, multiplier, _iceBarShrinkTime));
            }
            else
            {
                _iceBar.sizeDelta = multiplier;
            }
        }

        private static IEnumerator MoveObject(RectTransform localObject, Vector2 startPos, Vector2 endPos, float time)
        {
            var lerpTime = 0.0f;
            var rate = 1.0f / time;
            while (lerpTime < 1.0f)
            {
                lerpTime += Time.deltaTime * rate;
                localObject.sizeDelta = Vector3.Lerp(startPos, endPos, lerpTime);
                yield return null;
            }
            localObject.sizeDelta = endPos;
        }
    }
}
