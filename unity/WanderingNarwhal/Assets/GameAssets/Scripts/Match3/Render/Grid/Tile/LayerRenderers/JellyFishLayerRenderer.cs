using System;
using System.Collections;
using System.Collections.Generic;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using Spine.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class JellyFishLayerRenderer : DelayedAppearLayerRenderer
    {

        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private Animator _animator;
        [SerializeField] private List<TileColor> _tileColors;
        [SerializeField] private List<Image> _backgroundColors;
        [SerializeField] private List<Image> _foregroundColors;
        [SerializeField] private float _destroyDuration = 1.0f;
        [SerializeField] private TileKinds _helpPanelColor = TileKinds.Blue;
        [SerializeField] private float _destroyTileFXDuration = 1.0f;
        [SerializeField] private float _tileHelpMinOffset = 0.35f;
        [SerializeField] private float _tileHelpMaxOffset = 1.3f;
        
        private const string LoopAnimation = "loop_";
        private const string ShakeAnimation = "shake_";
        //There are in total 7 JellyFish/ 7 HP to destroy
        private const int TotalNumberOfJellyFish = 7;


        public float DestroyTileFXDuration => _destroyTileFXDuration;

        public void InitialSetup()
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            rect.pivot = new Vector2(0, 0);
            rect.anchorMin = new Vector2();
            rect.anchorMax = new Vector2(2.15f, 2.15f);
            rect.offsetMin = new Vector2();
            rect.offsetMax = new Vector2();
        }

        public void Setup(TileKinds tileKinds, bool playLoop)
        {
            _sk.Skeleton.SetSkin(tileKinds.GetSkinColor());
            if (_foregroundColors != null)
            {
                var currentColor = GetCurrentColor(tileKinds, true);
                foreach (var image in _foregroundColors)
                {
                    image.color = currentColor;
                }
            }
            
            if (_backgroundColors != null)
            {
                var currentColor = GetCurrentColor(tileKinds, false);
                foreach (var image in _backgroundColors)
                {
                    image.color = currentColor;
                }
            }
            
            if (playLoop)
            {
                _sk.AnimationState.AddAnimation(0, LoopAnimation + TotalNumberOfJellyFish, true, 0);
            }
        }

        public void PlayHit(int num)
        {
            if(num <= 0) return;
            
            _sk.AnimationState.SetAnimation(0, ShakeAnimation + num , false);
            _sk.AnimationState.AddAnimation(0, LoopAnimation + num, true, 0);
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_destroyDuration);
            onDone.SafeInvoke();
        }
        
        public override void PlayPreview()
        {
            Setup(_helpPanelColor, true);
            Show();
            UpdateSize(1);
            base.PlayPreview();
        }

        private void UpdateSize(int size)
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            if (size == 1)
            {
                // This is used for the preview in tile help panel
                rect.anchorMin = -Vector2.one * _tileHelpMinOffset;
                rect.anchorMax = Vector2.one * _tileHelpMaxOffset;
            }
            else
            {
                rect.anchorMin = Vector2.zero;
                rect.anchorMax = Vector2.one * 2;
            }
            rect.anchoredPosition = Vector2.zero;
            FitBaseRectToExactCellSize();
        }

        public Color GetCurrentColor(TileKinds col, bool foreground)
        {
            return foreground ? _tileColors.Find(a => a.TileKind == col).ForegroundColor : _tileColors.Find(a => a.TileKind == col).BackgroundColor;
        }
        
        public override void Show()
        {
            base.Show();
            _animator.StopPlayback();
            _animator.ResetAllParameters();
        }
        
        [Serializable]
        public class TileColor
        {
            public TileKinds TileKind;
            public Color BackgroundColor;
            public Color ForegroundColor;
        }
    }
}
