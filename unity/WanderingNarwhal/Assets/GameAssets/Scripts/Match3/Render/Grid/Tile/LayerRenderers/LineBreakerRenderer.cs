using System;
using System.Collections.Generic;
using BBB.Match3.Renderer;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB
{
    public class LineBreakerRenderer : TileLayerRendererBase, ISpecialTileRenderer, IFormableTileRenderer
    {
        [SerializeField] private SpriteRenderer _bodyImage;
        [SerializeField] private SpriteRenderer _firstArrowImage;
        [SerializeField] private SpriteRenderer _secondArrowImage;
        [SerializeField] private List<SpriteRenderer> _allSpriteRenderers;
        [SerializeField] private ParticleSystem _splashParticleSystem;
        [SerializeField] private Animator _animator;
        [SerializeField] private GameObject[] _rowBreakerObjects;
        [SerializeField] private GameObject[] _columnBreakerObjects;
        
        private Action _introCallback;
        public Vector3 GetGlobalScale()
        {
            return transform.lossyScale;
        }

        public void PlayFlight()
        {
            
        }

        public void PlaySwap()
        {
            
        }

        public void SetAlpha(float value)
        {
            _allSpriteRenderers.ForEach(sr =>
            {
                var color = sr.color;
                color.a = value;
                sr.color = color;
            });
        }

        public int SortingLayer
        {
            set
            {
                _allSpriteRenderers.ForEach(sr => { sr.sortingLayerID = value; });
            }
        }

        public void PlayPreactivate()
        {
            
        }

        public void Setup(M3Settings m3Settings, TileSpeciality tileSpeciality)
        {
            SetupBreakers(tileSpeciality);
        }

        private void SetupBreakers(TileSpeciality tileSpeciality)
        {
            if (_rowBreakerObjects is { Length: > 0 })
            {
                foreach (var rowBreaker in _rowBreakerObjects)
                {
                    rowBreaker.SetActive(tileSpeciality == TileSpeciality.RowBreaker);
                }
            }

            if (_columnBreakerObjects is { Length: > 0 })
            {
                foreach (var columnBreaker in _columnBreakerObjects)
                {
                    columnBreaker.SetActive(tileSpeciality == TileSpeciality.ColumnBreaker);
                }
            }
        }

        public void PlayStandardFormation()
        {
            _animator.SetTrigger(FormationTriggerHash);
            if(_splashParticleSystem != null)
                _splashParticleSystem.Play(false);
        }

        public void PlayFormationByBolt()
        {
            _animator.SetTrigger(FormationByBoltTriggerHash);
        }

        public void PlayBoltPreactivate()
        {
            _animator.SetTrigger(BoltComboTriggerHash);
        }

        public void SetupCallbackOnIntro(Action introCallback)
        {
            _introCallback = introCallback;
        }

        //called from animation clip
        public void IntroDoneEventHandler()
        {
            _introCallback.SafeInvoke();
            _introCallback = null;
        }
    }
}