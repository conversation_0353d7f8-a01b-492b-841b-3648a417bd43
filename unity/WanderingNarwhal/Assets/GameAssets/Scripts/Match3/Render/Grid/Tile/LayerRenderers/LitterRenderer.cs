using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class LitterRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SpriteRenderer _fullSpriteRenderer;

        [SerializeField]
        private List<SpriteRenderer> _partsSpriteRenderers;

        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private ParticleSystemRenderer _skinParticles;

        [SerializeField]
        private Material _particlesGreen;

        [SerializeField]
        private Material _particlesPurple;

        [SerializeField]
        private Material _particlesYellow;
        
        [SerializeField]
        private ParticleSystemRenderer _skinBigParticles;

        [SerializeField]
        private Material _particlesBigGreen;

        [SerializeField]
        private Material _particlesBigPurple;

        [SerializeField]
        private Material _particlesBigYellow;

        [SerializeField]
        private string _spineSkinNameGreen = "GreenWithPattern";

        [SerializeField]
        private string _spineSkinNamePurple = "PurpleWithPattern";

        [SerializeField]
        private string _spineSkinNameYellow = "YellowWithPattern";

        [SerializeField]
        private string _idleAnimName = "Idle";

        [SerializeField]
        private string _matchAnimName = "Match";

        [SerializeField]
        private string _tapFeedbackAnimName = "";

        private Action _currentDestroyCallback;

        public void Setup(LitterRenderVariant renderVariant)
        {
            if (_fullSpriteRenderer != null)
            {
                _fullSpriteRenderer.sprite = renderVariant.FullSprite;
            }

            if (_partsSpriteRenderers.Count == renderVariant.PartsSprites.Count)
            {
                for (int i = 0; i < renderVariant.PartsSprites.Count; i++)
                {
                    if (_partsSpriteRenderers[i] != null)
                    {
                        _partsSpriteRenderers[i].sprite = renderVariant.PartsSprites[i];
                    }
                }
            }

            if (_skinParticles != null)
            {
                Material mat = null;
                switch (renderVariant.LitterSkin)
                {
                    case LitterSkin.Green:
                    case LitterSkin.GreenWithPattern:
                        mat = _particlesGreen;
                        break;
                    case LitterSkin.Purple:
                    case LitterSkin.PurpleWithPattern:
                        mat = _particlesPurple;
                        break;
                    case LitterSkin.Yellow:
                    case LitterSkin.YellowWithPattern:
                        mat = _particlesYellow;
                        break;
                }

                if (mat != null)
                {
                    _skinParticles.material = mat;
                }
            }
            
            if (_skinBigParticles != null)
            {
                Material mat = null;
                switch (renderVariant.LitterSkin)
                {
                    case LitterSkin.Green:
                    case LitterSkin.GreenWithPattern:
                        mat = _particlesBigGreen;
                        break;
                    case LitterSkin.Purple:
                    case LitterSkin.PurpleWithPattern:
                        mat = _particlesBigPurple;
                        break;
                    case LitterSkin.Yellow:
                    case LitterSkin.YellowWithPattern:
                        mat = _particlesBigYellow;
                        break;
                }

                if (mat != null)
                {
                    _skinBigParticles.material = mat;
                }
            }

            if (_sk != null)
            {
                _sk.gameObject.SetActive(true);
                var skinName = _spineSkinNameGreen;
                switch (renderVariant.LitterSkin)
                {
                    case LitterSkin.Green:
                    case LitterSkin.GreenWithPattern:
                        skinName = _spineSkinNameGreen;
                        break;
                    case LitterSkin.Purple:
                    case LitterSkin.PurpleWithPattern:
                        skinName = _spineSkinNamePurple;
                        break;
                    case LitterSkin.Yellow:
                    case LitterSkin.YellowWithPattern:
                        skinName = _spineSkinNameYellow;
                        break;
                }

                var skin = _sk.SkeletonData.FindSkin(skinName);
                if (skin != null)
                {
                    _sk.Skeleton.SetSkin(skin);
                    _sk.Skeleton.SetToSetupPose();
                }

                if (!_idleAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
                    _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: UnityEngine.Random.Range(0, 3f));
                }
            }
        }

        public void ResetRenderer()
        {
            if (_sk != null)
            {
                _sk.AnimationState.ClearTracks();
                _sk.Skeleton.SetToSetupPose();
                _sk.gameObject.SetActive(false);
            }
        }
        public void PlayDestroy(Action onDone)
        {
            if (string.IsNullOrEmpty(_matchAnimName) || _sk == null)
            {
                onDone?.Invoke();
                return;
            }

            _sk.AnimationState.ClearTracks();
            _sk.AnimationState.SetAnimation(0, _matchAnimName, loop: false);
            _currentDestroyCallback = onDone;
            _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            _sk.AnimationState.Complete += OnDestroyAnimationEnd;
        }

        private void OnDestroyAnimationEnd(TrackEntry entry)
        {
            if (_sk != null)
            {
                _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
                ResetRenderer();
            }

            _currentDestroyCallback?.Invoke();
            _currentDestroyCallback = null;
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_sk == null || string.IsNullOrEmpty(_tapFeedbackAnimName))
                return;
            
            _sk.AnimationState.SetAnimation(0, _tapFeedbackAnimName, loop: false);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
        }

        protected override void OnDisable()
        {
            if (_currentDestroyCallback == null)
                return;
            
            _currentDestroyCallback?.Invoke();
            _currentDestroyCallback = null;
        }
    }
}