using System;
using System.Collections;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Utils;
using Spine;
using Spine.Unity;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class MagicHatLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _appearAnimName = "Appear";

        [SerializeField]
        private string _hitAnimName = "Match";

        [SerializeField]
        private string _idleAnimName = "Idle";

        [SerializeField]
        private string _idleOutOfRabbitsAnimName = "magic_hat_idle_closed";

        [SerializeField]
        private string _outOfRabbits = "magic_hat_idle_closed";

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string _tapFeedbackName = "Match";

        [SerializeField]
        private string _visitorPreviewStateName = "";
        
        [SerializeField]
        private float _magicHatFlipDelay = 1f;

        private Action _currentOnDestroyCallback;

        private bool _isOutOfRabbits;
        private bool _outOfRabbitsAnimationPlaying;

        public bool IsOutOfRabbits
        {
            get => _isOutOfRabbits;
            set
            {
                _isOutOfRabbits = value;
                
                if (!_isOutOfRabbits) return;
                if (!_outOfRabbitsAnimationPlaying)
                {
                    _outOfRabbitsAnimationPlaying = true;
                    _sk.AnimationState.SetAnimation(0, _outOfRabbits, loop: false);
                    _sk.AnimationState.AddAnimation(0, _idleOutOfRabbitsAnimName, loop: true, 0.9f);
                }
            }
        }

        public override void Init()
        {
            base.Init();
            _sk.AnimationState.ClearTracks();
            _sk.Skeleton.SetToSetupPose();
        }

        public override void PlayAppear()
        {
            _isOutOfRabbits = false;
            _outOfRabbitsAnimationPlaying = false;
            if (!_appearAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
        }

        public void PlayIdle()
        {
            if (_isOutOfRabbits)
            {
                var current = _sk.AnimationState.GetCurrent(0);
                if (current is { Animation: null } || current.Animation.Name != _hitAnimName)
                {
                    // Skip idle if currently playing hit, which will then switch to idle by itself.
                }
                else if (!_outOfRabbitsAnimationPlaying)
                {
                    _outOfRabbitsAnimationPlaying = true;
                    Rx.Invoke(_magicHatFlipDelay, _ =>
                    {
                        _sk.AnimationState.SetAnimation(0, _outOfRabbits, loop: false);
                        _sk.AnimationState.AddAnimation(0, _idleOutOfRabbitsAnimName, loop: true, 0.9f);
                    });
                }
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, Random.Range(0f, 3f));
            }
        }

        public void PlayHit()
        {
            if (!_isOutOfRabbits)
            {
                AudioProxy.PlaySound(Match3SoundIds.MagicHatHit);
                _outOfRabbitsAnimationPlaying = false;
                _sk.AnimationState.SetAnimation(0, _hitAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            
                _sk.AnimationState.Start -= OnNextAnimStart;
                _sk.AnimationState.Start += OnNextAnimStart;
            }
            else
            {
                PlayIdle();
            }
        }

        private void OnNextAnimStart(TrackEntry trackEntry)
        {
            if (trackEntry.Animation.Name == _idleAnimName)
            {
                _sk.AnimationState.Start -= OnNextAnimStart;
                if (_isOutOfRabbits)
                {
                    _outOfRabbitsAnimationPlaying = true;
                    _sk.AnimationState.SetAnimation(0, _outOfRabbits, loop: false);
                    _sk.AnimationState.AddAnimation(0, _idleOutOfRabbitsAnimName, loop: true, 0.9f);
                }
            }
            else if (trackEntry.Animation.Name == _idleOutOfRabbitsAnimName)
            {
                _sk.AnimationState.Start -= OnNextAnimStart;
                if (!_isOutOfRabbits)
                {
                    _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
                }
            }
        }

        public void PlayDestroy(Action onDone)
        {
            _currentOnDestroyCallback = onDone;
            StartCoroutine(DestroyCallbackRoutine(0.1f));
        }

        private IEnumerator DestroyCallbackRoutine(float delay)
        {
            yield return WaitCache.Seconds(delay);
            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }
        
        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;
            if (_isOutOfRabbits) return;
            
            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }
        
        protected override void OnDisable()
        {
            if (_currentOnDestroyCallback != null)
            {
                _currentOnDestroyCallback.Invoke();
                _currentOnDestroyCallback = null;
            }

            IsOutOfRabbits = false;
        }
    }
}