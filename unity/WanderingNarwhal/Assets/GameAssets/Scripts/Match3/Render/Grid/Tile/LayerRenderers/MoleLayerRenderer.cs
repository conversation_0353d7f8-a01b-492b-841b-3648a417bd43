using System;
using BBB.Audio;
using BebopBee.Core.Audio;
using Spine;
using Spine.Unity;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class MoleLayerRenderer : DelayedAppearLayerRenderer
    {
        private static readonly int MoleBaseDestroy = Animator.StringToHash("Destroy");

        [SerializeField]
        private SkeletonGraphic _sk;
        
        [SerializeField] private Canvas _baseCanvas;
        [SerializeField] private Canvas _moleTileBgCanvas ;

        [SerializeField]
        private string _animLayer1IdleEmotion1 = "Idle_2_Regular";

        [SerializeField]
        private string _animLayer1IdleEmotion2 = "Idle_2_Happy";

        [SerializeField]
        private string _animLayer1IdleEmotion3 = "Idle_2_Smile";

        [SerializeField]
        private string _animLayer2Idle = "Idle_1_MoleIsHiding";

        [SerializeField]
        private string _animMatchEmotion1 = "Match_Regular";

        [SerializeField]
        private string _animMatchEmotion2 = "Match_Happy";

        [SerializeField]
        private string _animMatchEmotion3 = "Match_Smile";

        [SerializeField]
        private string _animHideEmotion1 = "Hide_Regular";

        [SerializeField]
        private string _animHideEmotion2 = "Hide_Happy";

        [SerializeField]
        private string _animHideEmotion3 = "Hide_Smile";

        [SerializeField]
        private string _animDestroy = "Destroy";

        [SerializeField]
        private string _animAppear = "Appear";

        [SerializeField]
        private string _animTapFeedbackLayer1 = "Tap_1_MoleIsHiding";

        [SerializeField]
        private string _animTapFeedbackLayer2 = "Tap_2_Idle";

        [SerializeField]
        private string[] _skinNames = Array.Empty<string>();

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";
        
        [SerializeField]
        private Animator _moleBaseAnimator;

        private Action _currentOnDestroyCallback;

        private string AnimMatchCurrentEmotion
        {
            get
            {
                var index = CurrentSkinIndex % 3;
                return index switch
                {
                    2 => _animMatchEmotion3,
                    1 => _animMatchEmotion2,
                    _ => _animMatchEmotion1
                };
            }
        }

        private string AnimIdleLayer1CurrentEmotion
        {
            get
            {
                var index = CurrentSkinIndex % 3;
                return index switch
                {
                    2 => _animLayer1IdleEmotion3,
                    1 => _animLayer1IdleEmotion2,
                    _ => _animLayer1IdleEmotion1
                };
            }
        }

        private string AnimHideCurrentEmotion
        {
            get
            {
                var index = CurrentSkinIndex % 3;
                return index switch
                {
                    2 => _animHideEmotion3,
                    1 => _animHideEmotion2,
                    _ => _animHideEmotion1
                };
            }
        }

        private string CurrentSkinName
        {
            get
            {
                var index = Mathf.Clamp(CurrentSkinIndex / 3, 0, _skinNames.Length - 1);
                return _skinNames[index];
            }
        }

        private int _lastLayer;

        private int _skinIndex;
        public int CurrentSkinIndex
        {
            get => _skinIndex;
            set
            {
                _skinIndex = value;
                RefreshSkin();
            } 
        }

        public void SetLayer(int layer)
        {
            RefreshSkin();
            if (layer == _lastLayer)
            {
                return;
            }

            switch (_lastLayer)
            {
                case 1 when layer == 2:
                {
                    // Restore anim 1->2.
                    var hideAnimName = AnimHideCurrentEmotion;
                    var idleName = _animLayer2Idle;

                    _sk.AnimationState.SetAnimation(0, hideAnimName, loop: false);
                    _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
                    break;
                }
                case 2 when layer == 1:
                {
                    AudioProxy.PlaySound(Match3SoundIds.MoleAppear);
                    var matchAnim = AnimMatchCurrentEmotion;
                    var idleName = AnimIdleLayer1CurrentEmotion;

                    _sk.AnimationState.SetAnimation(0, matchAnim, loop: false);
                    _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
                    break;
                }
                default:
                {
                    var idleName = layer == 1 ? AnimIdleLayer1CurrentEmotion : _animLayer2Idle;
                    _sk.AnimationState.SetAnimation(0, idleName, loop: true);
                    _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: Random.Range(0f, 8f));
                    break;
                }
            }

            _lastLayer = layer;
        }

        public override void Init()
        {
            base.Init();
            if (_sk != null)
            {
                _sk.Initialize(false);
                _sk.AnimationState.ClearTracks();
                _sk.Skeleton.SetToSetupPose();
            }
        }


        private void RefreshSkin()
        {
            var skinName = CurrentSkinName;
            var skin = _sk.SkeletonData.FindSkin(skinName);
            if (skin == null) return;
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetToSetupPose();
        }

        public void PlayDestroy(Action onDone)
        {
            _currentOnDestroyCallback = onDone;
            _sk.AnimationState.ClearTracks();
            _sk.AnimationState.SetAnimation(0, _animDestroy, loop: false);
            _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            _sk.AnimationState.Complete += OnDestroyAnimationEnd;
            _moleBaseAnimator.SafeSetTrigger(MoleBaseDestroy);
            AudioProxy.PlaySound(Match3SoundIds.MoleDestroy);
        }

        protected override void OnAppearAfterDelayBeforeBaseCall()
        {
            var matchAnim = AnimMatchCurrentEmotion;
            var idleName = _lastLayer == 1 ? AnimIdleLayer1CurrentEmotion : _animLayer2Idle;

            _sk.AnimationState.SetAnimation(0, matchAnim, loop: false);
            _sk.AnimationState.AddAnimation(0, _animAppear, loop: false, delay: 0);
            _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
            _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: Random.Range(0f, 8f));
        }

        private void OnDestroyAnimationEnd(TrackEntry entry)
        {
            if (_sk != null)
            {
                _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            }

            CallOnDestroyCallback();
        }

        private void CallOnDestroyCallback()
        {
            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }

        protected override void OnDisable()
        {
            if (_currentOnDestroyCallback != null)
            {
                _currentOnDestroyCallback.Invoke();
                _currentOnDestroyCallback = null;
            }
            _lastLayer = 0;
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            base.PlayTapFeedback(layerView);
            var tapName = _lastLayer == 1 ? _animTapFeedbackLayer1 : _animTapFeedbackLayer2;
            _sk.AnimationState.SetAnimation(0, tapName, loop: false);
            var idleName = _lastLayer == 1 ? AnimIdleLayer1CurrentEmotion : _animLayer2Idle;
            _sk.AnimationState.AddAnimation(0, idleName, loop: true, delay: 0);
        }

        public override void PlayPreview()
        {
            Show();
            var idleName = _lastLayer == 1 ? AnimIdleLayer1CurrentEmotion : _animLayer2Idle;
            _sk.AnimationState.SetAnimation(0, idleName, loop: true);
        }

        public override void PlayVisitorPreview()
        {
            if (_visitorPreviewStateName.IsNullOrEmpty() || _sk == null) return;
            if (_sk.AnimationState == null)
            {
                _sk.Initialize(false);
            }

            Show();
            CurrentSkinIndex = 2;
            _sk.AnimationState?.SetAnimation(0, _visitorPreviewStateName, loop: false);
        }


        public override void Show()
        {
            _moleTileBgCanvas.sortingOrder = _baseCanvas.sortingOrder - 1;
        }
    }
}