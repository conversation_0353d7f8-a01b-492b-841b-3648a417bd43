using BBB.Match3.Renderer;
using UnityEngine;

namespace BBB
{
    public class NormalTileRenderer : TileLayerRendererBase
    {
        [SerializeField] private SpriteRenderer _tileImage;

        public float Alpha
        {
            set
            {
                var color = _tileImage.color;
                color.a = value;
                _tileImage.color = color;
            }
        }

        public int SortLayer
        {
            set { _tileImage.sortingLayerID = value; }
        }

        public void AddToSortOrder(int increment)
        {
            _tileImage.sortingOrder += increment;
        }

        public void RemoveFromSortOrder(int decrement)
        {
            _tileImage.sortingOrder -= decrement;
        }

        public void SetData(TileViewData data)
        {
            _tileImage.sprite = data.Sprite;
            if (data.CellSize != default)
            {
                _tileImage.transform.localScale = new Vector3(data.CellSize.x, data.CellSize.y, data.CellSize.x);
            }
        }
    }
}
