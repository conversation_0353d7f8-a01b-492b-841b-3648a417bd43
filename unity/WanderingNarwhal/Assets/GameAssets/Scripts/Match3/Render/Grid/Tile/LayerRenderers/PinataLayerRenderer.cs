using System;
using <PERSON><PERSON>;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class PinataLayerRenderer : TileLayerRendererBase
    {
        [SerializeField] private SkeletonGraphic _sk;

        [SerializeField] private string _idleAnimName;
        [SerializeField] private string _destroyAnimName;

        private Action _currentOnDestroyCallback;


        public void PlayIdle()
        {
            _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0.001f);
        }

        public void PlayDestroy(Action onDone)
        {
            _currentOnDestroyCallback = onDone;

            _sk.AnimationState.SetAnimation(0, _destroyAnimName, loop: false);
            _sk.AnimationState.Complete -= OnAnimEnd;
            _sk.AnimationState.Complete += OnAnimEnd;
        }

        private void OnAnimEnd(TrackEntry trackEntry)
        {
            if (_currentOnDestroyCallback == null)
                return;

            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }

        protected override void OnDisable()
        {
            if (_currentOnDestroyCallback == null)
                return;

            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }
    }
}