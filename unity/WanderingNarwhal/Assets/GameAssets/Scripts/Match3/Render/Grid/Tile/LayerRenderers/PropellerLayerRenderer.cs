using System;
using BBB.Match3.Renderer;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Settings;
using Spine.Unity;
using UnityEngine;

namespace BBB
{
    public class PropellerLayerRenderer : TileLayerRendererBase, ISpecialTileRenderer, IFormableTileRenderer
    {
        private static readonly int SwapState = Animator.StringToHash("Swap");
        private static readonly int ShakingState = Animator.StringToHash("Shaking");
        private static readonly int FlightState = Animator.StringToHash("Flight");
        private static readonly int ExplosionTrigger = Animator.StringToHash("ExplosionTrigger");
        
        [SerializeField] private SpriteRenderer[] _spriteRenderers;
        [SerializeField] private MeshRenderer[] _meshRenderers;
        [SerializeField] private SkeletonMecanim _skeletonMecanim;
        [SerializeField] private AnimationEventCatcher _eventCatcher;
        [SerializeField] private Animator _animator;


        private Action _introCallback;

        public void SetAlpha(float value)
        {
            foreach (var sr in _spriteRenderers)
            {
                var color = sr.color;
                color.a = value;
                sr.color = color;
            }

            var skColor = _skeletonMecanim.Skeleton.GetColor();
            skColor.a = value;
            _skeletonMecanim.Skeleton.SetColor(skColor);
        }

        public int SortingLayer
        {
            set
            {
                foreach (var sr in _spriteRenderers)
                    sr.sortingLayerID = value;

                foreach (var mr in _meshRenderers)
                    mr.sortingLayerID = value;
            }
        }

        public void PlayPreactivate()
        {
            _animator.SetTrigger(ComboTriggerHash);
        }

        public void SetupCallbackOnIntro(Action introCallback)
        {
            _introCallback = introCallback;
        }

        public Vector3 GetGlobalScale()
        {
            return transform.lossyScale;
        }

        public void Setup(M3Settings m3Settings, TileSpeciality tileSpeciality)
        {
            _introCallback = null;
            _eventCatcher.AnimationEventWithParameter -= AnimEventHandler;
            _eventCatcher.AnimationEventWithParameter += AnimEventHandler;
        }

        private void AnimEventHandler(string str, float arg2, int arg3)
        {
            switch (str)
            {
                case "intro":
                    _introCallback.SafeInvoke();
                    _introCallback = null;
                    break;
            }
        }

        public void ResetSpine()
        {
             _skeletonMecanim.Initialize(true);
             _animator.PlayMain();
        }

        public void PlayFlight()
        {
            _animator.Play(FlightState, -1, 0f);
            _animator.Update(Time.deltaTime);
        }

        public void PlaySwap()
        {
            _animator.Play(SwapState, -1, 0f);
        }

        public void PlayStandardFormation()
        {
            _animator.Play(IntroState, -1, 0f);
            _animator.Update(Time.deltaTime);
        }

        public void PlayFormationByBolt()
        {
            _animator.Play(ShakingState, -1, 0f);
            _animator.Update(Time.deltaTime);
        }

        public void PlayBoltPreactivate()
        {
            _animator.SetTrigger(BoltComboTriggerHash);
        }

        public void PlayDestroy()
        {
            _animator.SetTrigger(ExplosionTrigger);
        }
    }
}