using System;
using System.Collections;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Utils;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SheepLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _idle3Name = "Idle_body_3";
        [SerializeField]
        private string _idle2Name = "Idle_body_2";
        [SerializeField]
        private string _idle1Name = "Idle_body_1";
        [SerializeField]
        private string _hit2Name = "Match_body_2";
        [SerializeField]
        private string _hit1Name = "Match_body_3";
        [SerializeField]
        private string _hit0Name = "Match_body_4";
        [SerializeField]
        private string _destroyLeftName = "Destroy_Left";
        [SerializeField]
        private string _destroyRightName = "Destroy_Right";
        [SerializeField]
        private string _appearAnimName = "PopOut";

        [SerializeField]
        private ParticleSystem _hitParticles;

        [SerializeField]
        private ParticleSystem _destroyRightParticles;

        [SerializeField]
        private ParticleSystem _destroyLeftParticles;

        [SerializeField]
        private float _destroySideMoveDuration = 2.5f;

        [SerializeField]
        private AnimationCurve _destroySideMoveCurveX = AnimationCurve.Linear(0f, 0f, 1f, 900f);

        [SerializeField]
        private int _sortingOrderChangeOnDestroy = 10;
        

        [SerializeField]
        private string _tapFeedbackName = "Tap";

        [SerializeField]
        private string _previewAnimName = "";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        private string _currentIdleName;
        private int _currentLevel;
        private Action _currentOnDestroyCallback;

        public void SetViewLevel(int level)
        {
            _currentLevel = Mathf.Clamp(level, 1, 3);
            OnRefresh();
        }

        public override void ResetToDefault()
        {
            _currentIdleName = null;
            _currentLevel = 0;
            _currentOnDestroyCallback = null;
            base.ResetToDefault();
        }

        public override void OnRefresh()
        {
            DetermineCurrentIdleAnim();
            base.OnRefresh();

            if (!_hideUntilAppear || IsPlayedAppear)
            {
                _sk.AnimationState.SetAnimation(0, _currentIdleName, loop: true);

                // Add randomization of idle loop.
                _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true, delay: UnityEngine.Random.Range(0f, 3f));
            }
        }

        public override void Show()
        {
            base.Show();
            if (_sk != null)
            {
                _sk.gameObject.SetActive(true);
            }
        }

        protected override void Hide()
        {
            base.Hide();
            if (_sk != null)
            {
                _sk.gameObject.SetActive(false);
            }
        }

        private void DetermineCurrentIdleAnim()
        {
            switch (_currentLevel)
            {
                case 3:
                    _currentIdleName = _idle1Name;
                    break;
                case 2:
                    _currentIdleName = _idle2Name;
                    break;
                case 1:
                    _currentIdleName = _idle3Name;
                    break;
                default:
                    _currentIdleName = _idle3Name;
                    break;
            }
        }

        protected override void OnAppearAfterDelayBeforeBaseCall()
        {
            DetermineCurrentIdleAnim();
            if (_sk != null)
            {
                _sk.Skeleton.SetToSetupPose();
                _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true, delay: 0);
                _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true,
                    delay: UnityEngine.Random.Range(0f, 3f));
            }
        }

        public void PlayHit()
        {
            if (_hideUntilAppear && !IsPlayedAppear)
            {
                IsPlayedAppear = true;
                Show();
            }
            
            _hitParticles.Play();
            switch (_currentLevel)
            {
                case 2:
                    AudioProxy.PlaySound(Match3SoundIds.SheepHitOne);
                    _sk.AnimationState.SetAnimation(0, _hit2Name, loop: false);
                    _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true, delay: 0);
                    break;
                case 1:
                    AudioProxy.PlaySound(Match3SoundIds.SheepHitTwo);
                    _sk.AnimationState.SetAnimation(0, _hit1Name, loop: false);
                    _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true, delay: 0);
                    break;
            }
        }

        public void PlayDestroy(Coords coords, Action onDone)
        {
            _currentOnDestroyCallback = onDone;
            if (_sortingOrderChangeOnDestroy != 0)
            {
                var canvas = GetComponent<Canvas>();
                if (canvas != null)
                {
                    canvas.sortingOrder += _sortingOrderChangeOnDestroy;
                }
            }

            bool isMoveLeft = coords.X < 4;
            var firstStepAnimationDuration = _sk.AnimationState.SetAnimation(0, _hit0Name, loop: false).AnimationEnd;
            StartCoroutine(DestroyRoutine(firstStepAnimationDuration, isMoveLeft));
            _sk.AnimationState.AddAnimation(0, isMoveLeft ? _destroyLeftName : _destroyRightName, loop: true, delay: 0);
            AudioProxy.PlaySound(Match3SoundIds.SheepDestroy);
        }

        private IEnumerator DestroyRoutine(float startDelay, bool isMoveLeft)
        {
            yield return WaitCache.Seconds(startDelay);
            Vector3 startPos = transform.localPosition;
            Vector3 currentPos = startPos;

            if (isMoveLeft)
            {
                _destroyLeftParticles.Play();
            }
            else
            {
                _destroyRightParticles.Play();
            }

            var duration = _destroySideMoveDuration;
            var timer = 0f;
            while (timer < duration)
            {
                var ratio = timer / duration;
                var posX = _destroySideMoveCurveX.Evaluate(ratio);
                currentPos.x = startPos.x + (posX * (isMoveLeft ? -1 : 1));
                transform.localPosition = currentPos;
                timer += Time.deltaTime;
                yield return null;
            }
            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }

        protected override void OnDisable()
        {
            Hide();
            ResetToDefault();
            
            if (_currentOnDestroyCallback == null)
                return;
            
            _currentOnDestroyCallback.Invoke();
            _currentOnDestroyCallback = null;
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty())
            {
                base.PlayTapFeedback(layerView);
                return;
            }

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_currentIdleName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _currentIdleName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            _sk.AnimationState.SetAnimation(0, _idle1Name, loop: true);
            SetViewLevel(3);
            Show();
            if (_previewAnimName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: true);
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }
    }
}