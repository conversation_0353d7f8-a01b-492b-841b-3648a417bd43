using BBB;
using BebopBee.Core;
using DG.Tweening;
using Spine.Unity;
using UnityEngine;

public class ShelfItemRenderer : BbbMonoBehaviour
{
    [SerializeField] private SkeletonGraphic _shelfItem;
    [SerializeField] private string _shakeAnimationName = "shake ";
    [SerializeField] private string _destroyAnimationName = "idle after hit";
    [SerializeField] private string _idleAnimationName = "idle";
    [SerializeField] private float _destroyDelay;

    private Tweener _tweener;

    public void SetupItem()
    {
        if (_shelfItem != null)
        {
            _shelfItem.gameObject.SetActive(true);
            _shelfItem.Initialize(false);
            _shelfItem.AnimationState.ClearTracks();
            _shelfItem.Skeleton.SetToSetupPose();
            _shelfItem.AnimationState.SetAnimation(0, _idleAnimationName, true);
        }
    }

    public void ShakeItem()
    {
        if(_shelfItem == null) return;
        //We have 3 different shake animations, so we pick one at random
        _shelfItem.AnimationState.SetAnimation(0, _shakeAnimationName + Random.Range(1, 4), false);
        _shelfItem.AnimationState.AddAnimation(0, _idleAnimationName, true, 0);
    }

    public void DisableShelfItem()
    {
        if(_shelfItem == null) return;
        
        _tweener?.Kill();
    
        _tweener = Rx.Invoke(_destroyDelay, _ =>
        {
            if (_shelfItem != null)
            {
                _shelfItem.gameObject.SetActive(false);
            }
            _tweener = null;
        });
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        _tweener?.Kill();
        _tweener = null;
    }
}