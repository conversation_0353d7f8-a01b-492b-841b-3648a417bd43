using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class ShelfLayerRenderer : DelayedAppearLayerRenderer
    {
        private const float HelpPanelScaleMultiplier = 1.5f;

        [SerializeField] private List<Image> _quarterImages;
        [SerializeField] private List<ShelfData> _shelfTileData;
        [SerializeField] private Sprite _edgeCornerSprite;
        [SerializeField] private Sprite _edgeRecessSprite;
        [SerializeField] private Sprite _edgeFaceSprite;
        [SerializeField] private ShelfItemRenderer _shelfItem;
        [SerializeField] private float _shelfItemDestroyTime;
        [SerializeField] private float _shelfDestroyTime;

        public float ShelfItemDestroyTime => _shelfItemDestroyTime;
        public float ShelfDestroyTime => _shelfDestroyTime;

        private static readonly Coords[] NeighborCoords = new Coords[8];

        private static readonly ShelfPosition[] PositionMap =
        {
            ShelfPosition.BottomLeft, ShelfPosition.BottomMiddle, ShelfPosition.BottomRight, // y=0
            ShelfPosition.MiddleLeft, ShelfPosition.MiddleMiddle, ShelfPosition.MiddleRight, // y=1
            ShelfPosition.TopLeft, ShelfPosition.TopMiddle, ShelfPosition.TopRight // y=2
        };

        public override void PlayPreview()
        {
            base.PlayPreview();
            Content.localScale = Vector3.one * HelpPanelScaleMultiplier;
            for (var i = 0; i < 4; i++)
            {
                var quarterState = GenerateQuarterState(
                    false,
                    false,
                    false, i * 90f);

                SetImage(i, quarterState);
            }

            foreach (var shelfData in _shelfTileData)
            {
                shelfData.ShelfObject.SetActive(shelfData.ShelfPosition == ShelfPosition.SingleSingle);
            }

            SetupShelfItem();
        }

        public override void Init()
        {
            base.Init();
            Content.localScale = Vector3.one;
            SetupShelfItem();
        }

        private void SetupShelfItem()
        {
            _shelfItem.SetupItem();
        }

        public void ShakeItem()
        {
            _shelfItem.ShakeItem();
        }

        public void DestroyShelfItem()
        {
            _shelfItem.DisableShelfItem();
        }

        public void UpdateShelf(IGridController gridController, Coords coords, int shelfGroupIdentifier)
        {
            var neighbourExistenceFlags = new bool[8];
            var index = 0;
            CardinalDirectionsHelper.PopulateNeighborCoords(NeighborCoords, coords.X, coords.Y);
            foreach (var coordAround in NeighborCoords)
            {
                neighbourExistenceFlags[index] = gridController.IsPartOfShelfGroup(coordAround, shelfGroupIdentifier);
                index++;
            }

            // For Shelf each cell is cut into '4' quarters, each representing a 90-degree slice.
            // For each quarter, we need to decide what kind of edge or corner it should display.
            // To do this, we look at three neighbors: one directly next to the quarter, one at the corner, and one on the other side.
            // Since there are '8' possible neighbor positions around a cell, we use % 8 to loop around the array as we check each quarter.
            // This way, each slice of the cell knows exactly which neighbors to consult to decide if it should show a face, corner, or recess.
            // After calculating the state for each quarter, we set the appropriate sprite and rotation for that quarter completing the shelf.

            for (var i = 0; i < 4; i++)
            {
                var firstIndex = (i * 2) % 8;
                var secondIndex = (i * 2 + 1) % 8;
                var thirdIndex = (i * 2 + 2) % 8;

                var quarterState = GenerateQuarterState(
                    neighbourExistenceFlags[firstIndex],
                    neighbourExistenceFlags[secondIndex],
                    neighbourExistenceFlags[thirdIndex], i * 90f);

                SetImage(i, quarterState);
            }

            // Update shelf tiles based on position
            UpdateShelfTiles(gridController, coords, shelfGroupIdentifier, neighbourExistenceFlags);
        }

        private static EdgeQuarterState GenerateQuarterState(bool firstNeighbourExists, bool cornerNeighbourExists,
            bool secondNeighbourExists, float baseAngle)
        {
            if (firstNeighbourExists && secondNeighbourExists && cornerNeighbourExists)
            {
                return null;
            }

            if (firstNeighbourExists)
            {
                return secondNeighbourExists
                    ? new EdgeQuarterState(EdgeQuarterType.Corner, 270f - baseAngle)
                    : new EdgeQuarterState(EdgeQuarterType.Face, -90f - baseAngle);
            }

            if (cornerNeighbourExists)
            {
                return secondNeighbourExists
                    ? new EdgeQuarterState(EdgeQuarterType.Face, 0 - baseAngle)
                    : new EdgeQuarterState(EdgeQuarterType.Recess, 270 - baseAngle);
            }

            return secondNeighbourExists
                ? new EdgeQuarterState(EdgeQuarterType.Face, 360 - baseAngle)
                : new EdgeQuarterState(EdgeQuarterType.Recess, 270 - baseAngle);
        }

        private Sprite GetEdgeSprite(EdgeQuarterType type)
        {
            return type switch
            {
                EdgeQuarterType.Corner => _edgeCornerSprite,
                EdgeQuarterType.Face => _edgeFaceSprite,
                EdgeQuarterType.Recess => _edgeRecessSprite,
                _ => null
            };
        }

        private void SetImage(int i, EdgeQuarterState state)
        {
            if (state != null)
            {
                var sprite = GetEdgeSprite(state.QuarterType);
                _quarterImages[i].sprite = sprite;
                _quarterImages[i].transform.localRotation = Quaternion.Euler(0f, 0f, state.Angle);
                _quarterImages[i].enabled = true;
            }
            else
            {
                _quarterImages[i].enabled = false;
            }
        }

        private void UpdateShelfTiles(IGridController gridController, Coords center, int shelfGroupIdentifier,
            bool[] neighbourExistenceFlags)
        {
            var thisShelfCells = new HashSet<Coords>();
            CollectShelfGroupCoords(gridController, center, shelfGroupIdentifier, thisShelfCells);

            var shelfPosition = DetermineShelfPosition(thisShelfCells, center, neighbourExistenceFlags);

            foreach (var shelfData in _shelfTileData)
            {
                shelfData.ShelfObject.SetActive(shelfData.ShelfPosition == shelfPosition);
            }
        }

        private static (bool isLeft, bool isMiddleX, bool isRight, bool isBottom, bool isMiddleY, bool isTop)
            GetRelativePosition(HashSet<Coords> cells, Coords center)
        {
            int minX = int.MaxValue, maxX = int.MinValue;
            int minY = int.MaxValue, maxY = int.MinValue;

            foreach (var cell in cells)
            {
                if (cell.X < minX) minX = cell.X;
                if (cell.X > maxX) maxX = cell.X;
                if (cell.Y < minY) minY = cell.Y;
                if (cell.Y > maxY) maxY = cell.Y;
            }

            return (
                center.X == minX,
                center.X > minX && center.X < maxX,
                center.X == maxX,
                center.Y == minY,
                center.Y > minY && center.Y < maxY,
                center.Y == maxY
            );
        }

        private static ShelfPosition DetermineShelfPosition(HashSet<Coords> thisShelfCells, Coords center,
            bool[] neighbourExistenceFlags)
        {
            var (hasBottom, hasLeft, hasTop, hasRight) =
                CardinalDirectionsHelper.GetCardinalFlags(neighbourExistenceFlags);

            // Handle single-cell cases first
            if (hasBottom && !hasLeft && !hasTop && !hasRight)
                return ShelfPosition.SingleTop;
            if (!hasBottom && !hasLeft && hasTop && !hasRight)
                return ShelfPosition.SingleBottom;
            if (!hasBottom && !hasLeft && !hasTop && !hasRight)
                return ShelfPosition.SingleSingle;
            if (hasBottom && !hasLeft && hasTop && !hasRight)
                return ShelfPosition.SingleTop;

            var (isLeft, isMiddleX, isRight, isBottom, isMiddleY, isTop) =
                GetRelativePosition(thisShelfCells, center);
            
            return (isLeft, isMiddleX, isRight, isBottom, isMiddleY, isTop) switch
            {
                (true, false, false, true, false, false) => ShelfPosition.BottomLeft,
                (false, false, true, true, false, false) => ShelfPosition.BottomRight,
                (true, false, false, false, false, true) => ShelfPosition.TopLeft,
                (false, false, true, false, false, true) => ShelfPosition.TopRight,
                
                (false, true, false, true, false, false) when hasLeft && hasRight => ShelfPosition.BottomMiddle,
                (false, true, false, true, false, false) when hasLeft => ShelfPosition.BottomRight,
                (false, true, false, true, false, false) when hasRight => ShelfPosition.BottomLeft,

                (false, true, false, false, false, true) when hasLeft && hasRight => ShelfPosition.TopMiddle,
                (false, true, false, false, false, true) when hasLeft => ShelfPosition.TopRight,
                (false, true, false, false, false, true) when hasRight => ShelfPosition.TopLeft,

                (true, false, false, false, true, false) => ShelfPosition.MiddleLeft,
                (false, false, true, false, true, false) => ShelfPosition.MiddleRight,

                (false, true, false, false, true, false) when hasLeft && hasRight => ShelfPosition.MiddleMiddle,
                (false, true, false, false, true, false) when hasLeft => ShelfPosition.MiddleRight,
                (false, true, false, false, true, false) when hasRight => ShelfPosition.MiddleLeft,

                // Fallback to lookup table for standard positions
                _ => GetPositionFromLookup(isLeft, isMiddleX, isBottom, isMiddleY)
            };
        }

        private static ShelfPosition GetPositionFromLookup(bool isLeft, bool isMiddleX, bool isBottom, bool isMiddleY)
        {
            var xIdx = isLeft ? 0 : isMiddleX ? 1 : 2;
            var yIdx = isBottom ? 0 : isMiddleY ? 1 : 2;
            var index = yIdx * 3 + xIdx;
            return PositionMap[index];
        }

        private static void CollectShelfGroupCoords(IGridController gridController, Coords start,
            int shelfGroupIdentifier, HashSet<Coords> result)
        {
            var stack = new Stack<Coords>();
            stack.Push(start);

            while (stack.Count > 0)
            {
                var current = stack.Pop();
                if (result.Contains(current) || !gridController.IsPartOfShelfGroup(current, shelfGroupIdentifier))
                {
                    continue;
                }

                result.Add(current);

                // Use a local array to avoid static/shared state issues
                var neighbors = new Coords[8];
                CardinalDirectionsHelper.PopulateNeighborCoords(neighbors, current.X, current.Y);

                foreach (var neighbor in neighbors)
                {
                    if (!result.Contains(neighbor))
                    {
                        stack.Push(neighbor);
                    }
                }
            }
        }
    }

    public enum ShelfPosition
    {
        BottomLeft,
        BottomMiddle,
        BottomRight,
        MiddleLeft,
        MiddleMiddle,
        MiddleRight,
        TopLeft,
        TopMiddle,
        TopRight,
        SingleTop,
        SingleBottom,
        SingleSingle
    }

    [Serializable]
    public class ShelfData
    {
        public ShelfPosition ShelfPosition;
        public GameObject ShelfObject;
    }
}