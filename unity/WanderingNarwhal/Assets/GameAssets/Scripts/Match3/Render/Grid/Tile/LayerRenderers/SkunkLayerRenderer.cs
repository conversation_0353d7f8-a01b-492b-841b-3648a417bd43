using BBB.Audio;
using BebopBee.Core.Audio;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SkunkLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _idleName = "Idle";

        [SerializeField]
        private string _attackName = "Fart";

        [SerializeField]
        private string _afterAttackName = "AfterFart";

        [SerializeField]
        private string _appearName = "PopOut";

        [SerializeField]
        private ParticleSystem _attackPs;

        [SerializeField]
        private string _tapFeedbackName = "AfterFart";

        [SerializeField]
        private string _previewStateName = "HitEffect";

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";

        public void Setup()
        {
            if (_sk == null) return;
           
            _sk.Initialize(true);
            _sk.AnimationState.ClearTracks();
            _sk.Skeleton.SetToSetupPose();
        }
        
        public override void OnRefresh()
        {
            base.OnRefresh();
            if (!_hideUntilAppear || IsPlayedAppear)
            {
                _sk.AnimationState.SetAnimation(0, _idleName, loop: true);

                // Add randomization of idle loop.
                _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: UnityEngine.Random.Range(0f, 3f));
            }
        }
        
        protected override void OnAppearAfterDelayAfterBaseCall()
        {
            if (_sk != null)
            {
                _sk.AnimationState.SetAnimation(0, _appearName, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: 0);
            }
        }

        public void PlayHit()
        {
            if (_hideUntilAppear && !IsPlayedAppear)
            {
                IsPlayedAppear = true;
                Show();
            }

            AudioProxy.PlaySound(Match3SoundIds.SkunkHit);
            _sk.AnimationState.SetAnimation(0, _attackName, loop: false);
            _sk.AnimationState.AddAnimation(0, _afterAttackName, loop: false, delay: 0);
            _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: 0);

            if (_attackPs != null)
            {
                _attackPs.Play();
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            if (_previewStateName == "HitEffect")
            {
                PlayHit();
            }
            else
            {
                if (_previewStateName.IsNullOrEmpty()) return;
                _sk.AnimationState.SetAnimation(0, _previewStateName, loop: true);
            }
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        protected override void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        public override void Show()
        {
            _sk.gameObject.SetActive(true);
        }

        protected override void OnDisable()
        {
            ResetToDefault();
        }
    }
}