using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Logic.Tiles;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SlotMachineLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField] private RectTransform _content;
        [SerializeField] private float _destroyDuration = 2.0f;
        [SerializeField] private float _fxDestroyDuration = 2.0f;
        [SerializeField] private Vector2 _tileHelpMinOffset;
        [SerializeField] private Vector2 _tileHelpMaxOffset; 
        [SerializeField] private Animator _slotMachineAnimator;
        [SerializeField] private List<SlotMachineItemRenderer> _slotMachineItemRenderers;
        [SerializeField] private SpriteMask _slotMachineItemMask;
        [SerializeField] private Canvas _slotMachineCanvas;
        [SerializeField] private Vector3 _defaultScale;
        [SerializeField] private Vector3 _previewScale;
        
        [SerializeField] private Sprite _bombSprite;
        [SerializeField] private Sprite _propellerSprite;
        [SerializeField] private Sprite _rowBreakerSprite;
        [SerializeField] private Sprite _columnBreakerSprite;
        [SerializeField] private Sprite _discoBallSprite;
        [SerializeField] private Sprite _superDiscoBallSprite;

        private M3Settings _m3Settings;
        private readonly Dictionary<int, Sprite> _spritesForReels = new();

        public TileAsset FinalAssetType { get; private set; }

        private static readonly int SlotMachineShow = Animator.StringToHash( "Show");
        private const string SlotMachineHideLayer = "HideLayer_";
        private const int FrontLayerOffset = 10;

        public float FxDestroyDuration => _fxDestroyDuration;

        public void InitialSetup(M3Settings m3Settings)
        {
            _m3Settings = m3Settings;
            _m3Settings.SuperDiscoBallChanged -= UpdateSprites;
            _m3Settings.SuperDiscoBallChanged += UpdateSprites;
            UpdateSprites();
            
            FinalAssetType = TileAsset.Undefined;
            foreach (var itemRenderer in _slotMachineItemRenderers)
            {
                itemRenderer.ResetAllStates(_spritesForReels);
            }

            if (!_slotMachineItemMask.enabled)
            {
                _slotMachineItemMask.enabled = true;
            }
            _slotMachineItemMask.frontSortingOrder = _slotMachineCanvas.sortingOrder + FrontLayerOffset;
            _content.localScale = _defaultScale;
            if (_slotMachineAnimator != null)
            {
                _slotMachineAnimator.ResetAllParameters();
                _slotMachineAnimator.PlayMainOrRebind();
                _slotMachineAnimator.SetTrigger(SlotMachineShow);
            }
        }

        private void UpdateSprites()
        {
            _spritesForReels[(int)TileAsset.Bomb] = _bombSprite;
            _spritesForReels[(int)TileAsset.Propeller] = _propellerSprite;
            _spritesForReels[(int)TileAsset.RowBreaker] = _rowBreakerSprite;
            _spritesForReels[(int)TileAsset.ColumnBreaker] = _columnBreakerSprite;
            _spritesForReels[(int)TileAsset.ColorBomb] = _m3Settings.IsSuperDiscoBallActive ? _superDiscoBallSprite : _discoBallSprite;
        }

        public void SetupOutcome(TileAsset rewardAsset)
        {
            FinalAssetType = rewardAsset;
            foreach (var itemRenderer in _slotMachineItemRenderers)
            {
                itemRenderer.SetFinalState(rewardAsset);
            }
        }
        
        public override void PlayPreview()
        {
            UpdateSize(1);
            _slotMachineAnimator?.SetTrigger(SlotMachineShow);
            base.PlayPreview();
        }

        public void PlayHit(int hp)
        {
            if (hp <= 0) return;
            var hideLayer = SlotMachineHideLayer + hp;
            _slotMachineAnimator?.SetTrigger(hideLayer);
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_destroyDuration);
            foreach (var itemRenderer in _slotMachineItemRenderers)
            {
                itemRenderer.ResetAllStates(_spritesForReels);
            }
            onDone.SafeInvoke();
            _m3Settings.SuperDiscoBallChanged -= UpdateSprites;
        }

        public override void Show()
        {
            base.Show();
            _slotMachineAnimator?.gameObject.SetActive(true);
        }
        
        protected override void Hide()
        {
            base.Hide(); 
            if (_slotMachineAnimator == null) return;
            _slotMachineAnimator.ResetAllParameters();
            _slotMachineAnimator.gameObject.SetActive(false);
        }

        private void UpdateSize(int size)
        {
            if (size == 1)
            {
                // This is used for the preview in tile help panel
                _content.anchorMin = _tileHelpMinOffset;
                _content.anchorMax = _tileHelpMaxOffset;
                _content.localScale = _previewScale;
            }
            
            _content.anchoredPosition = Vector2.zero;
            FitBaseRectToExactCellSize();
            _content.sizeDelta = Vector2.zero;
        }
    }
}