using System;
using System.Collections;
using System.Collections.Generic;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SodaLayerRenderer : DelayedAppearLayerRenderer
    {
        private const string BottleObjectName = "SodaBottle";
        
        [SerializeField] private Animator _animator;
        [SerializeField] private Transform _bottleContainer;
        [SerializeField] private float _destroyDuration = 1.0f;
        [SerializeField] private Vector3 _destroyParticlesOffset = new Vector3( 20f, 45f, 0f);
        [SerializeField]private TileKinds[] _helpPanelColors = new TileKinds[4];
        [SerializeField] private float _destroyTileFXDuration = 1.0f;
        [SerializeField] private float _destroyBottleFXDuration = 1.0f;
        [SerializeField] private float _tileHelpMinOffset = 0.35f;
        [SerializeField] private float _tileHelpMaxOffset = 1.3f;
        
        private List<SodaLayerSubItemRenderer> _bottlesSubRenderer;
        private static readonly int ShakePins = Animator.StringToHash("ShakeSoda");
        private static readonly int Ordinal = Animator.StringToHash("Ordinal");
        private int _lastUsedBottleIndex = 0;
        private int _baseCanvasSortOrder = 0;
        private SodaLayerSubItemRenderer _lastUsedBottle;
        public float DestroyTileFXDuration => _destroyTileFXDuration;
        public float DestroyBottleFXDuration => _destroyBottleFXDuration;
        public Color BottleColor => _lastUsedBottle.particleSystemColor;
        public Vector3 ParticleOffsetCoords { get; private set; }

        public void InitialSetup()
        {
            _baseCanvasSortOrder = GetComponent<Canvas>().sortingOrder;
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            rect.pivot = new Vector2(0, 0);
            rect.anchorMin = new Vector2();
            rect.anchorMax = new Vector2(2.15f, 2.15f);
            rect.offsetMin = new Vector2();
            rect.offsetMax = new Vector2();

            _bottlesSubRenderer = new List<SodaLayerSubItemRenderer>();
            _lastUsedBottleIndex = 0;
            for (var i = 0; i < 4; ++i)
            {
                var sodaBottle = _bottleContainer.Find($"{BottleObjectName}{i}");
                sodaBottle.gameObject.SetActive(true);
                var bottleSubItemRenderer = sodaBottle.GetComponent<SodaLayerSubItemRenderer>();
                bottleSubItemRenderer.itemIndex = i;
                _bottlesSubRenderer.Add(bottleSubItemRenderer);
                
                var sodaBottleAnim = sodaBottle.GetComponent<Animator>();
                sodaBottleAnim.ResetAllParameters();
                sodaBottleAnim.Rebind();
                sodaBottleAnim.Update(0f);
            }
        }

        public void Setup(TileKinds[] tileKinds)
        {
            if(tileKinds.Length < _bottlesSubRenderer.Count)
                return;
            
            _helpPanelColors = tileKinds;
            
            var index = 0;
            foreach (var bottle in _bottlesSubRenderer)
            {
                bottle.Setup(tileKinds[index],_baseCanvasSortOrder);     
                index++;                                        
            }
        }
        
        public void PlayHit(int index)
        {
            if (_bottlesSubRenderer.Count <= 0) return;
            _lastUsedBottleIndex++;
            _animator.SetTrigger(ShakePins);
            _animator.SetInteger(Ordinal, _lastUsedBottleIndex);
            for (var i = 0; i < _bottlesSubRenderer.Count; i++)
            {
                var bottle = _bottlesSubRenderer[i];
                //Play Jump Animation for all others
                bottle.PlayJumpAnimation();
                if (bottle.itemIndex != index) continue;
                
                _lastUsedBottle = bottle;
                var bottlePos = bottle.RectTransform().position;
                ParticleOffsetCoords = bottlePos + _destroyParticlesOffset;
                
                bottle.PlayDestroyAnimation();
                _bottlesSubRenderer.RemoveAt(i);
                bottle.gameObject.SetActive(false);
            }
        }

        public void PlayDestroy(Action onDone)
        {
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_destroyDuration);
            onDone.SafeInvoke();
        }
        
        public override void PlayPreview()
        {
            Setup(_helpPanelColors);
            Show();
            UpdateSize(1);
            base.PlayPreview();
        }

        public void UpdateSize(int size)
        {
            var rect = transform.GetChild(0).GetComponent<RectTransform>();
            if (size == 1)
            {
                // This is used for the preview in tile help panel
                rect.anchorMin = -Vector2.one * _tileHelpMinOffset;
                rect.anchorMax = Vector2.one * _tileHelpMaxOffset;
            }
            else
            {
                rect.anchorMin = Vector2.zero;
                rect.anchorMax = Vector2.one * 2;
            }
            rect.anchoredPosition = Vector2.zero;
            FitBaseRectToExactCellSize();
        }


        public override void Show()
        {
            base.Show();
            _animator.StopPlayback();
            _animator.ResetAllParameters();
        }
    }
}
