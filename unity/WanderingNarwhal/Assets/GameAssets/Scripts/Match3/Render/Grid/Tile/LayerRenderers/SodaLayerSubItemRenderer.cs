using System;
using System.Collections.Generic;
using BBB;
using Spine.Unity;
using UnityEngine;

public class SodaLayerSubItemRenderer : BbbMonoBehaviour
{
    [HideInInspector]public int itemIndex;
    [HideInInspector]public Color particleSystemColor;
    public List<TileColor> tileColors;
    public SkeletonGraphic SkeletonGraphic;
    public string JumpAnimation;
    public string DestroyAnimation;
    [SerializeField]private Canvas bottleCanvas;
    [SerializeField]private int bottleIndex;
    
    public void Setup(TileKinds tileColor, int sortOrder)
    {
        SkeletonGraphic.Skeleton.SetSkin(tileColor.GetSkinColor());
        particleSystemColor = GetParticleSystemColor(tileColor);
        bottleCanvas.sortingOrder = sortOrder + bottleIndex;
        PlayJumpAnimation();
    }

    public void PlayJumpAnimation()
    {
        SkeletonGraphic.AnimationState.SetAnimation(0, JumpAnimation, false);
    }

    public void PlayDestroyAnimation()
    {
        SkeletonGraphic.AnimationState.SetAnimation(0, DestroyAnimation, false);
    }
    private Color GetParticleSystemColor(TileKinds col)
    {
        return tileColors.Find(a => a.tileKind == col).color;
    }
    

    [Serializable]
    public class TileColor
    {
        public TileKinds tileKind;
        public Color color;
    }
}
