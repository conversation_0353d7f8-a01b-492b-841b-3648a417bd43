using System;
using System.Collections.Generic;
using Spine;
using Spine.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class SquidLayerSubItemRenderer : BbbMonoBehaviour, IMultiSizeTileItemElement
    {
        [Serializable]
        public class ColoredSquidSkinPair
        {
            public TileKinds color;
            public string skin;
        }

        public int ItemIndex { get; private set; }

        [SerializeField] private List<ColoredSquidSkinPair> _skinsColors;
        [SerializeField] private Image _debugItem;
        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private string _idleAnim1 = "idle_1_Sad";
        [SerializeField] private string _idleAnim2 = "idle_2_VerySad";
        [SerializeField] private string _matchAnim = "Match";
        [SerializeField] private string _appearAnim = "appear";
        [SerializeField] private string _blinkAnim = "blink";
        [SerializeField] private string _previewAnim = "preview";
        
        private TrackEntry _idleTrackEntry;

        private void SetSkeletonAlpha(float alpha)
        {
            if (_sk == null) return;

            _sk.enabled = alpha > 0;
            _sk.DoAlpha(alpha, 0f);
        }

        private void SetDebugItemAlpha(float alpha)
        {
            if (_debugItem == null) return;

            _debugItem.enabled = alpha > 0;
            _debugItem.DoAlpha(alpha, 0f);
        }

        public void Setup(TileKinds col)
        {
            InitializeSkeleton();

            var skinName = GetSkinForColor(col);
            SetSkeletonAlpha(0f);
            SetDebugItemAlpha(0f);
            if (HasValidSkeleton(skinName))
            {
                _sk.Skeleton.SetSkin(skinName);
                ResetSpine();
            }
            else
            {
                SetupDebugVisual(col);
            }

#if UNITY_EDITOR
            if (_sk != null) _sk.raycastTarget = true;
            if (_debugItem != null) _debugItem.enabled = true;
            ItemIndex = 0;
#endif
        }

        private bool HasValidSkeleton(string skinName)
        {
            return !string.IsNullOrEmpty(skinName) &&
                   _sk != null &&
                   _sk.SkeletonData?.FindSkin(skinName) != null;
        }

        private void SetupDebugVisual(TileKinds col)
        {
            if (_debugItem != null)
            {
                _debugItem.color = GetDebugTileKindColor(col);
            }
        }

        private void InitializeSkeleton()
        {
            if (_sk == null) return;

            if (_sk.Skeleton == null)
            {
                _sk.Initialize(true);
            }
        }

        public void PlayAppear()
        {
            SetSkeletonAlpha(1f);
            _sk.AnimationState.SetAnimation(0, _appearAnim, false);
            PlayIdle(asContinue: true);
        }

        private void PlayIdle(bool asContinue = false)
        {
            var idleCounts = UnityEngine.Random.Range(2, 5);

            if (asContinue)
            {
                _sk.AnimationState.AddAnimation(0, _idleAnim1, false, 0);
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _idleAnim1, false);
            }

            for (var i = 0; i < idleCounts - 1; i++)
            {
                _sk.AnimationState.AddAnimation(0, _idleAnim1, false, 0);
            }

            Unsubscribe();
            _idleTrackEntry = _sk.AnimationState.AddAnimation(0, _idleAnim2, false, 0);
            _idleTrackEntry.Complete += OnAnimFin;
        }

        public void PlayPreview()
        {
            SetSkeletonAlpha(1f);
            _sk.AnimationState.SetAnimation(0, _previewAnim, true);
        }

        public void PlayHitReaction()
        {
            _sk.AnimationState.SetAnimation(0, _matchAnim, false);
            Unsubscribe();
            _idleTrackEntry = _sk.AnimationState.AddAnimation(0, _idleAnim2, false, 0);
            _idleTrackEntry.Complete += OnAnimFin;
        }

        private void OnAnimFin(TrackEntry trackEntry)
        {
            Unsubscribe();
            if (trackEntry.TrackIndex == 1)
            {
                HandleBlinkAnimation(trackEntry);
                return;
            }

            HandleMainAnimation(trackEntry);
        }

        private void Unsubscribe()
        {
            if (_idleTrackEntry != null)
            {
                _idleTrackEntry.Complete -= OnAnimFin;
                _idleTrackEntry = null;
            }
        }

        private void HandleBlinkAnimation(TrackEntry trackEntry)
        {
            if (trackEntry.Animation.Name == _blinkAnim)
            {
                var delay = UnityEngine.Random.Range(2f, 15f);
                _sk.AnimationState.AddAnimation(1, _blinkAnim, false, delay);
            }
        }

        private void HandleMainAnimation(TrackEntry trackEntry)
        {
            if (trackEntry.Animation.Name == _idleAnim2)
            {
                PlayIdle();
            }
        }

        private string GetSkinForColor(TileKinds col)
        {
            foreach (var item in _skinsColors)
            {
                if (item.color == col)
                    return item.skin;
            }

            return null;
        }

        public void ResetToDefault()
        {
            SetSkeletonAlpha(0f);
            SetDebugItemAlpha(0f);
            ResetSpine();
            Unsubscribe();
        }

        private void ResetSpine()
        {
            if (_sk == null) return;
            _sk.Skeleton?.SetToSetupPose();
            _sk.AnimationState?.ClearTracks();
        }

        private static Color GetDebugTileKindColor(TileKinds col)
        {
            return col switch
            {
                TileKinds.Error => new Color(0.1f, 0.1f, 0.1f, 0.8f),
                TileKinds.Undefined => new Color(0.5f, 0.5f, 0.5f, 0.8f),
                TileKinds.None => new Color(0.2f, 0.2f, 0.2f, 0.9f),
                TileKinds.Green => Color.green,
                TileKinds.Yellow => Color.yellow,
                TileKinds.Blue => Color.blue,
                TileKinds.Red => Color.red,
                TileKinds.Purple => Color.magenta,
                TileKinds.Orange => new Color(0.9f, 0.6f, 0.2f),
                TileKinds.White => Color.white,
                _ => Color.black
            };
        }
    }
}