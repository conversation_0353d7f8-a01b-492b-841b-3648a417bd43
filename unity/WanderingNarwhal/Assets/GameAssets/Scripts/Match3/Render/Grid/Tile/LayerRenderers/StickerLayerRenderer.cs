using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB
{
    public class StickerLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private GameObject[] _images = new GameObject[0];

        /// <summary>
        /// Used instead of main _images if current Skin is Slate.
        /// </summary>
        /// <remarks>
        /// Crate and Slate images differs by transform size and position.
        /// </remarks>
        [SerializeField]
        private GameObject[] _slateImages = new GameObject[0];

        [SerializeField]
        private ParticleSystem _particleSystem;

        [SerializeField]
        private ParticleSystem _particleSystemSlate;

        [SerializeField]
        private string _takeHitSoundUid = Match3SoundIds.StickerDestroy;

        //[SerializeField]
        //private bool _playTapFeedback = true;

        private StickerSkin _currentSkin;
        private int _currentCount;

        public void ResetView()
        {
            foreach (var image in _images)
                image.SetActive(false);
        }

        public void UpdateView(int count)
        {
            _currentCount = count;
            if (_currentSkin == StickerSkin.Crate)
            {
                for (int i = 0; i < _images.Length; i++)
                {
                    _images[i].SetActive(i < count);
                }

                foreach (var img in _slateImages)
                {
                    img.SetActive(false);
                }
            }
            else
            {
                for (int i = 0; i < _slateImages.Length; i++)
                {
                    _slateImages[i].SetActive(i == count - 1);
                }

                foreach (var img in _images)
                {
                    img.SetActive(false);
                }
            }
        }

        public void Setup(StickerRenderVariant data)
        {
            _currentSkin = data.Skin;
            if (_currentSkin == StickerSkin.Crate)
            {
                for (int i = 0; i < _images.Length; i++)
                {
                    _images[i].GetComponent<SpriteRenderer>().sprite = data.GetTileSpriteAtIndex(i);
                }
            }
            else
            {
                for (int i = 0; i < _slateImages.Length; i++)
                {
                    _slateImages[i].GetComponent<SpriteRenderer>().sprite = data.GetTileSpriteAtIndex(i);
                }
            }

            UpdateView(_currentCount);
        }

        public void PlayFx()
        {
            AudioProxy.PlaySound(_takeHitSoundUid);
            // if (_currentSkin == StickerSkin.Slate)
            // {
            //     _particleSystemSlate.Play(false);
            // }
            // else
            // {
            //     _particleSystem.Play(false);
            // }
        }
    }
}
