using BBB.Core;
using BebopBee.Core;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class TukTukLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField] private RectTransform _content;
        [SerializeField] private ActivePoolItem _tuktukActivePoolItem;
        [SerializeField] private RectTransform _tileRectTransform;
        [SerializeField] private Vector2 _helpPanelOffset = new(-16f, 0f);
        [SerializeField] private TextMeshProUGUI _numberText;
        [SerializeField] private Image _tuktukImage;
        [SerializeField] private Sprite _tuktukHelpPanelImage;
        [SerializeField] private SkeletonGraphic _tukTukSkeletonGraphic;
        [SerializeField] private ParticleSystem _hitParticleSystem;
        [SerializeField] private ParticleSystem _smokeParticleSystem;
        [SerializeField] private float _destroyNumberDelay = 0.5f;
        
        private const string ShakeAnimation = "shake";
        private const string IdleAnimation = "idle";
        private const string DestroyAnimation = "exit";
       
        private int _currentCount = -1;

        public void InitialSetup()
        {
            if (_tuktukActivePoolItem != null)
            {
                _tuktukActivePoolItem.OnSpawn();
            }
            
            _tukTukSkeletonGraphic.AnimationState.SetAnimation(0, IdleAnimation, true);
        }

        public void PlayDestroy()
        {
            if (_tuktukActivePoolItem != null)
            {
                _tuktukActivePoolItem.OnRelease();
            }
        }

        public override void PlayPreview()
        {
            UpdateSize(3, 0, 5, true);
            _tukTukSkeletonGraphic.AnimationState.SetAnimation(0, IdleAnimation, true);
            base.PlayPreview();
        }
        
        public void UpdateSize(int color, int orientation, int count, bool showPreview = false)
        {
            if (showPreview)
            {
                _tileRectTransform.anchoredPosition = _helpPanelOffset;
                _tuktukImage.sprite = _tuktukHelpPanelImage;
                _numberText.text = string.Empty;
                _content.rotation = Quaternion.Euler(0, 0, 0);
            }
            else
            {
                _tileRectTransform.anchoredPosition = Vector2.zero;

                if (_currentCount != count)
                {
                    _currentCount = count;
                    _numberText.text = _currentCount.ToString();
                    
                    _tukTukSkeletonGraphic.AnimationState.SetAnimation(0, _currentCount == 0 ? DestroyAnimation : ShakeAnimation, false);
                    
                    if (_currentCount != 0)
                    {
                        _tukTukSkeletonGraphic.AnimationState.AddAnimation(0, IdleAnimation, true, 0);
                    }
                   
                    _hitParticleSystem.Play(true);
                    _smokeParticleSystem.Play(true);
                }
                else
                {
                    _tukTukSkeletonGraphic.AnimationState.SetAnimation(0, IdleAnimation, true);
                }

                if (_currentCount == 0)
                {
                    Rx.Invoke(_destroyNumberDelay, _ =>
                    {
                        if (_numberText != null)
                        {
                            _numberText.text = string.Empty;
                        }
                    });
                }
                
                _tukTukSkeletonGraphic.Skeleton.SetSkin(((TileKinds)color).GetSkinColor());
                _content.rotation = Quaternion.Euler(0, 0, orientation);
            }
        }
    }
}
