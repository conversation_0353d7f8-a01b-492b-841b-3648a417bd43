using System;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class VaseLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _idleAnimLayer3 = "Idle";

        [SerializeField]
        private string _idleAnimLayer2 = "Match";

        [SerializeField]
        private string _idleAnimLayer1 = "Match2";

        [SerializeField]
        private string _destroyAnim = "Destroy";

        [SerializeField]
        private bool _idleLooping = false;

        [SerializeField]
        private bool _allowShakeOnHit = false;

        public bool allowShakeOnHit => _allowShakeOnHit;

        private Action _currentDestroyCallback;

        public void ResetView(int layer = 0)
        {
            var idleName = layer >= 3 ? _idleAnimLayer3 : (layer <= 1 ? _idleAnimLayer1 : _idleAnimLayer2);
            _sk.AnimationState.SetAnimation(0, idleName, loop: _idleLooping);
        }

        public void PlayDestroy(Action onDone)
        {
            if (string.IsNullOrEmpty(_destroyAnim) || _sk == null)
            {
                onDone?.Invoke();
                return;
            }

            _sk.AnimationState.ClearTracks();
            _sk.AnimationState.SetAnimation(0, _destroyAnim, loop: false);
            _currentDestroyCallback = onDone;
            _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            _sk.AnimationState.Complete += OnDestroyAnimationEnd;
        }

        private void OnDestroyAnimationEnd(TrackEntry entry)
        {
            if (_sk != null)
            {
                _sk.AnimationState.Complete -= OnDestroyAnimationEnd;
            }

            _currentDestroyCallback?.Invoke();
            _currentDestroyCallback = null;
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            base.PlayTapFeedback(layerView);
            if (_sk == null) return;
            var track = _sk.AnimationState.GetCurrent(0);
            if (track == null || track.Animation == null) return;
            _sk.AnimationState.SetAnimation(0, track.Animation.Name, loop: false);
        }
    }
}