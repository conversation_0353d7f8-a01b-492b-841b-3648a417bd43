using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BeeLayerView : TileLayerViewBase, IAlwaysApplicableTileView, ITileLayerViewSkin
    {
        public int SkinIndex => _renderer != null ? _renderer.CurrentSkinIndex : 0;

        private BeeLayerRenderer _renderer;

        public BeeLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<BeeLayerRenderer>();
            ProcessRendererVisibilityOnInstantiateView();
        }

        protected override void ShowRenderer()
        {
            if (_renderer != null) _renderer.OnSpawn();
        }

        protected override void HideRenderer()
        {
            if (_renderer != null) _renderer.Hide();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            ProcessRendererVisibilityOnApplyStarted(isVisible);
            base.Apply(tile, coords, container, viewsList, isVisible);
            if (_renderer != null)
            {
                _renderer.CurrentSkinIndex = tile.GetParam(TileParamEnum.Skin);
                _renderer.RefreshSkinRelatedAnim();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim, animParams);
            if (_renderer != null)
            {
                if (anim == TileLayerViewAnims.Destroy)
                {
                    // For this tile the goal fly animation acts as destruction effect.
                    _renderer.Hide();
                }
                else if (anim == TileLayerViewAnims.TapFeedback)
                {
                    _renderer.PlayTapFeedback(this);
                }
                else if (anim == TileLayerViewAnims.Preview)
                {
                    _renderer.PlayPreview();
                }
            }
        }
    }
}