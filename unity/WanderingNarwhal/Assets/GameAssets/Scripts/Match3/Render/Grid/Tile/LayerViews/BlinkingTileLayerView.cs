using System.Collections.Generic;
using BBB.Core;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BlinkingTileLayerView : TileLayerViewBase
    {
        private BlinkingTileRenderer _blinkingTile;

        public BlinkingTileLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _blinkingTile = instance.GetComponentInChildren<BlinkingTileRenderer>();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList, bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            TilesResources
                .GetAsync(tile.Kind)
                .ContinueWith(tileData =>
                {
                    _blinkingTile.SetData(tileData);
                    _blinkingTile.StartBlinking();
                }).Forget();
        }

        public override void UnApply()
        {
            _blinkingTile.StopBlinking();
            base.UnApply();
        }
    }
}