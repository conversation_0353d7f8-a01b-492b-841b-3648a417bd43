using BBB.Audio;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using BebopBee.Core.Audio;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BowlingLayerView : SheetTileLayerViewBase
    {
        private BowlingLayerRenderer _renderer;
        private int _size;
        private int _prevLevel = -1;
        private bool _curtainOpening;

        public BowlingLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<BowlingLayerRenderer>();
            _renderer.InitialSetup();
        }
        
        protected override int SelectSheetsCountValue(Tile tile)
        {
            var count = tile.GetParam(TileParamEnum.AdjacentHp);
            return count;
        }

        private void OnCurtainOpenedHandler()
        {
            _curtainOpening = false;
            _renderer.OnCurtainOpened -= OnCurtainOpenedHandler;
        }
        
        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (tile.GetParam(TileParamEnum.BowlingOpened) == 1 && _prevLevel != 0)
            {
                AudioProxy.PlaySound(Match3SoundIds.BowlingPinsCurtain);
                _curtainOpening = true;
                _renderer.OnCurtainOpened -= OnCurtainOpenedHandler;
                _renderer.OnCurtainOpened += OnCurtainOpenedHandler;
                _renderer.PlayCurtainOpening();
                _prevLevel = 0;
                return;
            }

            if (!_curtainOpening)
            {
                PlayHit();
            }
            else
            {
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(() => !_curtainOpening, PlayHit));
            }
            
            void PlayHit()
            {
                if (newLevel < 6)
                {
                    var fx = (FxType) ((int) FxType.BowlingPin6 - newLevel);
                    var pos = coords ?? Coords.Zero;
                    FxRenderer.SpawnSingleAnimatorEffect(pos, fx, _renderer.destroyPinFXDuration);
                
                    _renderer.PlayHit();
                    if (newLevel > 0)
                    {
                        AudioProxy.PlaySound(Match3SoundIds.BowlingPinsSingle);
                    }
                }
            }
        }

        public override void AfterApply()
        {
            _renderer.gameObject.SetActive(false);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.gameObject.SetActive(true);
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.BowlingPinsCompleteBox);
                    _renderer.PlayDestroy(OnDone);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.BowlingTileDestroy, _renderer.destroyTileFXDuration);
                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
