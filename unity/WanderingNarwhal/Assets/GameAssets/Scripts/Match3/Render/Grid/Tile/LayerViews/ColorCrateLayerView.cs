using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ColorCrateLayerView : SheetTileLayerViewBase
    {
        private ColorCrateLayerRenderer _renderer;

        private int _lastLevel = -1;

        private Color _expColor = Color.white;

        public ColorCrateLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<ColorCrateLayerRenderer>();
            if (_renderer == null)
            {
                UnityEngine.Debug.LogError($"Missing component {typeof(ColorCrateLayerRenderer).Name} on tile prefab: {instance.name}", instance);
            }
            else
            {
                _renderer.ResetView();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.StickerDestroy);
                    FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(
                        coords,
                        FxType.ColorCrateDestroy,
                        new FxOptionalParameters() { col = _expColor });
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);

            var data = TilesResources.GetDataForColorCrate(tile.Kind);
            _expColor = data.ExplosionColor;
            _renderer.SetData(data);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.UpdateView(newLevel);

            if (newLevel < _lastLevel)
            {
                _renderer.PlayFx();
                ShakeWithSiblings();
                if (coords.HasValue)
                {
                    switch (newLevel)
                    {
                        case 2:
                        {
                            FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(
                                coords.Value,
                                FxType.ColorCrateThirdLayerRemove,
                                new FxOptionalParameters() { col = _expColor });
                            break;
                        }
                        case 1:
                        {
                            FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(
                                coords.Value,
                                FxType.ColorCrateSecondLayerRemove,
                                new FxOptionalParameters() { col = _expColor });
                            break;
                        }
                        case 0:
                        {
                            AudioProxy.PlaySound(Match3SoundIds.StickerDestroy);
                            FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(
                                coords.Value,
                                FxType.ColorCrateDestroy,
                                new FxOptionalParameters() { col = _expColor });
                            break;
                        }
                    }
                }
            }

            _lastLevel = newLevel;
        }
    }
}