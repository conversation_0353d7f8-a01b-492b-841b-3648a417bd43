using BBB.Audio;
using UnityEngine;
using BebopBee.Core.Audio;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Renderer
{
    public class DynamiteBoxLayerView : SheetTileLayerViewBase
    {
        private DynamiteBoxLayerRenderer _renderer;
        private int _currentState;
        private int _size;
        private TileKinds[] _currentColors;
        private int _currentCount;

        public DynamiteBoxLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<DynamiteBoxLayerRenderer>();
            _renderer.InitialSetup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            RefreshTile(tile);
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            if (!Applied)
            {
                base.Apply(tile, coords, container, viewsList, isVisible);
                _currentCount = tile.GetParam(TileParamEnum.DynamiteSticksCount);
                _currentState = tile.GetParam(TileParamEnum.DynamiteBoxColors);
                _currentColors = new TileKinds[_currentCount];
                DynamiteBoxTileLayer.ExtractTileKindsFromInt(_currentState, _currentColors, _currentCount);
                _renderer.Setup(_currentColors);
            }
            else
            {
                RefreshTile(tile);
            }
        }

        private void RefreshTile(Tile tile)
        {
            if (_currentState == 0)
                return;

            var count = tile.GetParam(TileParamEnum.DynamiteSticksCount);
            var state = tile.GetParam(TileParamEnum.DynamiteBoxColors);

            if (state != _currentState)
            {
                for (var i = 0; i < DynamiteBoxLayerRenderer.NumberOfDynamiteSticks; i++)
                {
                    var currentSubColor = DynamiteBoxTileLayer.GetColorNumFromState(_currentState, i);
                    var newSubColor = DynamiteBoxTileLayer.GetColorNumFromState(state, i);
                    if (currentSubColor == newSubColor) continue;
                    _renderer.PlayHit(i, count);
                }
            }

            _currentCount = count;
            _currentState = state;
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(() =>
                    {
                        FxRenderer.ShakeBoard(coords, ShakeSettingsType.DynamiteBox);
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.DynamiteBoxTileDestroy,
                            _renderer.DestroyTileFXDuration);
                        IsPlayingLayerViewAnimation = false;
                    });
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}