using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class EggLayerView : SheetTileLayerViewBase
    {
        private EggLayerRenderer _renderer;

        private int _prevLevel = -1;
        
        public EggLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.EggLayerCount);
        }
        
        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<EggLayerRenderer>();
            _renderer.ResetView();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.ResetView(newLevel);

            if (_prevLevel != newLevel)
            {
                if (coords.HasValue && newLevel > 0)
                {
                    AudioProxy.PlaySound(Match3SoundIds.EggHit);
                    FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.EggLayerRemove);
                }

                if (newLevel >= 0)
                {
                    ShakeWithSiblings();
                }

                _prevLevel = newLevel;
            }
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            _renderer.ResetView();
            base.UnApply();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            if (_renderer != null)
            {
                switch (anim)
                {
                    case TileLayerViewAnims.TapFeedback:
                        _renderer.PlayTapFeedback(this);
                        break;
                    case TileLayerViewAnims.Preview:
                        _renderer.PlayPreview();
                        break;
                    case TileLayerViewAnims.Destroy:
                        AudioProxy.PlaySound(Match3SoundIds.EggHit);
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.EggDestroy);
                        break;
                }
            }
        }
    }
}