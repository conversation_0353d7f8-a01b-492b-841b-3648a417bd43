using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class FlowerPotLayerView : SheetTileLayerViewBase
    {
        private FlowerPotLayerRenderer _renderer;

        private int _prevLevel = -1;

        public FlowerPotLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.FlowerPotLayerCount);
        }
        
        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<FlowerPotLayerRenderer>();
            _renderer.ResetView();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (_prevLevel != newLevel)
            {
                if (newLevel < _prevLevel)
                {
                    if (coords.HasValue)
                    {
                        if (newLevel == 1)
                        {
                            _renderer.PlayReveal();
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.FlowerPotLayerRemove, _renderer._destroyDuration);
                            AudioProxy.PlaySound(Match3SoundIds.FlowerPotHit);
                        }
                    }
                }
                _prevLevel = newLevel;
            }
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            base.UnApply();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            if (_renderer != null)
            {
                switch (anim)
                {
                    case TileLayerViewAnims.Destroy:
                        _renderer.PlayDestroy();
                        AudioProxy.PlaySound(Match3SoundIds.FlowerPotDestroy);
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.FlowerPotDestroy,_renderer._destroyDuration);
                        break;
                    case TileLayerViewAnims.TapFeedback:
                        _renderer.PlayTapFeedback(this);
                        break;
                    case TileLayerViewAnims.Preview:
                        _renderer.PlayPreview();
                        break;
                   
                }
            }
        }
    }
}