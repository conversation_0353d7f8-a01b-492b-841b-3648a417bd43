using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class GameEventLabelLayerView : TileLayerViewBase
    {
        public GameEventLabelLayerView(ITileLayer layer) : base(layer) { }

        private GameEventLabelLayerRenderer _renderer;

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<GameEventLabelLayerRenderer>();
            _renderer.OnSpawn();
        }
    }
}