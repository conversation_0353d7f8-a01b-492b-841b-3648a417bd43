using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class GondolaLayerView : SheetTileLayerViewBase
    {
        private GondolaLayerRenderer _renderer;

        public GondolaLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<GondolaLayerRenderer>();
            _renderer.CellSize = cellSize;
            _renderer.InitialSetup();
        }
        
        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.GondolaReached);
        }
        
        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            var orientation = tile.GetParam(TileParamEnum.GondolaOrientation);
            _renderer.SetupGondola(orientation);
        }

        public void UpdateGondolaMovement(RectTransform rectTransform, List<Vector3> path, List<int> orientation, bool goalCollected, Tile tile, bool fastSpeed, Coords goalCoords, Action callback)
        {
            if (_renderer == null || tile == null)
                return;
            
            _renderer.MoveGondola(rectTransform, path, orientation, goalCollected, fastSpeed, FxRenderer, goalCoords, callback);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            
        }
        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(() =>
                    {
                        IsPlayingLayerViewAnimation = false;
                    });
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
