using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public interface ITileLayerView
    {
        float Alpha { get; set; }
        TileLayerState State { get; }
        bool Applied { get; }
        bool Glow { get; set; }
        int SortingLayer { set; }
        bool IsPlayingLayerViewAnimation { get; }
        void AddToSortOrder(int increment);
        void RemoveFromSortOrder(int decrement);
        void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList, bool isVisible);
        void UnApply();
        void Update(Tile tile, ITileLayer layer, int siblingIndex);
        void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None);
        void OnCoordsChanged(Vector2 coords);
        void AfterApply();
        void ShakeWithSiblings(Action onDone = null);
    }

    /// <summary>
    /// Interface for tiles that can have different skins varians.
    /// </summary>
    /// <remarks>
    /// Used to connect skin of tile with skin of spawned effects view (for example, destroy tile effect).
    /// </remarks>
    public interface ITileLayerViewSkin
    {
        int SkinIndex { get;  }
    }

    /// <summary>
    /// Interface for multi-size tiles that provide offset position for spawning effects.
    /// </summary>
    /// <remarks>
    /// Each multi-size tile is located in bottom-left corner cell of occupied rectangle,
    /// so any effect that spawns from this tile must have additional offset, otherwise all effects will also spawn in the corner.
    /// </remarks>
    public interface ITileLayerEffectSpawnOffset
    {
        Vector2 FxSpawnOffset { get; }
    }
}