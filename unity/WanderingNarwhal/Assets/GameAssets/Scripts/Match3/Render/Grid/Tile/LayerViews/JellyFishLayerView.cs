using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class JellyFishLayerView : SheetTileLayerViewBase
    {
        private JellyFishLayerRenderer _renderer;
        private int _size;
        private int _prevLevel = -1;
        private TileKinds _currentKind;

        public JellyFishLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<JellyFishLayerRenderer>();
            _renderer.InitialSetup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

         protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                _currentKind = (TileKinds) tile.GetParam(TileParamEnum.JellyFishColor);
                _renderer.Setup(_currentKind, false);
                AudioProxy.PlaySound(Match3SoundIds.JellyFishBreak);
                _renderer.PlayHit(newLevel);
            }
            _prevLevel = newLevel;
        }
        
        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            _currentKind = (TileKinds) tile.GetParam(TileParamEnum.JellyFishColor);
            if (!Applied)
            {
                base.Apply(tile, coords, container, viewsList, isVisible);
                _renderer.Setup(_currentKind, true);
            }
            else
            {
                _renderer.Setup(_currentKind, false);
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    FxRenderer.SpawnSingleAnimatorEffectWithCustomParameters(coords, FxType.JellyFishTileDestroy, new FxOptionalParameters{ col = _renderer.GetCurrentColor(_currentKind, true)}, _renderer.DestroyTileFXDuration);
                    _renderer.PlayDestroy(() => { IsPlayingLayerViewAnimation = false; });
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
