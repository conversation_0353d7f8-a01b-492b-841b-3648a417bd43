using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class LitterLayerView : TileLayerViewBase
    {
        private LitterRenderer _renderer;

        public LitterLayerView(ITileLayer layer) : base(layer)
        {
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile,coords,  container, viewsList, isVisible);
            _renderer.Setup(tileResourceSelector.SelectedLitterVariant);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            Animator = instance.GetComponent<Animator>();
            _renderer = instance.GetComponent<LitterRenderer>();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.LitterViewDestroy);
                    FxRenderer.SpawnLitterDestroy(coords);
                    IsPlayingLayerViewAnimation = true;

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }

                    _renderer.PlayDestroy(OnDone);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}