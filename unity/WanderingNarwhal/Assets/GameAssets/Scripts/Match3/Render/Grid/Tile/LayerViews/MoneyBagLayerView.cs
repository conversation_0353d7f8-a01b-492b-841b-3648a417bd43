using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MoneyBagLayerView : TileLayerViewBase
    {
        private MoneyBagLayerRenderer _renderer;

        public MoneyBagLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<MoneyBagLayerRenderer>();
            _renderer.Activate();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            if (_renderer != null)
            {
                _renderer.PLayAppear();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.MoneyBagDestroy);
                    IsPlayingLayerViewAnimation = true;

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }

                    _renderer.PlayDestroy(OnDone);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}