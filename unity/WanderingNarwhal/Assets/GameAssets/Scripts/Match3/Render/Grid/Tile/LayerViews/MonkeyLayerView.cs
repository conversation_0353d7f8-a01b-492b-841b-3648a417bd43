using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MonkeyLayerView : SheetTileLayerViewBase
    {
        private MonkeyBaseLayerRenderer _renderer;

        private int _prevLevel;

        public MonkeyLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<MonkeyBaseLayerRenderer>();
            _renderer.CellSize = cellSize;
            _renderer.Setup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.RestoresCount);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.SetLayer(newLevel);
            if (newLevel < _prevLevel)
            {
                _renderer.PlayHit();
            }

            _prevLevel = newLevel;
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }

                    _renderer.PlayDestroy(onDone: OnDone);
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}