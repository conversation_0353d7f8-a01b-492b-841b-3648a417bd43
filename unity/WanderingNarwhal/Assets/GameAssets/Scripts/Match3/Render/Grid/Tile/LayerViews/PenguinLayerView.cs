using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class PenguinLayerView : TileLayerViewBase
    {
        private PenguinLayerRenderer _renderer;

        public PenguinLayerView(ITileLayer layer) : base(layer) { }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<PenguinLayerRenderer>();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.PenguinDestroy);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}