using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SheepLayerView : SheetTileLayerViewBase
    {
        public SheepLayerView(ITileLayer layer) : base(layer)
        {
        }

        private SheepLayerRenderer _renderer;

        private int _prevLevel;

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SheepLayerRenderer>();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            if (anim == TileLayerViewAnims.Destroy)
            {
                IsPlayingLayerViewAnimation = true;

                void OnDone()
                {
                    IsPlayingLayerViewAnimation = false;
                }

                _renderer.PlayDestroy(coords, onDone: OnDone);
            }
            else if (anim == TileLayerViewAnims.CustomAppear)
            {
                _renderer.PlayAppear();
            }
            else if (anim == TileLayerViewAnims.TapFeedback)
            {
                _renderer.PlayTapFeedback(this);
            }
            else if (anim == TileLayerViewAnims.Preview)
            {
                _renderer.PlayPreview();
            }
        }

        public override void UnApply()
        {
            base.UnApply();
            _prevLevel = 0;
            _renderer.ResetToDefault();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.SetViewLevel(newLevel);
            if (newLevel < _prevLevel)
            {
                _renderer.PlayHit();
            }

            _prevLevel = newLevel;
        }
    }
}