using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SkunkLayerView : TileLayerViewBase
    {
        private SkunkLayerRenderer _renderer;
    
        public SkunkLayerView(ITileLayer layer) : base(layer) { }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SkunkLayerRenderer>();
            _renderer.Setup();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            _renderer.OnRefresh();
        }

        public void PlayHitTileAtPos(Coords from, Coords target, Action callback)
        {
            _renderer.PlayHit();
            this.FxRenderer.SpawnSkunkFx(from, target, callback);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            if (_renderer != null)
            {
                if (anim == TileLayerViewAnims.CustomAppear)
                {
                    _renderer.PlayAppear();
                }
                else if (anim == TileLayerViewAnims.TapFeedback)
                {
                    _renderer.PlayTapFeedback(this);
                }
                else if (anim == TileLayerViewAnims.Preview)
                {
                    _renderer.PlayPreview();
                }
            }
        }
    }
}