using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class TukTukLayerView : SheetTileLayerViewBase
    {
        private TukTukLayerRenderer _renderer;
        private int _prevLevel = -1;

        public TukTukLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<TukTukLayerRenderer>();
            _renderer.InitialSetup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.TukTukCount);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                SetupRendererSize(tile);
                if (newLevel >= 0)
                {
                    AudioProxy.PlaySound(Match3SoundIds.TukTukCountdown);
                }
            }
            _prevLevel = newLevel;
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            SetupRendererSize(tile);
            _renderer.OnRefresh();
        }

        private void SetupRendererSize(Tile tile)
        {
            if (_renderer == null || tile == null)
                return;
            
            var color  = tile.GetParam(TileParamEnum.TukTukColor);
            var count = tile.GetParam(TileParamEnum.TukTukCount);
            var orientation = tile.GetParam(TileParamEnum.TukTukOrientation);
            _renderer.UpdateSize(color, orientation, count, false);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    _renderer.PlayDestroy();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
