using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level;
using BBB.Wallet;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class TileRevealFinishedEvent : IEvent
    {
    }

    public class TileRevealJustFinishedEvent : IEvent
    {
    }

    public class TileRevealer : IContextInitializable, IContextReleasable
    {
        private List<AutoBoostInstance> _boosterInstances = new();
        private readonly List<AutoBoostInstance> _allBoosterCells = new();

        private Func<Coords, Vector2> _coordsConverter;
        private Func<Vector2, Vector2> _worldPosConverter;
        private GameController _gameController;
        private BoardRevealSettings _boardRevealSettings;
        private TurnsPanelController _turnsPanel;
        private IEventDispatcher _eventDispatcher;
        private TileController _tileController;
        private HashSet<FallRotateAnimator> _fallRotateAnimatorsBeingRevealed = new();
        private ExtraBoostersHelper _extraBoostersHelper;
        private IPlayerManager _playerManager;
        private SuperBoostSystem _superBoostSystem;
        private ButlerGiftGlobeController _butlerGiftGlobeController;
        private IButlerGiftManager _butlerGiftManager;
        private Match3SimulationPlayer _match3SimulationPlayer;
        private readonly List<AutoBoostInstance> _butlerBoostsList = new();
        private readonly List<AutoBoostInstance> _regularBoostsList = new();
        private WaitUntil _waitUntilBoosterCanBePlaced;

        private IInventory Inventory => _playerManager.PlayerInventory;

        public void InitializeByContext(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            var gridController = context.Resolve<IGridController>();
            _coordsConverter = gridController.ToLocalPosition;
            _worldPosConverter = gridController.ToDisplacedWorldPosition;
            _gameController = context.Resolve<GameController>();
            _turnsPanel = context.Resolve<TurnsPanelController>();
            _boardRevealSettings = context.Resolve<M3Settings>().BoardRevealSettings;
            _tileController = context.Resolve<TileController>();
            _extraBoostersHelper = context.Resolve<ExtraBoostersHelper>();
            _butlerGiftGlobeController = context.Resolve<ButlerGiftGlobeController>();
            _butlerGiftManager = context.Resolve<IButlerGiftManager>();
            _match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();

            _playerManager = context.Resolve<IPlayerManager>();
            _superBoostSystem = context.Resolve<SuperBoostSystem>();
            _waitUntilBoosterCanBePlaced = new WaitUntil(_extraBoostersHelper.CanPlaceBoosterToRandomCell);
        }

        public void Reveal(Dictionary<int, TileView> tileViews, Grid grid, int revealMoveChange)
        {
            Prepare(tileViews, grid);
            _fallRotateAnimatorsBeingRevealed.Clear();
            _gameController.StartCoroutineMethod(RevealRoutine(tileViews, grid, revealMoveChange));
        }

        public void ForceFinishReveal()
        {
            var list = new List<FallRotateAnimator>(_fallRotateAnimatorsBeingRevealed);
            foreach (var animator in list)
            {
                animator.ForceComplete();
            }

            _fallRotateAnimatorsBeingRevealed.Clear();
        }

        private void Prepare(Dictionary<int, TileView> tileViews, Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                var tile = cell.Tile;

                if (ReferenceEquals(tile, null))
                    continue;

                if (!tileViews.TryGetValue(tile.Id, out var tileView)) continue;
                
                if (tileView.CanTileHaveAnimatedReactions())
                {
                    tileView.Hide();
                    tileView.TileMotionController.SetPosAndFreeze(new Vector2(cell.Coords.X, grid.Height));
                }
                else
                {
                    tileView.TileMotionController.SetPosAndFreeze(cell.Coords.ToUnityVector2());
                }
            }
        }

        private IEnumerator RevealRoutine(Dictionary<int, TileView> tileViews, Grid grid, int revealMoveChange)
        {
            BDebug.Log(LogCat.Match3, "Tile Reveal Start, InputLock = true");
            _gameController.LockInput(true);

            var lastCell = grid.Cells.FindLast(cell => !ReferenceEquals(cell.Tile, null));

            // preventing last cell being a tile which does not have tile animated reaction
            var lastCellWhichAnimates = grid.Cells.FindLast(cell => !ReferenceEquals(cell.Tile, null) &&
                                                                    tileViews[cell.Tile.Id].CanTileHaveAnimatedReactions());
            var lastAnimator = tileViews[lastCellWhichAnimates.Tile.Id].Animator;

            var extraBoostersMinimumTime = (grid.Height - 1) * _boardRevealSettings.RowsRevealDelay;
            var settleBoostersRoutine = _gameController.StartCoroutineMethod(SettleBoosters(grid, extraBoostersMinimumTime));
            for (var y = 0; y < grid.Height; y++)
            {
                var fallingTime = CalculateFallingTime(y, grid);

                Cell cell = null;
                for (var x = 0; x < grid.Width; x++)
                {
                    var coords = new Coords(x, y);
                    cell = grid.GetCell(coords);

                    if (cell == null)
                        continue;

                    StartRevealFor(cell, tileViews, fallingTime);
                }

                AudioProxy.PlaySound(Match3SoundIds.TileRowRevealSpawn);
                if (cell != lastCell)
                    yield return WaitCache.Seconds(_boardRevealSettings.RowsRevealDelay);
            }

            const StateType state = StateType.Fall | StateType.Settle;
            while (lastAnimator.IsAnyPlaying(state))
                yield return null;

            AudioProxy.PlaySound(Match3SoundIds.BoardRevealTileSettle);

            yield return settleBoostersRoutine;

            var centerVec = grid.GetCenterVec2();
            if (lastCell != null)
            {
                var maxDistance = lastCell.Coords.DistanceFrom(centerVec);
                var sortedCells = new List<Cell>(grid.Cells);

                sortedCells.Sort((c1, c2) => 
                    c1.Coords.SqrDistanceFrom(centerVec).CompareTo(c2.Coords.SqrDistanceFrom(centerVec)));

                foreach (var cell in sortedCells)
                {
                    var tile = cell.Tile;

                    if (ReferenceEquals(tile, null))
                        continue;

                    if (!tileViews.TryGetValue(tile.Id, out var tileView)) continue;
                    
                    var distance = cell.Coords.DistanceFrom(centerVec);
                    var time = _boardRevealSettings.ShockwaveMaxDelay * distance / maxDistance;

                    Rx.Invoke(time, _ =>
                    {
                        AudioProxy.PlaySound(Match3SoundIds.Shockwave);
                        tileView.Animator.StartShockwaveEffect(CardinalDirections.NE);
                    });
                }
            }

            yield return WaitCache.Seconds(_boardRevealSettings.ShockwaveMaxDelay);

            if (revealMoveChange > 0)
            {
                var animateFromCount = _gameController.RemainingMoves - revealMoveChange;
                AudioProxy.PlaySound(Match3SoundIds.Match4);
                _turnsPanel.PlayHit();
                _turnsPanel.AnimateNewMoves(revealMoveChange, animateFromCount, shouldPlayHaptics: true);
            }

            _fallRotateAnimatorsBeingRevealed.Clear();

            BDebug.Log(LogCat.Match3, "Tile Reveal done, InputLock = false");
            _gameController.LockInput(false);
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<TileRevealJustFinishedEvent>());
            _eventDispatcher.TriggerEventNextFrame(_eventDispatcher.GetMessage<TileRevealFinishedEvent>());
        }

        private IEnumerator SettleBoosters(Grid grid, float extraBoostersMinimumDelay)
        {
            // selected boosters
            var boosters = new List<AutoBoostInstance>();
            var boosterNames = new List<string>(Inventory.EquippedAutoBoosters);
            foreach (var boosterName in boosterNames)
            {
                boosters.Add(new AutoBoostInstance(boosterName));
            }

            if (!_extraBoostersHelper.CanPlaceAllBoosters(boosters))
            {
                var pendingAutoBoosterAddedEvent = _eventDispatcher.GetMessage<PendingAutoBoosterAddedEvent>();
                pendingAutoBoosterAddedEvent.Set(boosters,
                    (uid, coords, extraInfo) =>
                    {
                        var equippedAutoBoostAppliedEvent = _eventDispatcher.GetMessage<EquippedAutoBoostAppliedEvent>();
                        equippedAutoBoostAppliedEvent.Set(uid, coords, extraInfo, true);
                        _eventDispatcher.TriggerEvent(equippedAutoBoostAppliedEvent);
                    });
                _eventDispatcher.TriggerEvent(pendingAutoBoosterAddedEvent);
            }
            else
            {
                var startTime = Time.time;

                _butlerBoostsList.Clear();
                foreach (var booster in boosters)
                {
                    if (_butlerGiftManager.IsButlerGift(booster.Name))
                    {
                        if (booster.Name == InventoryBoosters.LineBreakerBoosterButler)
                        {
                            var direction = booster.ExtraInfo != null
                                ? (SimplifiedDirections)booster.ExtraInfo
                                : RandomSystem.Next() > 0.5f
                                    ? SimplifiedDirections.Horizontal
                                    : SimplifiedDirections.Vertical;

                            _butlerBoostsList.Add(new AutoBoostInstance(booster.Name, booster.CellPos, direction));
                        }
                        else
                        {
                            _butlerBoostsList.Add(booster);
                        }
                    }
                }

                if (!_butlerBoostsList.IsNullOrEmpty())
                {
                    yield return WaitCache.Seconds(_boardRevealSettings.ButlerGlobeAppearingDelay);
                    
                    _butlerGiftGlobeController.SetupBoosterData(_butlerBoostsList);
                    _butlerGiftGlobeController.Play();
                    
                    yield return new WaitUntil(() => !_butlerGiftGlobeController.IsActiveAndRunning);
                    
                    yield return SettleExtraBoosters(grid, _butlerBoostsList, true, false, true);
                }

                extraBoostersMinimumDelay += _boardRevealSettings.EquippedBoostersAppearingDelay;
                var passedTime = Time.time - startTime;
                if (passedTime < extraBoostersMinimumDelay)
                    yield return WaitCache.Seconds(extraBoostersMinimumDelay - passedTime);

                if (Inventory.BonusBooster is InventoryBoosters.LineCrushBooster or InventoryBoosters.BombBooster
                    or InventoryBoosters.LightningStrikeBooster)
                {
                    boosters.Add(new AutoBoostInstance(Inventory.BonusBooster));
                }

                _regularBoostsList.Clear();
                foreach (var boost in boosters)
                {
                    if (!_butlerGiftManager.IsButlerGift(boost.Name))
                    {
                        _regularBoostsList.Add(boost);
                    }
                }

                yield return SettleExtraBoosters(grid, _regularBoostsList, true);
            }

            while (_match3SimulationPlayer.IsPlayingVisualSimulation())
            {
                yield return null;
            }
        }

        public IEnumerator SettleExtraBoosters(Grid grid, IEnumerable<AutoBoostInstance> boosters, bool isLevelStart,
            bool isSuperBoost = false, bool isButlerGift = false)
        {
            _allBoosterCells.Clear();
            List<AutoBoostInstance> pendingBoosters = null;
            var boostersOrdered = new List<AutoBoostInstance>(boosters);

            boostersOrdered.Sort((boost1, boost2) => 
            {
                var isBoost1ButlerGift = boost1 != null && _butlerGiftManager.IsButlerGift(boost1.Name);
                var isBoost2ButlerGift = boost2 != null && _butlerGiftManager.IsButlerGift(boost2.Name);

                return isBoost1ButlerGift.CompareTo(isBoost2ButlerGift);
            });

            foreach (var booster in boostersOrdered)
            {
                if (booster == null)
                    continue;

                yield return _waitUntilBoosterCanBePlaced;

                _boosterInstances.Clear();
                _extraBoostersHelper.PlaceBoosterToRandomCell(booster, _gameController.RemainingMoves, ref _boosterInstances);

                if (_boosterInstances.IsNullOrEmpty())
                {
                    pendingBoosters ??= new List<AutoBoostInstance>();
                    pendingBoosters.Add(new AutoBoostInstance(booster.Name, booster.CellPos ?? Coords.OutOfGrid, booster.ExtraInfo));
                    continue;
                }

                var boostRevealSettings = _boardRevealSettings.GetBoostersRevealSettings(isButlerGift);
                var pauseBetween = boostRevealSettings.PauseBetweenBoosters;
                var revealTime = boostRevealSettings.BoosterFlightTime;
                var alphaFadeInTime = boostRevealSettings.AlphaFadeInTime;
                var startScale = boostRevealSettings.StartScale;
                var movementCurve = boostRevealSettings.ScaleCurve;
                var fxTrailDuration = boostRevealSettings.FxTrailLifetime;

                foreach (var autoBoostInstance in _boosterInstances)
                {
                    var cellPos = autoBoostInstance.CellPos;
                    if (cellPos.HasValue && grid.TryGetCell(cellPos.Value, out var cell) && !ReferenceEquals(cell.Tile, null))
                    {
                        if (isSuperBoost)
                        {
                            _superBoostSystem.RegisterSuperBoostTile(cell.Tile.Id);
                        }

                        var tileView = _tileController.GetExistingTileView(cell.Tile.Id);
                        if (tileView != null)
                        {
                            tileView.SetAlpha(0f);
                            tileView.TileMotionController.SetPosAndFreeze(cell.Coords.ToUnityVector2());
                            if (isButlerGift && _butlerGiftManager.ShowGlobeIntro)
                            {
                                _butlerGiftGlobeController.HideImage(booster.Name, alphaFadeInTime);
                                var endPos = _worldPosConverter(tileView.Coords.ToUnityVector2());
                                var followingTransform = _butlerGiftGlobeController.GetBoosterPath(booster.Name, endPos);
                                tileView.SpawnFromPosition(tileView.LocalPosition, revealTime, followingTransform, fxTrailDuration);
                            }

                            tileView.FadeInAppear(startScale, revealTime, alphaFadeInTime, movementCurve);
                        }
                    }

                    if (!isSuperBoost)
                    {
                        _allBoosterCells.Add(autoBoostInstance);
                    }
                }

                yield return WaitCache.Seconds(pauseBetween);
            }

            if (!_allBoosterCells.IsNullOrEmpty())
            {
                foreach (var booster in _allBoosterCells)
                {
                    var equippedAutoBoostAppliedEvent = _eventDispatcher.GetMessage<EquippedAutoBoostAppliedEvent>();
                    equippedAutoBoostAppliedEvent.Set(booster.Name, booster.CellPos, booster.ExtraInfo, isLevelStart);
                    _eventDispatcher.TriggerEvent(equippedAutoBoostAppliedEvent);
                }
            }

            if (!pendingBoosters.IsNullOrEmpty())
            {
                var pendingAutoBoosterAddedEvent = _eventDispatcher.GetMessage<PendingAutoBoosterAddedEvent>();
                pendingAutoBoosterAddedEvent.Set(pendingBoosters,
                    (uid, coords, extraInfo) =>
                    {
                        var equippedAutoBoostAppliedEvent = _eventDispatcher.GetMessage<EquippedAutoBoostAppliedEvent>();
                        equippedAutoBoostAppliedEvent.Set(uid, coords, extraInfo, isLevelStart);
                        _eventDispatcher.TriggerEvent(equippedAutoBoostAppliedEvent);
                    });
                _eventDispatcher.TriggerEvent(pendingAutoBoosterAddedEvent);
            }
        }

        private Vector2 GetStartPos(int x, Grid grid)
        {
            return _coordsConverter(new Coords(x, grid.Height));
        }

        private void StartRevealFor(Cell cell, Dictionary<int, TileView> tileViews, float fallingTime)
        {
            var tile = cell.Tile;

            if (ReferenceEquals(tile, null))
                return;

            TileView tileView;
            if (tileViews.TryGetValue(tile.Id, out tileView))
            {
                tileView.Show();
                if (tileView.CanTileHaveAnimatedReactions())
                {
                    tileView.UpdatePositionDependentProperties();
                    tileView.TileMotionController.SetToZeroGravity();
                    tileView.Animator.Fall();
                    var fallRotateAnimator = tileView.gameObject.GetOrAddComponent<FallRotateAnimator>();
                    fallRotateAnimator.Setup();
                    fallRotateAnimator.SetLifetime(fallingTime)
                        .SetTranslation(Vector2.zero, _boardRevealSettings.TilesFallingAcceleration)
                        .SetRotation(-_boardRevealSettings.RotationSpeed * fallingTime, _boardRevealSettings.RotationSpeed)
                        .OnDone(() =>
                        {
                            tileView.TileMotionController.SetPosAndFreeze(cell.Coords.ToUnityVector2());
                            tileView.Animator.Settle();
                            _fallRotateAnimatorsBeingRevealed.Remove(fallRotateAnimator);
                            fallRotateAnimator.Stop();
                        })
                        .Run();

                    _fallRotateAnimatorsBeingRevealed.Add(fallRotateAnimator);
                }
                else
                {
                    tileView.CustomAnimatedAppear();
                }
            }
        }

        private float CalculateFallingTime(int targetY, Grid grid)
        {
            int x = 0;
            var startPos = GetStartPos(x, grid);
            var targetPos = _coordsConverter(new Coords(x, targetY));
            var pathLength = targetPos.y - startPos.y;
            return Mathf.Sqrt(2f * pathLength / _boardRevealSettings.TilesFallingAcceleration);
        }


        public void ReleaseByContext(IContext context)
        {
            _coordsConverter = null;
            _worldPosConverter = null;
        }
    }
}