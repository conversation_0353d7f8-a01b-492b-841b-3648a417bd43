using System.Collections.Generic;
using BBB.CellTypes;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BBB.UI.Level;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public sealed class GridController : RendererBase, IGridController, IContextReleasable, IGridoverlayContainer
    {
        [SerializeField] private RectTransform _overlayRectTransform;
        [SerializeField] private RectTransform _rainRectTransform;
        [SerializeField] private RectTransform _perimeterRectTransform;
        [SerializeField] private RectTransform _rootBoardTransform;
        [SerializeField] private RectTransform _hintRectTransform;
        [Space]
        [SerializeField] private float _gridRevealTime = 0.5f;
        [SerializeField] private float _gridRevealTimeDelay = 0.2f;
        [SerializeField] private float _horizontalOffsetMultiplier = 1.1f;
        [SerializeField] private AnimationCurve _gridRevealEase;

        private static readonly List<TileView> TempTilesCache = new();

        private Animator _gridGroupAnimator;
        private RectTransform _rectTransform;
        private ICellController _cellController;
        private TileController _tileController;
        private TilesResources _tileResources;
        private IPerimeterRenderer _perimeterRenderer;
        private IEventDispatcher _eventDispatcher;
        private GlassRenderer _glassRenderer;
        private BoardRevealer _boardRevealer;
        private IVibrationsWrapper _vibrations;
        private readonly List<IBoardRevealObserver> _boardRevealObservers = new();

        private Camera _levelCamera;
        private Grid _grid;
        private Vector2 _gridSizeByCells;
        private M3Settings _m3Settings;
        private Transform _gridGroupTransform;
        private TileTickPlayer _tileTickPlayer;
        private Tweener _delayTween;
        private Tweener _shakeTween;
        private readonly Dictionary<Transform, Vector3> _preShakePositions = new();
        private ILevel _level;

        public RectTransform Transform => _rectTransform ? _rectTransform : (_rectTransform = GetComponent<RectTransform>());

        public RectTransform OverlayTransform => _overlayRectTransform;
        public RectTransform RainOverlayTransform => _rainRectTransform;
        public RectTransform PerimeterTransform => _perimeterRectTransform;
        public RectTransform RootBoardTransform => _rootBoardTransform;
        public RectTransform HintRectTransform => _hintRectTransform;

        public override void InitializeByContext(IContext context)
        {
            _boardRevealObservers.Clear();
            base.InitializeByContext(context);
            if (_boardRevealer == null)
            {
                _boardRevealer = new BoardRevealer(context);
                _glassRenderer = new GlassRenderer(context);
                _levelCamera = context.Resolve<Camera>(Match3Constants.LevelCameraTag);
            }

            _cellController = context.Resolve<ICellController>();
            _tileController = context.Resolve<TileController>();
            _tileTickPlayer = context.Resolve<TileTickPlayer>();
            _tileResources = context.Resolve<TilesResources>();
            _perimeterRenderer = context.Resolve<IPerimeterRenderer>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _m3Settings = context.Resolve<M3Settings>();
            _eventDispatcher.RemoveListener<LevelExitFromOutOfMovesEvent>(OnLevelLoseByMoves);
            _eventDispatcher.AddListener<LevelExitFromOutOfMovesEvent>(OnLevelLoseByMoves);
            _vibrations = context.Resolve<IVibrationsWrapper>();

            if (_gridGroupTransform == null)
                _gridGroupTransform = Transform.parent;

            if (_gridGroupAnimator == null)
                _gridGroupAnimator = _gridGroupTransform.GetComponent<Animator>();

            _gridGroupAnimator.Rebind();
            _gridGroupAnimator.Update(0f);
            _gridGroupTransform.localPosition = Vector3.zero;
            _gridGroupTransform.localRotation = Quaternion.identity;
        }

        public void SetupTransformSizes()
        {
            _gridSizeByCells = new Vector2(_grid.Width, _grid.Height);

            var gridSize = GridSize;
            _rectTransform.sizeDelta = gridSize;
            _overlayRectTransform.sizeDelta = gridSize;
        }

        public void DoTweenShakeLevelCamera(float overrideDuration = -1, ShakeSettingsType shakeSettings = ShakeSettingsType.ClearBoard)
        {
            DoTweenShakeTransform(_levelCamera.transform, overrideDuration, shakeSettings);
        }

        public void DoTweenShakeBoard(float overrideDuration = -1, ShakeSettingsType shakeSettings = ShakeSettingsType.ClearBoard)
        {
            DoTweenShakeTransform(Transform.parent.parent, overrideDuration, shakeSettings);
        }

        private void DoTweenShakeTransform(Transform targetTf, float overrideDuration = -1, ShakeSettingsType shakeSettings = ShakeSettingsType.ClearBoard)
        {
            var shakeParams = _m3Settings.GetShakeParams(shakeSettings);
            var duration = overrideDuration > 0f ? overrideDuration : shakeParams.Duration;

            _delayTween = Rx.Invoke(shakeParams.Delay, _ =>
            {
                if (targetTf.gameObject.activeInHierarchy)
                {
                    _shakeTween?.Kill();

                    if (!_preShakePositions.ContainsKey(targetTf))
                        _preShakePositions[targetTf] = targetTf.localPosition;

                    _shakeTween = targetTf.DOShakePosition(duration, shakeParams.Amplitude, shakeParams.Frequency, shakeParams.VectorAngleSpread, _m3Settings.Snapping, _m3Settings.FadeOut)
                        .OnComplete(() =>
                        {
                            targetTf.localPosition = _preShakePositions[targetTf];
                            _shakeTween = null;
                        }).OnKill(() => { targetTf.localPosition = _preShakePositions[targetTf]; });

                    _vibrations.PlayHaptic(ImpactPreset.HeavyImpact);
                }

                _delayTween = null;
            });
        }

        public void AddRevealObserver(IBoardRevealObserver observer)
        {
            _boardRevealObservers.Add(observer);
        }

        public void MoveGlassBehind(Coords targetCoords)
        {
            _glassRenderer.MoveGlassBehind(targetCoords);
        }

        public void MoveGlassToFront(Coords targetCoords)
        {
            _glassRenderer.MoveGlassToFront(targetCoords);
        }

        public void DimBoard(bool value, float dimTimeOverride = -1f)
        {
            _glassRenderer.Dim(value, dimTimeOverride);
        }

        public void ReleaseByContext(IContext context)
        {
            _boardRevealObservers.Clear();
            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<LevelExitFromOutOfMovesEvent>(OnLevelLoseByMoves);
            }

            Clear(true);
        }

        private void OnLevelLoseByMoves(LevelExitFromOutOfMovesEvent ev)
        {
            var delay = _m3Settings.GridFallAnim.StartDelay;

            void PlayFallGridOnGameLoseAction(long _)
            {
                PlayFallGridOnGameLose(returnBack: ev.IsDebug);
            }

            if (delay > 0)
            {
                Rx.Invoke(delay, PlayFallGridOnGameLoseAction);
            }
            else
            {
                PlayFallGridOnGameLoseAction(0);
            }
        }

        public void OnSkip()
        {
            _delayTween?.Kill();
            _delayTween = null;
            _shakeTween?.Kill();
            _shakeTween = null;
        }

        public void Clear(bool cleanupPools = false)
        {
            _perimeterRenderer.Clear();
            _tileController.Clear();
            _cellController.ClearGrid(cleanupPools);
            _glassRenderer.Clear(cleanupPools);
            _perimeterRenderer.Clear();
        }

        public void SetupLevel(ILevel level)
        {
            _grid = level.Grid;
            _level = level;
        }

        public void SetupGrid(Grid grid)
        {
            _grid = grid;
            _cellController.SetGrid(grid);
            _perimeterRenderer.RenderGrid(grid, this, _level);
            _glassRenderer.SetGrid(grid);
            _boardRevealer.Refresh();
        }

        public void ForceUpdateGridTiles()
        {
            _tileController.Update(_grid, force: true);
        }

        public void ForceUpdatePerimeter()
        {
            _perimeterRenderer.RenderGrid(_grid, this, _level);
        }

        public void RevealGrid(bool instant, int revealMoveChange)
        {
            SetupTransformSizes();

            if (instant)
            {
                _tileController.CreateTileViewsInstant(_grid);
            }
            else
            {
                foreach (var tileView in _tileController.GetAllTileViews())
                    tileView.Hide();

                _boardRevealer.Reveal(() => { _tileController.CreateAndRevealTileViews(_grid, revealMoveChange); });

                var startPosition = new Vector2(GridSize.x * _horizontalOffsetMultiplier, 0f);
                _rootBoardTransform.anchoredPosition = startPosition;
                DOTween.To(value => { _rootBoardTransform.anchoredPosition = Vector2.LerpUnclamped(startPosition, Vector2.zero, value); }, 0f, 1f, _gridRevealTime)
                    .SetEase(_gridRevealEase)
                    .SetDelay(_gridRevealTimeDelay)
                    .OnComplete(() => _rootBoardTransform.anchoredPosition = Vector2.zero);

                foreach (var observer in _boardRevealObservers)
                    observer.OnBoardRevealStart();
            }
        }

        [ContextMenu("Play FallGrid Anim")]
        private void DebugPlayFallGridAnimation()
        {
            OnLevelLoseByMoves(new LevelExitFromOutOfMovesEvent() { IsDebug = true });
        }

        private void PlayFallGridOnGameLose(bool returnBack = false)
        {
            TempTilesCache.Clear();
            if (this == null || _tileController == null) return;
            var isAnyTileFall = false;
            foreach (var tileView in _tileController.GetAllTileViews())
            {
                isAnyTileFall = true;
                tileView.Animator.AnimateFallGridOnLevelLose(_m3Settings.GridFallAnim, returnBack);
            }

            if (isAnyTileFall)
            {
                AudioProxy.PlaySound(_m3Settings.GridFallAnim.StartSoundFxUid);
            }

            foreach (var tileView in TempTilesCache)
            {
                Cell cell;
                if (_grid.TryGetCell(tileView.TileMotionController.Coords, out cell))
                {
                    _tileController.VisualizeTileDestruction(cell, TileLayerViewAnimParams.EndGame);
                    if (cell.HasTile())
                        _tileController.ReleaseTileWhenCan(cell.Tile.Id, Coords.OutOfGrid);
                    //_tileController.DestroyTile(cell, removeOffset: 0, damageSource: DamageSource.Adjacent, skipAnim: false, endGameDestroy: true);
                }
            }
        }

        public bool IsSwappableCell(Coords coord)
        {
            return _grid.IsSwappableCell(coord);
        }

        public bool HasTile(Coords coord)
        {
            Cell cell;
            return _grid.TryGetCell(coord, out cell)
                   && (!ReferenceEquals(cell.Tile, null) || cell.HasMultiSizeCellReferenceWithMultiSizeTile());
        }

        public Tile GetTile(Coords coord)
        {
            return _grid.TryGetCell(coord, out var cell) && cell.HasTile() ? cell.Tile : null;
        }

        public bool HasSwappableTile(Coords coords)
        {
            return IsSwappableCell(coords) && HasTile(coords);
        }

        public Vector2 ToDisplacedLocalPosition(Coords coords)
        {
            var cellSize = CellSize;
            var gridSize = CurrentGrid.Size;

            return new Vector2(
                (coords.X + 0.5f - gridSize.x * 0.5f) * cellSize.x,
                (coords.Y + 0.5f - gridSize.y * 0.5f) * cellSize.y);
        }

        public Vector2 ToLocalPosition(Coords coords)
        {
            var cellSize = CellSize;
            return new Vector2(
                (coords.X + 0.5f) * cellSize.x,
                (coords.Y + 0.5f) * cellSize.y);
        }

        public Vector2 ToLocalPosition(Vector2 coords)
        {
            var cellSize = CellSize;
            return new Vector2(
                (coords.x + 0.5f) * cellSize.x,
                (coords.y + 0.5f) * cellSize.y);
        }

        public Vector2 ToDisplacedWorldPosition(Vector2 vec2)
        {
            var cellSize = CellSize;
            var gridSize = CurrentGrid.Size;
            var result = new Vector2(
                (vec2.x + 0.5f - gridSize.x * 0.5f) * cellSize.x,
                (vec2.y + 0.5f - gridSize.y * 0.5f) * cellSize.y);
            return transform.TransformPoint(result);
        }

        public Vector2 ToWorldPosition(Vector2 vec2)
        {
            return transform.TransformPoint((vec2 + Vector2.one * 0.5f) * CellSize.x);
        }

        public Vector2 GetCurrentGridSize()
        {
            return CurrentGrid.Size;
        }

        public Vector2 GetLowestPosition()
        {
            var minY = int.MaxValue;

            foreach (var cell in _grid.Cells)
            {
                if (cell.Coords.Y < minY)
                {
                    minY = cell.Coords.Y;
                }
            }

            var vec2 = new Coords(0, minY).ToUnityVector2();
            return transform.TransformPoint(vec2 * CellSize.x - GridSize / 2);
        }

        public Vector2 GetCenterPosition()
        {
            return transform.TransformPoint(Vector3.zero);
        }

        public Coords GetGridCoords(Vector3 pointer)
        {
            var localPoint = GetGridInputPosition(pointer);
            return localPoint.x < 0 || localPoint.y < 0
                ? Coords.OutOfGrid
                : new Coords(Mathf.FloorToInt(localPoint.x), Mathf.FloorToInt(localPoint.y));
        }

        public Vector2 GetGridInputPosition(Vector3 pointer)
        {
            if (!RectTransformUtility.RectangleContainsScreenPoint(_rectTransform, pointer, _levelCamera))
            {
                return Coords.OutOfGridVector2;
            }

            return GetGridLocalPoint(pointer);
        }

        public Vector2 GetGridLocalPoint(Vector3 pointer)
        {
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(_rectTransform, pointer, _levelCamera, out localPoint);
            return Vector2.Scale(Rect.PointToNormalized(_rectTransform.rect, localPoint), _gridSizeByCells);
        }

        private static float InverseLerpNoClamp(float a, float b, float value) => a != b ? (value - a) / (b - a) : 0.0f;

        public Coords GetOutGridCoords(Vector3 pointer)
        {
            var scaledPoint = GetOutGridVector2(pointer);
            return new Coords(Mathf.FloorToInt(scaledPoint.x), Mathf.FloorToInt(scaledPoint.y));
        }

        public Vector2 GetOutGridVector2(Vector3 pointer)
        {
            RectTransformUtility.ScreenPointToLocalPointInRectangle(_rectTransform, pointer, _levelCamera, out Vector2 localPoint);
            Rect rect = _rectTransform.rect;
            Vector2 normalized = new Vector2(InverseLerpNoClamp(rect.x, rect.xMax, localPoint.x),
                InverseLerpNoClamp(rect.y, rect.yMax, localPoint.y));
            return Vector2.Scale(normalized, _gridSizeByCells);
        }

        /// <summary>
        /// Get all transform for cells with tile of specific type.
        /// </summary>
        /// <param name="tileType">Tile asset type, for example, drop item.</param>
        /// <returns>All cells with tile type.</returns>
        public IEnumerable<CellView> GetAllCellViewsByTileAsset(TileAsset tileType)
        {
            var result = new List<CellView>();
            if (_grid != null)
            {
                foreach (var item in _grid.Cells)
                {
                    if (item?.Tile?.Asset == tileType)
                    {
                        var tileView = _cellController.GetCellView(item.Coords);
                        if (tileView != null)
                        {
                            result.Add(tileView);
                        }
                    }
                }
            }

            return result;
        }

        public bool IsWaterState(Coords coords)
        {
            return _grid.TryGetCell(coords, out var cell) && cell.IsAnyOf(CellState.Water);
        }

        public bool IsPartOfShelfGroup(Coords coords, int shelfGroupIdentifier)
        {
            return _grid.TryGetCell(coords, out var cell) && cell.HasTile() &&
                   cell.Tile.GetParam(TileParamEnum.ShelfGroupIdentifier) == shelfGroupIdentifier;
        }
    }
}