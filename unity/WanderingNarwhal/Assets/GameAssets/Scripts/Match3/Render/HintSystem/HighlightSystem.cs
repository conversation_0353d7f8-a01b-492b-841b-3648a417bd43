using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3.Renderer
{
    public class HighlightSystem : IContextInitializable
    {
        private IGridController _gridController;
        private TileController _tileController;
        private GameController _gameController;
        private IHintPerimeterRenderer _hintHighlightRenderer;

        private readonly List<Coords> _highlightedCoords = new List<Coords>();
        
        private bool _waitingForReset;

        public void InitializeByContext(IContext context)
        {
            _highlightedCoords.Clear();
            _gameController = context.Resolve<GameController>();
            _gridController = context.Resolve<IGridController>();
            _tileController = context.Resolve<TileController>();
            _hintHighlightRenderer = context.Resolve<IHintPerimeterRenderer>();
        }

        public void ResetDefaults()
        {
            _waitingForReset = default;
        }

        public void LeaveOnlyAvailableTo(BoosterItem boosterItem)
        {
            _waitingForReset = true;

            _highlightedCoords.Clear();

            var grid = _gameController.Grid;
            grid.RefrehsAllCellsMultisizeCaches();

            foreach (var cell in grid.Cells)
            {
                var coords = cell.Coords;
                if (!cell.HasMultiSizeCellReferenceWithCellOverlay()
                    && (cell.HasTile() && cell.Tile.CheckApplicability(boosterItem, cell, grid) || cell.CanCellReceiveDirectDamage(DamageSource.UsableBoost, grid)))
                {
                    _gridController.MoveGlassBehind(coords);
                    _highlightedCoords.Add(coords);
                }
                else
                {
                    var mainCell = cell.GetMainCellReference(out _);
                    if (!mainCell.HasMultiSizeCellReferenceWithCellOverlay() 
                        && (mainCell.HasTile() && mainCell.Tile.CheckApplicability(boosterItem, cell, grid) || mainCell.CanCellReceiveDirectDamage(DamageSource.UsableBoost, grid)))
                    {
                        _gridController.MoveGlassBehind(coords);
                    }
                    else
                    {
                        _gridController.MoveGlassToFront(coords);
                    }
                    _highlightedCoords.Add(coords);
                }
            }

            //_gridController.DimBoard(true, HintSystemConstants.DimTimeOverride);

        }
        
        public void Highlight(IEnumerable<Coords> targetCoords, float cycleTime, int cyclesCount)
        {
            _waitingForReset = true;
            var grid = _gameController.Grid;
            
            foreach (var coords in targetCoords)
            {
                _gridController.MoveGlassBehind(coords);
                _highlightedCoords.Add(coords);
                if (grid.GetCell(coords).HasMultiSizeCellReferenceWithMultiSizeTile()) continue;
                var tileView = _tileController.GetTileViewByCoord(coords, false);
                if (tileView != null)
                {
                    tileView.Animator.StartYoyo(HintSystemConstants.PowerUpHintTileScaleMult, cycleTime, cyclesCount);
                }
            }
            
            _gridController.DimBoard(true, HintSystemConstants.DimTimeOverride);
        }
        
        public void ResetHighlightCells()
        {
            _hintHighlightRenderer.Clear();
        }
        
        public void HighlightCells(IEnumerable<Coords> targetCoords)
        {
            var width = 0;
            var height = 0;
            var coordsList = new List<Coords>();
            Coords? previousCoord = null;

            foreach (var coords in targetCoords)
            {
                coordsList.Add(coords);
                if (previousCoord.HasValue)
                {
                    if (coords.X == previousCoord.Value.X)
                        width++;
                    if (coords.Y == previousCoord.Value.Y)
                        height++;
                }
                previousCoord = coords;
            }

            if (coordsList.Count == 0)
            {
                return;
            }

            width = width > 0 ? width : 1;
            height = height > 0 ? height : 1;

            var grid = new Grid(height, width);
            foreach (var coords in coordsList)
            {
                grid.CreateCell(coords);
            }

            _hintHighlightRenderer.RenderGrid(grid, _gridController);
        }
        
        public void Reset()
        {
            if (!_waitingForReset)
                return;
            
            _waitingForReset = false;
            
            foreach (var coords in _highlightedCoords)
            {
                _gridController.MoveGlassToFront(coords);

                if (_tileController.HasTileViewInCoord(coords))
                {
                    var tileView = _tileController.GetTileViewByCoord(coords, false);
                    if (tileView != null)
                    {
                        tileView.Animator.StopYoyo();
                    }
                }
            }
            
            _highlightedCoords.Clear();
            
            _gridController.DimBoard(false, HintSystemConstants.DimTimeOverride);
        }
    }
}