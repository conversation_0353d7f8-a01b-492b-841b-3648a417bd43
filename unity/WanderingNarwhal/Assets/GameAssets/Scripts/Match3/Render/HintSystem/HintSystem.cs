using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Map;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI;
using BBB.UI.Level.Input;
using BBB.UI.Level.Scripts.Boosts;
using BebopBee.Core;
using BebopBee.Core.Collections;
using Core.Configs;
using DG.Tweening;
using FBConfig;
using GameAssets.Scripts.Match3.Settings;
using JetBrains.Annotations;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class HintSystem : IContextInitializable, IContextReleasable
    {
        private const float AdditionalHintDelay = 2.5f;

        private TilesResources _tilesResources;
        private GameController _gameController;
        private IEventDispatcher _eventDispatcher;
        private IScreensManager _screensManager;
        private ILockManager _lockManager;
        private IBoostButtonsController _boostButtonsController;
        private TileController _tileController;
        private IDisposable _hintTimerDisposable;
        private int _index;


        private GoalsSystem _goalsSystem;
        private M3SpawnSystem _m3SpawnSystem;
        private TileResourceSelector _tileResourceSelector;
        private Tweener _specialHintTweener;
        private Tweener _highlightDelayTweener;
        private ILevel _level;
        private List<string> _eligibleBoosts;
        private GoalsSystem _goalSystem;
        private HighlightSystem _highlightSystem;
        private IInputController _inputController;
        private UserSettings _userSettings;
        private HintSystemConfig _config;
        private List<PossibleMove> _hints;
        private bool _globalEnabled = true;
        private bool _enabled = true;
        private PossibleMove? _currentMove;
        
        private readonly List<string> _boostersUnlocked = new();
        private readonly HashSet<string> _specialHintsDoneThisMatch = new();
        private readonly Multimap<string, HintConditionBase> _powerUpHintConditions = new();
        private static readonly List<string> HelpingUidsList = new();
        private static readonly Type[] RequiredConfigs = {
            typeof(HintSystemConfig)
        };

        public void InitializeByContext(IContext context)
        {
            _enabled = true;
            _boostersUnlocked.Clear();
            _specialHintsDoneThisMatch.Clear();
            
            _lockManager = context.Resolve<ILockManager>();
            _goalSystem = context.Resolve<GoalsSystem>();
            _highlightSystem = context.Resolve<HighlightSystem>();
            _boostButtonsController = context.Resolve<IBoostButtonsController>();
            _tileController = context.Resolve<TileController>();
            _gameController = context.Resolve<GameController>();
            _tilesResources = context.Resolve<TilesResources>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            
            _userSettings = context.Resolve<UserSettings>();
            _userSettings.HintsSettingsChanged -= UserSettingsOnHintsSettingsChanged;
            _userSettings.HintsSettingsChanged += UserSettingsOnHintsSettingsChanged;
            _globalEnabled = _userSettings.HintsOn;

            _eventDispatcher.RemoveListener<TileRevealFinishedEvent>(OnTilesRevealFinishedHandler);
            _eventDispatcher.AddListener<TileRevealFinishedEvent>(OnTilesRevealFinishedHandler);
            
            _eventDispatcher.RemoveListener<RemainingMovesChanged>(OnRemainingMovesChangedHandler);
            _eventDispatcher.AddListener<RemainingMovesChanged>(OnRemainingMovesChangedHandler);
            
            _eventDispatcher.RemoveListener<LevelResultPredicted>(OnLevelResultPredictedHandler);
            _eventDispatcher.AddListener<LevelResultPredicted>(OnLevelResultPredictedHandler);
            
            _eventDispatcher.RemoveListener<BoostButtonPressedEvent>(OnBoostButtonPressedHandler);
            _eventDispatcher.AddListener<BoostButtonPressedEvent>(OnBoostButtonPressedHandler);
            
            _eventDispatcher.RemoveListener<LevelEndedEvent>(LevelEndedHandler);
            _eventDispatcher.AddListener<LevelEndedEvent>(LevelEndedHandler);
            
            _eventDispatcher.RemoveListener<LevelEndedUnfinishedEvent>(OnLevelUnfinishedHandler);
            _eventDispatcher.AddListener<LevelEndedUnfinishedEvent>(OnLevelUnfinishedHandler);

            _inputController = context.Resolve<IInputController>();
            _inputController.OnTileSelected -= OnTapInput;
            _inputController.OnTileSelected += OnTapInput;
            _inputController.OnEndTouchEvent -= OnTapInput;
            _inputController.OnEndTouchEvent += OnTapInput;
            
            
            _screensManager = context.Resolve<IScreensManager>();
            _screensManager.OnScreenChangingStarted -= OnScreenChangeStarted; 
            _screensManager.OnScreenChangingStarted += OnScreenChangeStarted; 

            var config = context.Resolve<IConfig>();
            SetupHintConfig(config);
            Config.OnConfigUpdated -= SetupHintConfig;
            Config.OnConfigUpdated += SetupHintConfig;

            _powerUpHintConditions.Clear();
            _powerUpHintConditions.Add("shovel", new ShovelGoalTargetCondition(2));
            ClearScheduledTimer();
            
            
            _goalsSystem = context.Resolve<GoalsSystem>();
            _m3SpawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            _gameController = context.Resolve<GameController>();
            _tileResourceSelector = context.Resolve<TileResourceSelector>();
        }

        public void ResetDefaults()
        {
            _specialHintTweener = default;
            _highlightDelayTweener = default;
            _level = default;
            _eligibleBoosts = default;
            _config = default;
            _hints = default;
            _currentMove = default;
        }

        private void UserSettingsOnHintsSettingsChanged()
        {
            _globalEnabled = _userSettings.HintsOn;
            RefreshHintsActiveState();
        }

        public void SetupForLevel(ILevel level)
        {
            _level = level;
            _eligibleBoosts = _level.Config.TrueEligibleBoosts();

            _boostersUnlocked.Clear();
            if (_eligibleBoosts != null)
            {
                foreach (var boostId in _eligibleBoosts)
                {
                    if (!_lockManager.IsLocked(boostId, LockItemType.Booster))
                    {
                        _boostersUnlocked.Add(boostId);
                    }
                }
            }
        }

        private void SetupHintConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var configDict = config.Get<HintSystemConfig>();

            if (configDict != null && configDict.TryGetValue("default", out var value))
            {
                _config = value;
            }
            else
            {
                UnityEngine.Debug.LogError("HintConfig is not found");
            }
        }

        public void SetHintsActiveAndEnabled(bool activeAndEnabled)
        {
            SetHintsActive(activeAndEnabled);
            _enabled = activeAndEnabled;
        }
        
        private void OnTapInput(Coords coord)
        {
            ResetAllHints();
        }

        private void ResetPowerUpHint()
        {
            if (_highlightDelayTweener != null)
            {
                _highlightDelayTweener.Kill(complete: true);
                _highlightDelayTweener = null;
            }

            if (_specialHintTweener != null)
            {
                _specialHintTweener.Kill(complete: true);
                _specialHintTweener = null;
            }

            _highlightSystem.Reset();
            _boostButtonsController.StopHint();
        }
        
        private void ResetAllHints()
        {
            ResetPowerUpHint();
            _index = 0;
            if (_currentMove != null) 
            {
                CancelShowPossibleMove(_currentMove.Value);
                _currentMove = null;
            }
            _highlightSystem.ResetHighlightCells();
        }
        
        private void SetHintsActive(bool active)
        {
            if (active)
            {
                _hintTimerDisposable?.Dispose();
                ResetAllHints();

                void ShowPossibleMoveAction(long _)
                {
                    var shouldHighlight = true;
                    var hintsTemp = ShowHint(ref shouldHighlight);
                    if (hintsTemp == null)
                        return;
                    _currentMove = hintsTemp;
                    ShowPossibleMove(_currentMove.Value, shouldHighlight);
                }

                //YA: Adding extra 2f for delay taking fx duration in account
                _hintTimerDisposable = Rx.InvokeRepeating(_config.InitialHintTime, _config.HintTime+AdditionalHintDelay, ShowPossibleMoveAction);
                
                TryLaunchSpecialHint();
            }
            else
            {
                _hintTimerDisposable?.Dispose();
                ResetAllHints();
                if (_specialHintTweener != null)
                {
                    _specialHintTweener.Kill(complete: true);
                    _specialHintTweener = null;
                }

                ResetHighlight();
                ClearScheduledTimer();
            }
        }

        private void TryLaunchSpecialHint()
        {
            HelpingUidsList.Clear();

            if (_eligibleBoosts != null)
            {
                foreach (var eliBoostUid in _eligibleBoosts)
                {
                    if (!_specialHintsDoneThisMatch.Contains(eliBoostUid))
                    {
                        HelpingUidsList.Add(eliBoostUid);  
                    }
                }
            }

            if (HelpingUidsList.Count == 0)
                return;

            var boostUid = HelpingUidsList.GetRandomItem();

            if (!_boostersUnlocked.Contains(boostUid))
                return;

            if (!_specialHintsDoneThisMatch.Contains(boostUid))
            { 
                var time = -1;
                for (var i = 0; i < _config.PowerUpHintTimesFbLength; i++)
                {
                    var powerUpHintTimesFb = _config.PowerUpHintTimesFb(i);
                    
                    if(!powerUpHintTimesFb.HasValue || powerUpHintTimesFb.Value.Key != boostUid)
                        continue;

                    time = powerUpHintTimesFb.Value.Value;
                    break;
                }

                if (time != -1)
                {
                    var moves = -1;

                    for (var i = 0; i < _config.PowerUpHintMovesLeftFbLength; i++)
                    {
                        var powerUpHintMovesLeft = _config.PowerUpHintMovesLeftFb(i);
                        
                        if(!powerUpHintMovesLeft.HasValue || powerUpHintMovesLeft.Value.Key != boostUid)
                            continue;

                        moves = powerUpHintMovesLeft.Value.Value;
                        break;
                    }

                    if (moves != -1)
                    {
                        HintConditionBase condition;
                        if (_gameController.RemainingMoves <= moves && (condition = FindSatisfiedCondition(boostUid)) != null)
                        {
                            _specialHintTweener?.Kill(complete: true);

                            void HighlightAction(long _)
                            {
                                var targetCoords = condition.FindTargetCoords(_gameController.Grid);
                            
                                _highlightSystem.Highlight(targetCoords, HintSystemConstants.PowerUpHintCycleTime, HintSystemConstants.PowerUpHintCyclesCount);

                                _highlightDelayTweener = Rx.Invoke(HintSystemConstants.PowerUpHintCycleTime * HintSystemConstants.PowerUpHintCyclesCount, ResetAction);

                                _boostButtonsController.Hint(boostUid, HintSystemConstants.PowerUpHintCycleTime, HintSystemConstants.PowerUpHintCyclesCount);
                                _specialHintsDoneThisMatch.Add(boostUid);
                                _specialHintTweener = null;
                                return;

                                void ResetAction(long t)
                                {
                                    _highlightSystem?.Reset();
                                    _highlightDelayTweener = null;
                                }
                            }

                            _specialHintTweener = Rx.Invoke(time, HighlightAction);
                        }
                    }
                }
            }

            HelpingUidsList.Clear();
        }

        private void ResetHighlight()
        {
            if (_highlightDelayTweener != null)
            {
                _highlightDelayTweener.Complete();
                _highlightDelayTweener = null;
            }
        }

        private HintConditionBase FindSatisfiedCondition(string boostUid)
        {
            var conditions = _powerUpHintConditions.GetValues(boostUid);

            if (conditions == null) return null;

            foreach (var condition in conditions)
            {
                if (condition.IsSatisfiedBy(_gameController.Grid, _goalSystem))
                    return condition;
            }
            
            return null;
        }
        
        private void OnTilesRevealFinishedHandler(TileRevealFinishedEvent ev)
        {
            RefreshHintsActiveState();
        }

        private void RefreshHintsActiveState()
        {
            if (_config.IsNull() || !_enabled )
                return;
            
            var active = _gameController.RemainingMoves > 0 && !_gameController.GameEnded && _globalEnabled;
            SetHintsActive(active);
        }

        private void OnRemainingMovesChangedHandler(RemainingMovesChanged ev)
        {
            ResetAllHints();
            RefreshHintsActiveState();
        }

        private void OnLevelResultPredictedHandler(LevelResultPredicted ev)
        {
            ResetAllHints();
        }

        private void LevelEndedHandler(LevelEndedEvent obj)
        {
            ResetAllHints();
            ClearScheduledTimer();
        }

        private void OnLevelUnfinishedHandler(LevelEndedUnfinishedEvent obj)
        {
            ResetAllHints();
            ClearScheduledTimer();
        }

        private void OnScreenChangeStarted(ScreenType screenType, IScreensController _)
        {
            ResetAllHints();
            ClearScheduledTimer();
        }

        private void OnBoostButtonPressedHandler(BoostButtonPressedEvent ev)
        {
            ResetHighlight();
        }

        private PossibleMove? ShowHint(ref bool shouldHighlight)
        {
            if (_gameController.IsInputLocked || _specialHintTweener != null || _highlightDelayTweener != null)
                return null;
            
            var hints = ShowAIBestHint(ref shouldHighlight);
            if (hints is not {Length: > 0})
            {
                UnityEngine.Debug.LogWarning("Hint not found");
                return null;
            }
            
            if (_index >= hints.Length)
                _index = 0;
            var move = hints[_index];
            _index++;
            return move;
        }
        
        public void ShowPossibleMove(PossibleMove move, bool shouldHighlight = true, float hintMoveDistance = 0.2f, int loops = 2)
        {
            if(shouldHighlight)
                _highlightSystem.HighlightCells(move.GetAllInvolvedCoords);
            
            if (move.Type == PossibleMoveType.DoubleTap)
            {
                var tileView = _tileController.GetTileViewByCoord(move.FirstCell.Coords);
                tileView.Animator.Hint(new Vector3(), 0, loops);
                return;
            }

            foreach (var coords in move.GetAllInvolvedCoords)
            {
                var tileView = _tileController.GetTileViewByCoord(coords, false);
                if (tileView != null)
                {
                    var dir = Vector2.zero;
                    if (move.HasCoord(coords))
                    {
                        dir = move.GetMoveDirectionForTile(coords).ToVector2();
                    }

                    tileView.Animator.Hint(dir.Multiply(_tilesResources.CellSize), hintMoveDistance, loops);
                }
            }
        }

        private void CancelShowPossibleMove(PossibleMove move)
        {
            foreach (var coords in move.GetAllInvolvedCoords)
            {
                var tileView = _tileController.GetTileViewByCoord(coords, false);
                if (tileView != null)
                {
                    tileView.Animator.KillAnyTransformTweening();
                }
            }
        }

        [CanBeNull]
        private PossibleMove[] ShowAIBestHint(ref bool shouldHighlight)
        {
            var bestMoves = new HashSet<PossibleMove>();
            var state = HintSystemMovePicker.GetBestPossibleMoves(ref bestMoves, ref shouldHighlight, _level, _level.Grid, _goalsSystem, _m3SpawnSystem,
                _gameController, _tileResourceSelector);
    
            if (!state)
            {
                return null;
            }

            var bestMovesArray = new PossibleMove[bestMoves.Count];
            var i = 0;
    
            foreach (var move in bestMoves)
            {
                bestMovesArray[i++] = move;
            }

            return bestMovesArray;
        }
        
        public void ReleaseByContext(IContext context)
        {
            _inputController.OnTileSelected -= OnTapInput;
            _inputController.OnEndTouchEvent -= OnTapInput;
            _userSettings.HintsSettingsChanged -= UserSettingsOnHintsSettingsChanged;
            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<TileRevealFinishedEvent>(OnTilesRevealFinishedHandler);
                _eventDispatcher.RemoveListener<RemainingMovesChanged>(OnRemainingMovesChangedHandler);
                _eventDispatcher.RemoveListener<LevelResultPredicted>(OnLevelResultPredictedHandler);
                _eventDispatcher.RemoveListener<BoostButtonPressedEvent>(OnBoostButtonPressedHandler);
            }
            if (_screensManager != null)
            {
                _screensManager.OnScreenChangingStarted -= OnScreenChangeStarted;
            }

            ClearScheduledTimer();
        }

        private void ClearScheduledTimer()
        {
            if (_hintTimerDisposable != null)
            {
                _hintTimerDisposable.Dispose();
                _hintTimerDisposable = null;
            }
            
            Config.OnConfigUpdated -= SetupHintConfig;
        }
    }
}