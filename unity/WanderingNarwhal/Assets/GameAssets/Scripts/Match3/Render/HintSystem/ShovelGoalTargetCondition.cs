using System.Collections.Generic;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Renderer
{
    public class ShovelGoalTargetCondition : HintConditionBase
    {
        private readonly List<GoalType> _goalTypes;
        private readonly int _goalLeftNumberThreshold;
        
        public ShovelGoalTargetCondition(int goalLeftNumberThreshold)
        {
            _goalTypes = new List<GoalType>(GoalTypeExtensions.GridBasedTypes().Length);
            foreach (var goal in GoalTypeExtensions.GridBasedTypes())
            {
                if (goal != GoalType.DropItems)
                {
                    _goalTypes.Add(goal);
                }
            }
            _goalLeftNumberThreshold = goalLeftNumberThreshold;
        }
        
        public override bool IsSatisfiedBy(Grid grid, GoalsSystem goalSystem)
        {
            bool satisfied = true;
            var totalCount = 0;
            foreach (var goal in _goalTypes)
            {
                var count = goalSystem.GetLeftGoalCount(goal);
                satisfied &= count <= _goalLeftNumberThreshold;
                totalCount += count;
            }

            return totalCount > 0 && satisfied;
        }

        public override IEnumerable<Coords> FindTargetCoords(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                var c = cell.GetMainCellReference(out _);

                if (c.IvyCount > 0)
                    yield return cell.Coords;
                
                if (c.BackgroundCount > 0)
                    yield return cell.Coords;

                var tile = c.Tile;

                if (ReferenceEquals(tile, null))
                    continue;

                if (tile.IsAnyOf(TileState.AnimalMod | TileState.IceCubeMod | TileState.MagicHatMod |
                                 TileState.FlowerPotMod | TileState.BowlingMod | TileState.SodaMod | TileState.SafeMod |
                                 TileState.BushMod | TileState.SquidMod | TileState.ToadMod | TileState.DynamiteBoxMod |
                                 TileState.ShelfMod | TileState.JellyFishMod | TileState.GoldenScarabMod |
                                 TileState.GondolaMod | TileState.TukTukMod | TileState.FireWorksMod |
                                 TileState.SlotMachineMod))
                    yield return cell.Coords;

                switch (tile.Speciality)
                {
                    case TileSpeciality.Sticker:
                    case TileSpeciality.Litter:
                    case TileSpeciality.Pinata:
                    case TileSpeciality.Frame:
                        yield return cell.Coords;
                        break;
                }
            }
        }
    }
}