using BBB.DI;
using BBB.Match3.Renderer;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Level
{
    public interface ILevelSkipper
    {
        void TriggerTap();
        void ShowSkipText();
        void ForbidToSkip();
    }

    public class LevelSkipper : BbbMonoBehaviour, IContextInitializable, ILevelSkipper
    {
        private static readonly int Tap = Animator.StringToHash("Tap");
        private const float KeepShownDuration = 4f;

        [SerializeField] private Button _showSkipButton;
        [SerializeField] private GameObject _skipTitle;
        [SerializeField] private Animator _animator;

        private IForceWinLosable _levelController;
        private GameController _gameController;

        private float _showTimer = -1f;
        private bool _skipBlocked;
        private LocalizedTextPro _skipTitleText;
        private TextMeshProUGUI _skipTitleTmp;

        private FxRenderer _fxRenderer;
        private IGridController _gridController;

        private void Awake()
        {
            _skipTitleText = _skipTitle.GetComponent<LocalizedTextPro>();
            _skipTitleTmp = _skipTitle.GetComponent<TextMeshProUGUI>();
        }

        void IContextInitializable.InitializeByContext(IContext context)
        {
            _showTimer = -1f;
            _fxRenderer = context.Resolve<FxRenderer>();
            _gridController = context.Resolve<IGridController>();
            _gameController = context.Resolve<GameController>();

            // passing OnSkipButton we bypass showing "tap to skip" text + one extra click
            _showSkipButton.ReplaceOnClick(OnSkipButton);
            _skipTitle.SetActive(false); 
        }

        public void ResetDefaults()
        {
            _skipBlocked = default;
        }

        public void Init(LevelController levelController)
        {
            _levelController = levelController;
            ForbidToSkipInternal();
            enabled = false;
        }

        void ILevelSkipper.ForbidToSkip()
        {
            ForbidToSkipInternal();
        }

        private void ForbidToSkipInternal()
        {
            _showSkipButton.targetGraphic.raycastTarget = false;
            _skipTitle.SetActive(false);
            _showTimer = -1f;
        }

        public void BlockSkippingWithLocalizedText(string locKey)
        {
            if (!_skipBlocked)
            {
                _skipTitle.SetActive(false); // it's intentionally hidden, will be shown on tap only 
                _skipTitleText.SetTextId(locKey);
                _skipBlocked = true;
            }
        }

        public void UnblockSkipping()
        {
            if (_skipBlocked)
            {
                _skipTitle.SetActive(false);
                _skipBlocked = false;
            }
        }

        private void OnShownButton()
        {
            if (!_gameController.GameEnded || !_gameController.HasWon)
            {
                return;
            }

            _showSkipButton.ReplaceOnClick(OnSkipButton);
            // var lowestWorldPosition = _gridController.GetLowestPosition();
            // var cachedPosition = _skipTitle.transform.position;
            // cachedPosition.y = lowestWorldPosition.y;
            // _skipTitle.transform.position = cachedPosition;
            _skipTitle.SetActive(true);
            _showTimer = KeepShownDuration;
            enabled = true;
        }

        private void Update()
        {
            if (_showTimer > 0f)
            {
                _showTimer -= Time.deltaTime;

                if (_showTimer <= 0f)
                {
                    _showSkipButton.ReplaceOnClick(OnShownButton);
                    _skipTitle.SetActive(false);

                    _showTimer = -1f;
                }
            }
        }

        private void OnSkipButton()
        {
            if (_skipBlocked)
            {
                _skipTitle.SetActive(true);
                return;
            }

            _showSkipButton.targetGraphic.raycastTarget = false;
            _skipTitle.SetActive(false);
            _showTimer = -1f;

            _gridController.OnSkip();
            _fxRenderer.Complete();

            _levelController.ForceWin();

            ForbidToSkipInternal();
        }

        public void TriggerTap()
        {
            _animator.SetTrigger(Tap);
        }

        public void ShowSkipText()
        {
            _skipTitle.SetActive(true);

            _skipTitleTmp.alpha = 0;
            DOTween.ToAlpha(() => _skipTitleTmp.color, x => _skipTitleTmp.color = x, 1, 0.33f);
        }
    }
}