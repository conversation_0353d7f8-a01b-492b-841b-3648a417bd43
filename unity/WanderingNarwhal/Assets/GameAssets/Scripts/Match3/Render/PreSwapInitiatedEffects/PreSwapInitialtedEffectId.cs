namespace BBB.Match3.Renderer
{
    public class PreSwapInitialtedEffectId
    {
        private readonly Coords _first;
        private readonly Coords _second;

        public PreSwapInitialtedEffectId(Coords first, Coords second)
        {
            _first = first;
            _second = second;
        }

        public override bool Equals(object obj)
        {
            return obj is PreSwapInitialtedEffectId other && other._first.Equals(_first) &&
                   other._second.Equals(_second);
        }

        public override int GetHashCode()
        {
            return _first.GetHashCode() + 7 * _second.GetHashCode();
        }
    }
}