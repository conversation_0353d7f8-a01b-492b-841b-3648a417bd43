using System.Collections;
using BebopBee.UnityEngineExtensions;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public sealed partial class TileAnimator : BbbMonoBehaviour
    {
        private const float HintMoveTime = 0.43f;
        private const Ease HintMoveEase = Ease.InOutCubic;

        private TileView _view;
        private AnimationProcess _process;
        private Animator _animator;

        private Tween _yoyoTween;
        private Tweener _tweener;

        public void Init()
        {
            _view = GetComponent<TileView>();
            _animator = GetComponent<Animator>();
            _process = new AnimationProcess(_animator);
        }

        public void OnRelease()
        {
            _animator.speed = 1f;
            KillAnyTransformTweening();
        }

        public bool IsAnyPlaying(StateType stateType)
        {
            return _process.IsAnyPlaying(stateType);
        }

        public IEnumerator AnimateDestroy(Coords coords)
        {
            if(_view.CanTileHaveAnimatedReactions() && _view.CanTileHaveDestroyAnimation())
            {
                
                _process.Execute(StateType.Destroy);

                yield return WaitCache.Seconds(0.2f);
                
                _process.Finish(StateType.Destroy);
            }
        }

        [ContextMenu("FallGridAnim")]
        private void DebugRunGridFall()
        {
            AnimateFallGridOnLevelLose(new GridFallAnimSettings(), true);
        }

        public void AnimateFallGridOnLevelLose(GridFallAnimSettings settings, bool returnBack)
        {
            _animator.enabled = false;

            float posX = Random.Range(settings.MinX, settings.MaxX);
            float posY = settings.EndY;
            var duration = settings.Duration;
            var easeX = settings.EaseX;
            var easeY = settings.EaseY;
            var overshootX = settings.OvershootX;
            var overshootY = Random.Range(settings.OvershootYMin, settings.OvershootYMax);
            var rotation = Random.Range(settings.RotationMin, settings.RotationMax);
            _view.LocalAnchor.DOLocalMoveX(posX, duration, false).SetEase(easeX, overshootX);
            _view.LocalAnchor.DOLocalMoveY(posY, duration, false).SetEase(easeY, overshootY);
            var tween = _view.LocalAnchor.DOLocalRotate(new Vector3(0, 0, rotation), duration);
            if (returnBack)
            {
                // For debug.
                void ReturnBackAction()
                {
                    _animator.enabled = true;
                    _view.LocalAnchor.anchoredPosition = Vector3.zero;
                }

                tween.OnComplete(ReturnBackAction);
            }
        }

        public void Fall()
        {
            _process.Execute(StateType.Fall);
        }

        public void ShortSwipe(Vector3 moveDelta, float duration)
        {
            DOTween.Kill(_view.LocalAnchor, complete: true);
            _tweener?.Kill(true);
            _animator.enabled = false;

            void ResetAction()
            {
                _view.LocalAnchor.anchoredPosition = Vector3.zero;
                _animator.enabled = true;
                _tweener = null;
            }

            const float moveDistance = 0.1f; //10%
            _tweener = _view.LocalAnchor.DOLocalMove(moveDelta * moveDistance, duration)
                .SetRelative()
                .SetEase(Ease.InOutQuad).SetLoops(2, LoopType.Yoyo)
                .OnKill(ResetAction)
                .OnComplete(ResetAction);
        }

        public void Hint(Vector3 moveDelta, float hintMoveDistance, int loops)
        {
            if (moveDelta != Vector3.zero)
            {
                DOTween.Kill(_view.LocalAnchor, complete: true);
                _tweener?.Kill(true);
                _animator.enabled = false;

                void ResetAction()
                {
                    _view.LocalAnchor.anchoredPosition = Vector3.zero;
                    _animator.enabled = true;
                    _tweener = null;
                }

                _tweener = _view.LocalAnchor.DOLocalMove(moveDelta * hintMoveDistance, HintMoveTime)
                    .SetRelative()
                    .SetEase(HintMoveEase).SetLoops(2 * loops, LoopType.Yoyo)
                    .OnKill(ResetAction)
                    .OnComplete(ResetAction);
            }
            else
            {
                _process.Execute(StateType.Hint);
            }
        }

        public void SetAnimatorEnabled(bool value)
        {
            _animator.enabled = value;
        }

        public void StartYoyo(float scaleMultiplier, float cycleTime, int cyclesCount)
        {
            DOTween.Kill(_view.LocalAnchor, complete: true);
            _animator.enabled = false;
            var currentScale = _view.LocalAnchor.localScale;
            var sequence = DOTween.Sequence();
            var newScale = currentScale * scaleMultiplier;
            sequence.Append(_view.LocalAnchor.DOScale(newScale, cycleTime * 0.5f).SetEase(Ease.InOutQuad));
            sequence.Append(_view.LocalAnchor.DOScale(currentScale, cycleTime * 0.5f).SetEase(Ease.InOutQuad));
            sequence.SetLoops(cyclesCount);

            void ResetAction()
            {
                _view.LocalAnchor.localScale = currentScale;
                _animator.enabled = true;
                _yoyoTween = null;
            }

            sequence.OnKill(ResetAction)
                .OnComplete(ResetAction);

            _yoyoTween = sequence;
        }

        public static Sequence PlayFeedbackOnTileView(Transform tile, float duration, float scaleMultiplier)
        {
            if (DOTween.IsTweening(tile)) return null;
            if (duration <= 0) return null;
            var currentScale = tile.localScale;
            var sequence = DOTween.Sequence();
            var newScale = currentScale * scaleMultiplier;
            sequence.Append(tile.DOScale(newScale, duration * 0.5f).SetEase(Ease.InOutQuad));
            sequence.Append(tile.DOScale(currentScale, duration * 0.5f).SetEase(Ease.InOutQuad));
            return sequence;
        }

        public void StopYoyo()
        {
            if (_yoyoTween != null)
            {
                _yoyoTween.Kill(complete: true);
            }
        }

        public void Settle()
        {
            _animator.speed = 1f;
            _process.Execute(StateType.Settle);
        }

        public void Shake()
        {
            _animator.speed = 1f;
            _process.Execute(StateType.Shaking);
        }

        public void StopShaking()
        {
            _process.Finish(StateType.Shaking);
            _process.Execute(StateType.Settle);
        }


        public void StartShockwaveEffect(CardinalDirections fromDirection)
        {
            if (!_view.CanTileHaveAnimatedReactions())
            {
                 return;
            }

            _process.Execute(StateType.Shockwave, fromDirection);
        }

        public void StartSubtleShockwaveEffect()
        {
            if (!_view.CanTileHaveAnimatedReactions())
            {
                return;
            }

            _process.Execute(StateType.SubtleShockwave);
        }

        private void OnDestroyFinished()
        {
        }

        // Animation events callback
        private void OnHintFinished()
        {
            _process.Finish(StateType.Hint);
        }

        private void OnShockWaveFinished()
        {
            _process.Finish(StateType.Shockwave);
        }

        private void OnSubtleShockWaveFinished()
        {
            _process.Finish(StateType.SubtleShockwave);
        }

        private void OnSettleFinished()
        {
            _process.Finish(StateType.Settle);
        }

        public void KillAnyTransformTweening()
        {
            _yoyoTween?.Kill(true);
            _tweener?.Kill();
            if(DOTween.IsTweening(_view.LocalAnchor))
                DOTween.Kill(_view.LocalAnchor, complete: true);
            
            _animator.ResetAllParameters();
            _animator.PlayMain();
            _process.Release();
        }

    }
}
