using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Systems.AssistSystem;

namespace BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes
{
    public class AssistInputBase
    {
    }

    public sealed class AssistInputSpecialLuckDefine : AssistInputBase
    {
        public AssistMode Stage { get; }

        public AssistInputSpecialLuckDefine(AssistMode stage)
        {
            Stage = stage;
        }
    }

    public sealed class AssistInputAutomatches : AssistInputBase
    {
        public int Limit { get; private set; }
        public Dictionary<GoalType, float> GoalValueWeights { get; }
        

        public AssistInputAutomatches(int limit, Dictionary<GoalType, float> goalValueWeights)
        {
            Limit = limit;
            GoalValueWeights = goalValueWeights;
        }

        public void DecreaseLimit(int value)
        {
            Limit-= value;

            if (Limit < 0)
                Limit = 0;
        }

        public override string ToString()
        {
            return "AssistInputAutomatches";
        }
    }
    
    public enum AssistInput
    { 
        TileKindAim = 1,
        Automatches = 2,
        SpecialLuckDefine = 3
    }
}
