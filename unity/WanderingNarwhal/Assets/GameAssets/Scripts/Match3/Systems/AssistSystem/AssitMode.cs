namespace GameAssets.Scripts.Match3.Systems.AssistSystem
{
    public enum AssistMode
    {
        None = 0,
        Balancing = 1, //change the assist or resist based on assist value
        Slingshot = 2, //add a lot of assisting to help player reach final stage easier
        Hard = 3, //remove all assist and reduce luck to min possible
        FinalLucky = 4, //same as FinalHard, but if there is a chance to spawn match5 move, do it.
        AMarkerFocus = 5,
        InitialLucky = 6,
        RestFromHard = 7 //when hard mode is enabled sometimes it is changed to rest from hard
                         //to give player chance to get out from difficult state
    }
}