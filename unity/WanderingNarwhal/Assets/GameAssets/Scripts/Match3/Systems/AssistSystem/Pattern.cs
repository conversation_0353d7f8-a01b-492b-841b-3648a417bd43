using System;

namespace GameAssets.Scripts.Match3.Systems.AssistSystem
{
    [Flags]
    public enum Pattern
    {
        None = 0,
        BoltVer = 1,
        BoltHor = 2,
            
        Bolt = BoltVer | BoltHor,
        Ver = BoltVer,
        Hor = BoltHor
    }

    public static class PatternExtensions
    {
        public static bool IsVer(this Pattern pattern)
        {
            return (pattern & Pattern.Ver) != 0;
        }
    }
}