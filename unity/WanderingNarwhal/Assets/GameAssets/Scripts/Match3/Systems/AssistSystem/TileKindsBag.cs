using System;
using System.Collections.Generic;
using System.Text;
using BBB.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes
{
    public class TileKindsBag
    {
        private readonly List<TileKinds> _usedTileKinds = new (10);
        private readonly List<TileKinds> _bag = new (10);

        public int Count => _bag.Count;

        public TileKinds this[int index] => _bag[index];

        public void SetupUsed(List<TileKinds> usedTileKinds)
        {
            _usedTileKinds.Clear();
            _usedTileKinds.AddRange(usedTileKinds);
            _bag.Clear();
            _bag.AddRange(usedTileKinds);
        }

        public void Reset()
        {
            _bag.Clear();
            _bag.AddRange(_usedTileKinds);
        }

        public void Remove(TileKinds kind)
        {
            _bag.Remove(kind);
        }
        
        public TileKinds GetRandomKind()
        {
            if (_bag.Count == 1)
                return _bag[0];
            
            return _bag.Count > 0 ? _bag.DeterministicRandomInSelf() : _usedTileKinds.DeterministicRandomInSelf();
        }

        /// <summary>
        /// Returns a tile kind after calculating the density and constructing the weight table based on that. The weight
        /// table would assign more weight (a.k.a probability) to tiles that are not present (or less present) and less
        /// weight to tiles with greater presence
        /// </summary>
        /// <param name="density"></param>
        /// <returns></returns>
        public TileKinds GetRandomKindByDensity(Dictionary<TileKinds, int> density)
        {
            if (_bag.Count == 1)
                return _bag[0];
            
            if (density.Count == 0)
                return GetRandomKind();

            var maxValue = int.MinValue;

            foreach (var entry in density)
            {
                if (entry.Value > maxValue)
                {
                    maxValue = entry.Value;
                }
            }
            
            Span<float> inverseWeights = stackalloc float[_bag.Count];
            for (var i = 0; i < _bag.Count; ++i)
            {
                if (density.TryGetValue(_bag[i], out var weight))
                {
                    inverseWeights[i] = maxValue - weight + 1f;//+1, otherwise weight will be 0 for max kind and it will never participate in calculation
                }
                else
                {
                    inverseWeights[i] = maxValue + 1f;
                }
            }

            return WeightedRandomKind(inverseWeights);
        }
        
        public TileKinds GetWeightedRandomKind(Dictionary<TileKinds, float> weights)
        {
            if (_bag.Count == 1)
                return _bag[0];
            
            if (weights == null || weights.Count == 0)
            {
                return _bag.Count > 0 ? _bag.DeterministicRandomInSelf() : _usedTileKinds.DeterministicRandomInSelf();
            }

            return WeightedRandomKind(weights);
        }

        private TileKinds WeightedRandomKind(Dictionary<TileKinds, float> kindWeights)
        {
            if (kindWeights.Count == 0 || _bag.Count == 0)
                return TileKinds.Error;

            if (_bag.Count == 1)
                return _bag[0];
            
            Span<float> weights = stackalloc float[_bag.Count];

            for (var i = 0; i < _bag.Count; ++i)
            {
                weights[i] = kindWeights.GetValueOrDefault(_bag[i], 0f); //skip from calculation
            }

            return WeightedRandomKind(weights);
        }
        
        private TileKinds WeightedRandomKind(Span<float> weights)
        {
            switch (_bag.Count)
            {
                case 0:
                    return TileKinds.Error;
                case 1:
                    return _bag[0];
            }

            var sum = 0f;
            foreach (var weight in weights)
            {
                sum += weight;
            }

            var index = 0;
            var roll = RandomSystem.Next(sum);

            var accumWeight = 0f;
            for (var i = 0; i < weights.Length; ++i)
            {
                accumWeight += weights[i];
                
                if (!(roll <= accumWeight)) continue;
                
                index = i;
                break;
            }

            return _bag[index];
        }
        
        public bool IsEmpty()
        {
            return _bag.Count == 0;
        }

        public override string ToString()
        {
            var result = new StringBuilder(string.Empty).Append("Bag: ");
            const string space = " ";
            
            foreach (var kind in _bag)
            {
                result.Append(kind).Append(space);
            }

            result.Append("Used: ");

            foreach (var kind in _usedTileKinds)
            {
                result.Append(kind).Append(space); 
            }
            
            return result.ToString();
        }
    }
}