using System;
using System.Collections.Generic;
using BebopBee;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class CalculateSpecialTilesSystem
    {
        public static void Uninit()
        {
        }

        public static void Clear()
        {
        }

        private static readonly HashSet<Match> IntersectingNonBoltMatches = new HashSet<Match>();
        private static readonly HashSet<Match> MatchesIncludedToOtherMatches = new HashSet<Match>();
        private static readonly List<Match> TempMatchesList = new List<Match>(10);
        private static readonly List<Match> MatchesToIncludeToSquareMatch = new List<Match>(2);
        private static readonly HashSet<Match> IntersectionBombMatches = new HashSet<Match>();

        /// <summary>
        /// This method is used to calculate special spawns made out of other tile combinations
        /// line breakers, bombs and bolts
        /// </summary>
        /// <param name="explicitHelpSpawns">this flag determines if explicit help spawns are enabled
        /// the other name for them is "supermatches". If it is true, then match3 gonna spawn bomb and match4 gonna spawn bolt
        /// there is a limit of supermatches per one playout counted by _boltCounter and _bombCounter and coming from remote config</param>
        public static void CalculateSpecialTiles(SpecialTileCalculationResult resultToFill, 
            Grid grid,
            ICollection<Match> matchesOriginal,
            List<Coords> preferredCoords,
            bool needsVisualizedFormation, FixedSizeQueue<int> lastSettledTileIdsQueue)
        {
            if (matchesOriginal == null || matchesOriginal.Count == 0) 
                return;

            lock (TempMatchesList)
            {
                IntersectionBombMatches.Clear();
                TempMatchesList.Clear();
                MatchesIncludedToOtherMatches.Clear();
                IntersectingNonBoltMatches.Clear();
                TempMatchesList.AddRange(matchesOriginal);

                foreach (var matchA in TempMatchesList)
                {
                    if (matchA.Length == 5 || matchA.MatchType == MatchType.Square)
                        continue;
                    
                    foreach (var matchB in TempMatchesList)
                    {
                        if (matchB.Length == 5 || matchB.MatchType == MatchType.Square)
                            continue;
                        
                        if (matchA.Equals(matchB))
                            continue;

                        if (matchA.AnyOverlap(matchB))
                        {
                            IntersectingNonBoltMatches.Add(matchA);
                            IntersectingNonBoltMatches.Add(matchB);
                        }
                    }
                }
                
                TempMatchesList.Sort((m1, m2) =>
                {
                    var m1Priority = IntersectingNonBoltMatches.Contains(m1) ? 4.5f : m1.DefaultPriority;
                    var m2Priority = IntersectingNonBoltMatches.Contains(m2) ? 4.5f : m2.DefaultPriority;
                    
                    return m1Priority.CompareTo(m2Priority);
                });

                for (var i = TempMatchesList.Count - 1; i >= 0; i--)
                {
                    var match = TempMatchesList[i];

                    var hasLineBreaker = false;
                    var hasBomb = false;
                    
                    TempMatchesList.RemoveAt(i);
                    Tile tile = null;
                    var spawnCoord = match.GetSpawnCoord(preferredCoords, lastSettledTileIdsQueue, grid);

                    var tileKind = match.Kind;

                    if (match.Length >= 5)
                    {
                        // If any other match is overlapping with this one, add it to the list in order to prevent other
                        // possible matches to be created
                        foreach (var otherMatch in TempMatchesList)
                        {
                            if (otherMatch.Kind != tileKind)
                                continue;

                            if (!match.EdgeIntersect(otherMatch, out var intersectionCoords))
                            {
                                continue;
                            }

                            MatchesIncludedToOtherMatches.Add(otherMatch);
                        }
                        
                        // ColorBomb:
                        tile = resultToFill.GetOrCreateSpecialTile(grid, spawnCoord, 
                            TileSpeciality.ColorBomb, match);
                    }

                    if (!MatchesIncludedToOtherMatches.Contains(match) && match.Length == 4)
                    {
                        if (match.MatchType == MatchType.Square)
                        {
                            MatchesToIncludeToSquareMatch.Clear();
                            
                            var bombMatchIntersectWithSquare = false;
                            foreach (var coords in match.GetAllCoords())
                            {
                                foreach (var intersectionMatch in IntersectionBombMatches)
                                {
                                    if (intersectionMatch.Contains(coords))
                                    {
                                        bombMatchIntersectWithSquare = true;
                                        break;
                                    }
                                }
                                    
                                if (bombMatchIntersectWithSquare) break;
                            }
                            
                            // If this Square is contained into a Bomb or Bolt match that was already processed, then this
                            // Propeller should not be created
                            if (bombMatchIntersectWithSquare) continue;

                            foreach (var otherMatch in TempMatchesList)
                            {
                                if (otherMatch.Kind != tileKind || otherMatch.MatchType == MatchType.Square)
                                    continue;

                                if (MatchesIncludedToOtherMatches.Contains(otherMatch))
                                    continue;

                                if (match.AnyOverlap(otherMatch))
                                {
                                    MatchesIncludedToOtherMatches.Add(otherMatch);
                                    MatchesToIncludeToSquareMatch.Add(otherMatch);
                                }
                            }

                            MatchesToIncludeToSquareMatch.Add(match);
                            tile = resultToFill.GetOrCreateSpecialTile(grid, spawnCoord, 
                                TileSpeciality.Propeller, MatchesToIncludeToSquareMatch.ToArray());
                            MatchesToIncludeToSquareMatch.Clear();
                        }
                        else
                        {
                            hasLineBreaker = true;
                        }
                    }

                    if (match.Length >= 3)
                    {
                        if (!MatchesIncludedToOtherMatches.Contains(match) &&
                            match.Length < 5 && 
                            match.MatchType != MatchType.Square)
                        {
                            var intersectionFound = false;
                            // Bomb:z
                            foreach (var otherMatch in TempMatchesList)
                            {
                                if (otherMatch.Kind != tileKind)
                                    continue;

                                if (otherMatch.MatchType == MatchType.Square)
                                {
                                    continue;
                                }
                                
                                if (!match.EdgeIntersect(otherMatch, out var intersectionCoords))
                                {
                                    continue;
                                }
                                
                                if (MatchesIncludedToOtherMatches.Contains(otherMatch))
                                    continue;

                                intersectionFound = true;

                                MatchesIncludedToOtherMatches.Add(otherMatch);
                                
                                // Add both matches coordinates to intersectionMatches list, so that we can evaluate
                                // afterward if a Square match overlaps with it (in which case, it should not create
                                // a propeller
                                IntersectionBombMatches.Add(match);
                                IntersectionBombMatches.Add(otherMatch);

                                tile = resultToFill.GetOrCreateSpecialTile(grid, intersectionCoords, 
                                    TileSpeciality.Bomb ,match, otherMatch);
                                resultToFill.AddCornerMatch(match);
                            }

                            hasBomb = intersectionFound;
                        }
                    }

                    if (hasLineBreaker && !hasBomb)
                    {
                        var simplifiedDirection = match.GetSimplifiedDirections();
                        switch (match.GetSimplifiedDirections())
                        {
                            case SimplifiedDirections.Horizontal:
                                tile = resultToFill.GetOrCreateSpecialTile(grid, spawnCoord, 
                                    TileSpeciality.ColumnBreaker, match);
                                break;

                            case SimplifiedDirections.Vertical:
                                tile = resultToFill.GetOrCreateSpecialTile(grid, spawnCoord, 
                                    TileSpeciality.RowBreaker, match);
                                break;

                            default:
                                throw new ArgumentOutOfRangeException($"direction {simplifiedDirection} is not supported");
                        }
                    }

                    bool isVisualizedFormation = needsVisualizedFormation;
                    resultToFill.AddTileToSpawn(tile, isVisualizedFormation);
                }
                
                TempMatchesList.Clear();
                MatchesIncludedToOtherMatches.Clear();
            }
        }
    }
}