using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class CellSpawnHandler
    {
        public static void HandleSpawnSquareTile(GoalsSystem goalsSystem, Grid grid, IRootSimulationHandler events,
            Coords from, Tile tile, HitWaitParams hitWaitParams)
        {
            var cellsToSpawn = GetSpawnSquareData(grid, from, tile,
                out var cellState, out var goalType, out var fxType);

            foreach (var variable in cellsToSpawn)
            {
                if (!grid.TryGetCell(variable.coords, out var cell))
                {
                    continue;
                }

                cell.BackgroundCount = 1;
                cell.Add(cellState);
                goalsSystem.TryAddGoalIfNeeded(goalType);
            }

            cellsToSpawn.Sort((cell1, cell2) => cell1.position.CompareTo(cell2.position));

            var sortedList = new List<Coords>(cellsToSpawn.Count);

            foreach (var cell in cellsToSpawn)
            {
                sortedList.Add(cell.coords);
            }

            events.AddAction(new ActionSpawnInCells(from, sortedList, cellState, goalType, fxType, hitWaitParams));
        }

        private static List<(Coords coords, int position)> GetSpawnSquareData(Grid grid, Coords fromCoords,
            Tile tile, out CellState cellState, out GoalType goalType, out FxType fxType)
        {
            var spawnSuitableList = new List<(Coords coords, int position)>();

            var (tempCellState, tempGoalType, tempFxType, positionList,
                expectedPositionCount, minOffset, maxOffset) = tile.GetSpawnConfiguration();

            cellState = tempCellState;
            goalType = tempGoalType;
            fxType = tempFxType;

            if (cellState == CellState.None || goalType == GoalType.None || positionList == null ||
                expectedPositionCount == 0)
            {
                return spawnSuitableList;
            }

            var min = new Coords(fromCoords.X + minOffset.X, fromCoords.Y + minOffset.Y);
            var max = new Coords(fromCoords.X + maxOffset.X, fromCoords.Y + maxOffset.Y);

            if (positionList.Length != expectedPositionCount)
            {
                positionList = new int[expectedPositionCount];

                for (var i = 0; i < expectedPositionCount; i++)
                {
                    positionList[i] = i;
                }
            }

            var positionIndex = 0;

            for (var x = min.X; x <= max.X; x++)
            for (var y = min.Y; y <= max.Y; y++)
            {
                var coords = new Coords(x, y);

                var index = positionList[positionIndex++];

                if (!grid.TryGetCell(coords, out var cell))
                {
                    continue;
                }

                if (IsCellSuitableForSpawn(cell))
                {
                    spawnSuitableList.Add((coords, index));
                }
            }

            return spawnSuitableList;
        }

        private static bool IsCellSuitableForSpawn(Cell cell)
        {
            if (cell.IsAnyOf(CellState.BackOne | CellState.BackDouble | CellState.Petal | CellState.Water |
                             CellState.FlagEnd))
            {
                return false;
            }

            cell = cell.GetMainCellReference(out _);

            if (cell.Tile.IsNull())
            {
                return true;
            }

            return !cell.Tile.DisallowCellBackgroundSpawn();
        }
    }
}