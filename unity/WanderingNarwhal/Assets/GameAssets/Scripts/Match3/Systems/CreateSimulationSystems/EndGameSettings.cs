using System.Collections.Generic;
using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class EndGameSettings
    {
        public static float FinalTextPause = 1.2f;
        public static float BonusAnimPreDelay = 0.6f;
        public static float BonusTimeTitlePause = 1.6f;
        public static float StarWaveEffectTotalTime = 0.3f;
        public static float DelayBetweenCircleAndStars = 0.03f;
        public static float DelayAfterStars = 0.01f;
        public static readonly HashSet<TileAsset> EndGameConversionTypes = new() { TileAsset.ColumnBreaker, TileAsset.RowBreaker };
    }
}