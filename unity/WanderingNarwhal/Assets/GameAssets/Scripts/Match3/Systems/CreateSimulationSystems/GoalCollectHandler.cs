using System.Collections.Generic;
using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;

public class GoalCollectHandler
{
    private static List<GoalTypeTagPair> _tempGoals = new(5);

    public static void OnAfterHandleStepEnd(Grid grid, IRootSimulationHandler events,
        TileHitReactionHandler reactionHandler, GoalsSystem goalSystem)
    {
        if ((TileSpawnHandler.SpawnedTilesOnCurrentStep & TileState.HiveMod) != 0)
        {
            HiveTile.MarkHivesOnGridOutOfBeesIfNeeded(grid, events, goalSystem, reactionHandler);
        }

        TileSpawnHandler.SpawnedTilesOnCurrentStep = TileState.None;
    }

    public static void HandleCollectGoal(Grid grid, GoalsSystem goalSystem, PopSystem popSystem,
        TileHitReactionHandler reactionHandler, SimulationInputParams inputParams, IRootSimulationHandler events,
        HitWaitParams hitWaitParams, Coords coords, int tileId, TileSpeciality speciality, TileState state,
        TileKinds kind, int? skin, Coords coordsOffset = default)
    {
        _tempGoals.Clear();
        goalSystem.ReduceGoalsOnTileDestroyIfNeeded(speciality, state, kind, ref _tempGoals);
        var isGoalCollected = false;
        foreach (var affectedGoal in _tempGoals)
        {
            if (affectedGoal.GoalType == GoalType.None) continue;

            isGoalCollected = true;
            events.AddAction(new ActionCollectGoal(affectedGoal, coords, tileId, skin, coordsOffset, hitWaitParams,
                kind));
        }

        if (!isGoalCollected) return;

        if (ShouldTileSkipDestroyAnimOnGoalCollect(speciality, hitWaitParams?.DamageSource ?? DamageSource.None))
        {
            goalSystem.AddTileSkipDestroyAnim(tileId);
        }

        MagicHatTile.MarkMagicHatsOnGridOutOfRabbitsIfNeeded(grid, events, reactionHandler, goalSystem);
        ToadTile.MarkToadsOnGridOutOfFliesIfNeeded(grid, events, goalSystem, popSystem, reactionHandler, inputParams);
    }

    private static bool ShouldTileSkipDestroyAnimOnGoalCollect(TileSpeciality speciality, DamageSource damageSource)
    {
        return speciality is TileSpeciality.DropItem or TileSpeciality.Propeller || damageSource == DamageSource.PropellerCombo;
    }
}