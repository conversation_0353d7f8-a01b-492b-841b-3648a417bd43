using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class CellLayerAnalyzer
    {
        /// <summary>
        /// Returns generalized layers of the cell derived from cell and tile states
        /// The order of layers is bottom to top from the perspective of damagability
        /// </summary>
        /// <param name="cell">Some cell</param>
        /// <returns>Enumerable of enum values of generalized layers</returns>
        public static IEnumerable<string> GetGeneralizedLayers(this Cell cell)
        {
            var mainCell = cell.GetMainCellReference(out _, isCellOverlay: false);

            foreach (var assistState in cell.GetGeneralizedLayersInternal())
            {
                yield return assistState;
            }

            if (mainCell.HasTile())
            {
                foreach (var assistState in mainCell.Tile.GetGeneralizedLayers())
                {
                    yield return assistState;
                }

                foreach (var assistState in mainCell.Tile.GetGeneralizedLayersModState())
                {
                    yield return assistState;
                }
            }

            var mainCellOverlay = cell.GetMainCellReference(out _, isCellOverlay: true);

            foreach (var assistState in mainCellOverlay.GetGeneralizedLayersOverlay())
            {
                yield return assistState;
            }
        }
    }
}