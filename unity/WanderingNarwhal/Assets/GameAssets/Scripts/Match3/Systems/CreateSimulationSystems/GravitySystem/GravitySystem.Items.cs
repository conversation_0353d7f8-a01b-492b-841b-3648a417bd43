using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class GravitySystem
    {
        private static readonly HashSet<Cell> CellsRainTemp = new HashSet<Cell>();
        private static readonly List<Cell> CellsBaloonTemp = new List<Cell>();
        private static int boosterIndex;

        private void AppendCellHit(Cell cell, DamageSource damageSource, int busyTime, int delay = 0)
        {
            var hit = new Hit<Cell>(
                immuneTiles:null,
                target: cell,
                busyWait: busyTime,
                damageSource: damageSource,
                coords: cell.Coords);
            Queue.AppendHit(delay, hit);
        }

        private bool UseShovel(Grid grid, PlayerInputItemShovel playerInputItemShovel)
        {
            var coords = playerInputItemShovel.TargetCoord;
            Cell cell;
            if (!grid.TryGetCell(coords, out cell)) return false;

            AddAction(new ActionItemShovel(coords));
            AppendCellHit(cell, damageSource: DamageSource.UsableBoost, busyTime: 1);
            IncrementCurrentTurn();
            return true;
        }

        private bool UseCreateLineBreaker(Grid grid, PlayerInputItemSingleCellWithDirections playerInputItem)
        {
            var coords = playerInputItem.TargetCoord;
            if (!grid.TryGetCell(coords, out var cell)) return false;
            var tile = cell.Tile;
            var direction = playerInputItem.Direction;
            if (ReferenceEquals(tile, null) || !tile.CheckApplicability(playerInputItem.Item, cell, grid)) return false;
            var tileAsset = direction == SimplifiedDirections.Horizontal
                ? TileAsset.RowBreaker
                : TileAsset.ColumnBreaker;
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, tileAsset, new TileOrigin(Creator.Item, cell));
            
            _reactionHandler.RegisterTileShouldReactOnDie(grid, this, null, cell.Coords, 
                tile.Id, tile.Asset, tile.Speciality, tile.State, tile.Kind, tile);

            cell.ReplaceTile(newTile);
            AddAction(new ActionReplace(coords, newTile, GoalType.None, false));

            IncrementCurrentTurn();
            return true;
        }
        
        private bool UseCreateBomb(Grid grid, PlayerInputItemSingleCell playerInputItem)
        {
            var coords = playerInputItem.TargetCoord;
            if (!grid.TryGetCell(coords, out var cell)) return false;
            var tile = cell.Tile;
            if (ReferenceEquals(tile, null) || !tile.CheckApplicability(playerInputItem.Item, cell, grid)) return false;
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Bomb, new TileOrigin(Creator.Item, cell));

            _reactionHandler.RegisterTileShouldReactOnDie(grid, this, null, cell.Coords, 
                tile.Id, tile.Asset, tile.Speciality, tile.State, tile.Kind, tile);
            
            cell.ReplaceTile(newTile);
            AddAction(new ActionReplace(coords, newTile, GoalType.None, false));

            IncrementCurrentTurn();
            return true;
        }
        
        private bool UseCreateColorBomb(Grid grid, PlayerInputItemSingleCell playerInputItem)
        {
            var coords = playerInputItem.TargetCoord;
            if (!grid.TryGetCell(coords, out var cell)) return false;
            var tile = cell.Tile;
            if (ReferenceEquals(tile, null) || !tile.CheckApplicability(playerInputItem.Item, cell, grid)) return false;
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.ColorBomb, new TileOrigin(Creator.Item, cell));
            
            _reactionHandler.RegisterTileShouldReactOnDie(grid, this, null, cell.Coords, 
                tile.Id, tile.Asset, tile.Speciality, tile.State, tile.Kind, tile);

            cell.ReplaceTile(newTile);
            AddAction(new ActionReplace(coords, newTile, GoalType.None, false));

            IncrementCurrentTurn();
            return true;
        }
        
        private bool UseCreatePropeller(Grid grid, PlayerInputItemSingleCell playerInputItem)
        {
            var coords = playerInputItem.TargetCoord;
            if (!grid.TryGetCell(coords, out var cell)) return false;
            var tile = cell.Tile;
            if (ReferenceEquals(tile, null) || !tile.CheckApplicability(playerInputItem.Item, cell, grid)) return false;
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Propeller, new TileOrigin(Creator.Item, cell));
            
            _reactionHandler.RegisterTileShouldReactOnDie(grid, this, null, cell.Coords, 
                tile.Id, tile.Asset, tile.Speciality, tile.State, tile.Kind, tile);

            cell.ReplaceTile(newTile);
            AddAction(new ActionReplace(coords, newTile, GoalType.None, false));

            IncrementCurrentTurn();
            return true;
        }

        private bool UseBalloon(Grid grid, PlayerInputItemBallon playerInputItemBallon)
        {
            var coords = playerInputItemBallon.TargetCoord;
            Cell cell;
            if (!grid.TryGetCell(coords, out cell)) return false;
            var tile = cell.Tile;
            if (ReferenceEquals(tile, null) || !tile.CheckApplicability(playerInputItemBallon.Item, cell, grid)) return false;
            AppendCellHit(cell, damageSource: DamageSource.UsableBoost, busyTime: 4);
            // AddActionWithDelay(new ActionPause(stepPause), stepPause);
            var balloonCoords = new List<Coords> { coords };
            CellsBaloonTemp.Clear();
            foreach (var c in grid.Cells)
            {
                if (!c.Tile.IsNull() && c.Tile.Speciality != TileSpeciality.ColorBomb)
                {
                    CellsBaloonTemp.Add(c);
                }
            }

            var allowedCells = CellsBaloonTemp;
            var balloonPathCells = new List<Cell>(4);
            for (int i = 1; i <= 4; i++)
            {
                var allowedCell = allowedCells.DeterministicRandomInSelf();
                allowedCells.Remove(allowedCell);
                balloonPathCells.Add(allowedCell);
            }

            balloonPathCells.Sort((a, b) => (a.Coords - coords).SqrMagnitude.CompareTo((b.Coords - coords).SqrMagnitude));
            for (int i = 0; i < balloonPathCells.Count; i++)
            {
                var balloonCell = balloonPathCells[i];
                AppendCellHit(balloonCell, damageSource: DamageSource.UsableBoost, busyTime: 0);
                // if (i + 1 < balloonPathCells.Count)
                //     AddActionWithDelay(new ActionPause(stepPause), delay);

                balloonCoords.Add(balloonCell.Coords);
            }

            AddActionWithDelay(new ActionItemBalloon(balloonCoords, 0), 0);
            IncrementCurrentTurn();
            return true;
        }

        private bool UseRain(Grid grid, PlayerInputItemRain itemRain)
        {
            CellsRainTemp.Clear();
            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.IsSuperBoostable() && cell.Tile.IsSimple())
                {
                    CellsRainTemp.Add(cell);
                }
            }

            var countToDestroy = (int)Math.Ceiling(CellsRainTemp.Count * itemRain.Percent);

            var hitCells = new List<Cell>();
            if (countToDestroy <= 0) return true;
    
            for (int i = 0; i < countToDestroy; i++)
            {
                if (CellsRainTemp.Count == 0)
                    break;
                var randomCell = CellsRainTemp.DeterministicRandomInSelf();
                CellsRainTemp.Remove(randomCell);
                hitCells.Add(randomCell);
            }

            var hitPositions = new List<Coords>();
            foreach (var c in hitCells)
            {
                hitPositions.Add(c.Coords);
            }
            itemRain.HitPositions = hitPositions;

            IncrementCurrentTurn();
            AddAction(new ActionSuperBoostTitle());
            AddAction(new ActionStartRain());
            IncrementCurrentTurn();

            for (int i = 0; i < hitCells.Count; i++)
            {
                var randomCell = hitCells[i];
                var randomTile = randomCell.Tile;
                var coords = randomCell.Coords;

                var busyWait = countToDestroy - i;
                AddActionWithDelay(new ActionLightning(new LightningRecord()
                {
                    Coords = coords,
                    TileKind = randomTile.Kind
                }, i), i);

                AppendCellHit(randomCell, damageSource: DamageSource.SuperBoost, busyTime: busyWait, i);
            }

            IncrementCurrentTurn();
            AddActionWithDelay(new ActionEndRain(), hitCells.Count);
            return true;
        }

        private bool UseWind(Grid grid, PlayerInputItemWind playerInputItemWind)
        {
            var firstCoord = playerInputItemWind.FirstCoord;
            var secondCoord = playerInputItemWind.SecondCoord;
            if (!grid.Contains(firstCoord) || !grid.Contains(secondCoord)) return false;

            Cell firstCell;
            if (!grid.TryGetCell(firstCoord, out firstCell)) return false;

            Cell secondCell;
            if (!grid.TryGetCell(secondCoord, out secondCell)) return false;

            var firstTile = firstCell.Tile;
            if (ReferenceEquals(firstTile, null) || !firstTile.CheckApplicability(playerInputItemWind.Item, firstCell, grid))
                return false;

            var secondTile = secondCell.Tile;
            if (ReferenceEquals(secondTile, null) || !secondTile.CheckApplicability(playerInputItemWind.Item, secondCell, grid))
                return false;

            // Swap:
            firstCell.SwapTileWith(secondCell);

            AddAction(new ActionSwapBoth(firstCoord, secondCoord));
            AddAction(new ActionSwapElegant(firstCoord, secondCoord));

            IncrementCurrentTurn();
            return true;
        }
        
        
        private bool UseVerticalBooster(Grid grid, PlayerInputItemVerticalBooster playerInputItemVerticalBooster)
        {
            var targetCoords = playerInputItemVerticalBooster.TargetCoord;
            if (!grid.TryGetCell(targetCoords, out _)) return false;

            targetCoords.Y = 0;
            const CardinalDirections directionToDestroy = CardinalDirections.N;
           
            var allCoords = CardinalDirectionsHelper.GetCoordsInDirection(targetCoords.X, targetCoords.Y, directionToDestroy,  grid);

            var damageCoords = new List<Coords>();
            foreach (var coords in allCoords)
            {
                if (!grid.TryGetCell(coords, out var damageCell)) continue;
                
                var hit = new Hit<Cell>(
                    null,
                    damageCell,
                    1,
                    DamageSource.UsableBoost,
                    coords: damageCell.Coords,
                    boostInfo: new DirectionalBoosterInfo(boosterIndex, directionToDestroy),
                    hitSourceUid: boosterIndex);
                Queue.AppendHit(1, hit);
                damageCoords.Add(damageCell.Coords);
            }
            
            AddAction(new ActionMoveDirectionalBooster(targetCoords, directionToDestroy, boosterIndex, damageCoords));
            boosterIndex++;
            IncrementCurrentTurn();
            return true;
        }
        
        private bool UseHorizontalBooster(Grid grid, PlayerInputItemHorizontalBooster playerInputItemVerticalBooster)
        {
            var targetCoords = playerInputItemVerticalBooster.TargetCoord;
            if (!grid.TryGetCell(targetCoords, out _)) return false;

            targetCoords.X = 0;
            const CardinalDirections directionToDestroy = CardinalDirections.E;
            
            var allCoords = CardinalDirectionsHelper.GetCoordsInDirection(targetCoords.X, targetCoords.Y, directionToDestroy,  grid);

            var damageCoords = new List<Coords>();
            foreach (var coords in allCoords)
            {
                if (!grid.TryGetCell(coords, out var damageCell)) continue;
                
                var hit = new Hit<Cell>(
                    null,
                    damageCell,
                    boosterIndex,
                    DamageSource.UsableBoost,
                    coords: damageCell.Coords,
                    boostInfo: new DirectionalBoosterInfo(boosterIndex, directionToDestroy),
                    hitSourceUid: boosterIndex);
                Queue.AppendHit(1, hit);
                damageCoords.Add(damageCell.Coords);
            }
            
            AddAction(new ActionMoveDirectionalBooster(targetCoords, directionToDestroy, boosterIndex, damageCoords));
            boosterIndex++;
            IncrementCurrentTurn();
            return true;
        }
        
    }
}
