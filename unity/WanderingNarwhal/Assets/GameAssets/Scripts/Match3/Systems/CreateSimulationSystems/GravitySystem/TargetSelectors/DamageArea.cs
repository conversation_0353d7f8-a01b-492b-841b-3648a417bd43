using System.Collections.Generic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class DamageArea
    {
        private readonly TileSpeciality _tileSpeciality;
        public readonly int FromX; //inclusive
        public readonly int ToX;   //inclusive
        public readonly int FromY; //inclusive
        public readonly int ToY;   //inclusive

        public DamageArea(TileSpeciality spec, Coords seedCoords, Grid grid)
        {
            _tileSpeciality = spec;
            switch (_tileSpeciality)
            {
                case TileSpeciality.Bomb:
                {
                    var bombRad = M3Constants.DefaultBombRadius;
                    FromX = seedCoords.X - bombRad;
                    ToX = seedCoords.X + bombRad;
                    FromY = seedCoords.Y - bombRad;
                    ToY = seedCoords.Y + bombRad;
                    break;
                }
                case TileSpeciality.ColumnBreaker:
                {
                
                    FromX = seedCoords.X;
                    ToX = seedCoords.X;
                    FromY = 0;
                    ToY = grid.Height - 1;
                    break;  
                }
                case TileSpeciality.RowBreaker:
                {
                    
                    FromX = 0;
                    ToX = grid.Width-1;
                    FromY = seedCoords.Y;
                    ToY = seedCoords.Y;
                    break;
                }
            }
        }

        public bool Contains(Coords coords)
        {
            return FromX <= coords.X && coords.X <= ToX && FromY <= coords.Y && ToY <= coords.Y;
        }

        public IEnumerable<Coords> GetAllCoords()
        {
            for(int x = FromX; x <= ToX; x++)
            for (int y = FromY; y <= ToY; y++)
            {
                yield return new Coords(x, y);
            }
        }

        public Coords GetTargetCoords()
        {
            switch (_tileSpeciality)
            {
                case TileSpeciality.Bomb:
                {
                    var bombRad = M3Constants.DefaultBombRadius;
                    return new Coords(FromX + bombRad, FromY + bombRad);
                }
                case TileSpeciality.ColumnBreaker:
                {
                    return new Coords(FromX, RandomSystem.Next(ToY));
                }
                case TileSpeciality.RowBreaker:
                {
                    return new Coords(RandomSystem.Next(ToX), FromY);
                }
            }

            return Coords.OutOfGrid;
        }
    }
}