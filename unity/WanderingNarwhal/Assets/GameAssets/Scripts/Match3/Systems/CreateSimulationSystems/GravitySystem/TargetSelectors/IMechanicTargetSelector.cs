using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public interface IMechanicTargetSelector
    {
        void Init(Grid grid, GoalsSystem goalSystem);
        void UnInit();
        Cell SelectTarget(Coords sourceCoords, TileSpeciality skipTileSpeciality = TileSpeciality.None, bool includeSourceCoords = false);
        Cell SelectComboTarget(TileSpeciality boosterSpec);
    }
}