using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class MechanicTargetInfo
    {
        private Cell Cell { get; set; }

        private float _weight;

        public float TotalWeight
        {
            get
            {
                if (_removableWeights.Count == 0)
                    return 0;

                return _weight;
            }
        }

        private readonly List<float> _removableWeights;

        public MechanicTargetInfo(Cell cell)
        {
            Cell = cell;
            _removableWeights = new List<float>();
        }

        public void Init(Cell cell)
        {
            Cell = cell;
            _removableWeights.Clear();
        }

        public void DetermineWeights(ILayerWeightsDeterminer determiner)
        {
            _weight = determiner.DetermineWeights(Cell, _removableWeights);            
        }
    }
}