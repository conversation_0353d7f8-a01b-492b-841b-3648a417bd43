using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class MechanicTargetSelector : IMechanicTargetSelector
    {
        private Grid _grid;
        private readonly StandardLayerWeightDeterminer _weightDeterminer = new();
        private MechanicTargetInfo[,] _targetInfosMap;
        private readonly MechanicTargetingSettings _mechanicTargetingSettings;
        private readonly HashSet<Cell> _tmpHashSet = new();

        public MechanicTargetSelector(MechanicTargetingSettings mechanicTargetingSettings)
        {
            _mechanicTargetingSettings = mechanicTargetingSettings;
        }

        public void Init(Grid grid, GoalsSystem goalsSystem)
        {
            _grid = grid;
            _grid.OnHitToCell -= HandleHitOnCell;
            _grid.OnHitToCell += HandleHitOnCell;
            _targetInfosMap = new MechanicTargetInfo[grid.Width, grid.Height];
            
            _weightDeterminer.Init(grid, goalsSystem, _mechanicTargetingSettings);
            _weightDeterminer.InvalidateCache();

            foreach (var cell in _grid.Cells)
            {
                var coords = cell.Coords;
                _targetInfosMap[coords.X, coords.Y] ??= new MechanicTargetInfo(cell);
                var info = _targetInfosMap[coords.X, coords.Y];
                info.Init(cell);
                info.DetermineWeights(_weightDeterminer);
            }
        }

        private void HandleHitOnCell(Cell cell)
        {
            if (cell != null)
            {
                AddHitTo(cell);
            }
        }

        public void UnInit()
        {
            _grid.OnHitToCell -= HandleHitOnCell;
            _targetInfosMap = null;
            _weightDeterminer.UnInit();
        }

        public Cell SelectTarget(Coords sourceCoords,
            TileSpeciality skipTileSpeciality = TileSpeciality.None, bool includeSourceCoords = false)
        {
            if (_grid.Cells.Count == 0)
                return null;

            var maxWeight = float.MinValue;
            Cell selectedCell = null;
            foreach (var cell in _grid.GetCellsInRandomOrder())
            {
                var coords = cell.Coords;

                if (ShouldSkipCell(cell, sourceCoords, includeSourceCoords, skipTileSpeciality))
                    continue;

                var info = _targetInfosMap[coords.X, coords.Y];
                if (info == null)
                    continue;

                var weight = info.TotalWeight;
                if (weight > maxWeight)
                {
                    maxWeight = weight;
                    selectedCell = cell;
                }
            }

            selectedCell ??= _grid.Cells.DeterministicRandomInSelf();

            return selectedCell;
        }

        private float GetSumDamage(Grid grid, DamageArea damageArea)
        {
            var result = 0f;
            foreach (var coords in damageArea.GetAllCoords())
            {
                if (grid.TryGetCell(coords, out var cell))
                {
                    var mainCell = cell.GetMainCellReference(out _);
                    
                    if (ShouldSkipCell(mainCell))
                        continue;
                    
                    if (_tmpHashSet.Add(mainCell))
                    {
                        var info = _targetInfosMap[mainCell.Coords.X, mainCell.Coords.Y];
                        if (info == null || info.TotalWeight < 0)
                            continue;

                        result += info.TotalWeight;
                    }
                }
            }

            _tmpHashSet.Clear();
            return result;
        }

        public Cell SelectComboTarget(TileSpeciality boosterSpec)
        {
            if (_grid.Cells.Count == 0)
                return null;

            _weightDeterminer.InvalidateCache();

            var maxWeight = float.MinValue;
            DamageArea selectedCellDamageArea = null;
            foreach (var cell in _grid.GetCellsInRandomOrder())
            {
                if (ShouldSkipCell(cell))
                    continue;
                
                var info = _targetInfosMap[cell.Coords.X, cell.Coords.Y];
                if (info == null || info.TotalWeight < 0)
                    continue;

                var damageArea = new DamageArea(boosterSpec, cell.Coords, _grid);
                var weight = GetSumDamage(_grid, damageArea);
                if (weight > maxWeight)
                {
                    maxWeight = weight;
                    selectedCellDamageArea = damageArea;
                }
            }

            Cell selectedCell = null;
            if (selectedCellDamageArea != null)
            {
                var targetCoords = selectedCellDamageArea.GetTargetCoords();
                _grid.TryGetCell(targetCoords, out selectedCell);

                var info = _targetInfosMap[targetCoords.X, targetCoords.Y];

                if (selectedCell == null || info == null || info.TotalWeight < 0 || ShouldSkipCell(selectedCell))
                {
                    maxWeight = float.MinValue;
                    foreach (var coords in selectedCellDamageArea.GetAllCoords())
                    {
                        if (_grid.TryGetCell(coords, out var cell))
                        {
                            info = _targetInfosMap[coords.X, coords.Y];

                            if (info == null || info.TotalWeight < 0 || ShouldSkipCell(cell))
                                continue;

                            if (info.TotalWeight > maxWeight)
                            {
                                maxWeight = info.TotalWeight;
                                selectedCell = cell;
                            }
                        }
                    }
                }
            }

            return selectedCell;
        }

        private bool ShouldSkipCell(Cell cell, Coords? sourceCoords = null,
            bool includeSourceCoords = false, TileSpeciality skipTileSpeciality = TileSpeciality.None)
        {
            var coords = cell.Coords;

            if (sourceCoords != null && coords == sourceCoords.Value && !includeSourceCoords)
                return true;

            var mainCell = cell.GetMainCellReference(out _);

            if (!mainCell.Equals(cell) && mainCell.IsBusy == 0)
                return true;

            if (cell.HasMultiSizeCellReferenceWithCellOverlay())
                return true;

            if (cell.HasTile() && cell.Tile.IsAnyOf(TileState.Invincible) && !cell.Tile.HasAnyArmor() &&
                cell.BackgroundCount <= 0)
                return true;

            if (cell.HasTile() && cell.Tile.IsAnyOf(TileState.GondolaMod) &&
                cell.Tile.GetParam(TileParamEnum.GondolaReached) > 0)
                return true;

            if (skipTileSpeciality != TileSpeciality.None && cell.HasTile() &&
                cell.Tile.Speciality == skipTileSpeciality)
                return true;

            return false;
        }

        private void AddHitTo(Cell cell)
        {
            _weightDeterminer.InvalidateCache();

            var coords = cell.Coords;
            _targetInfosMap[coords.X, coords.Y] ??= new MechanicTargetInfo(cell);
            var info = _targetInfosMap[coords.X, coords.Y];
            info.Init(cell);
            info.DetermineWeights(_weightDeterminer);
        }
    }
}