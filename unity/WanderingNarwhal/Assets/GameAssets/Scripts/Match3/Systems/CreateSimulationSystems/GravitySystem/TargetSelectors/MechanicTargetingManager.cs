using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.GoalsService;
using FBConfig;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class MechanicTargetingManager
    {
        private readonly Dictionary<string, IMechanicTargetSelector> _mechanicTargetSelectors = new();
        private Grid _grid;

        public MechanicTargetingManager(IDictionary<string, MechanicTargetingConfig> configDict)
        {
            foreach (var variable in configDict)
            {
                if (Enum.TryParse(variable.Key, out DamageSource damageSource))
                {
                    var mechanicTargetSettings = new MechanicTargetingSettings(variable.Value);
                    var mechanicTargetSelector = new MechanicTargetSelector(mechanicTargetSettings);
                    _mechanicTargetSelectors.Add(damageSource.ToString(), mechanicTargetSelector);
                }
            }
        }

        public void Init(Grid grid, GoalsSystem goalSystem)
        {
            _grid = grid;
            foreach (var mechanicTargetSelector in _mechanicTargetSelectors.Values)
            {
                mechanicTargetSelector.Init(grid, goalSystem);
            }
        }

        public void UnInit()
        {
            foreach (var mechanicTargetSelector in _mechanicTargetSelectors.Values)
            {
                mechanicTargetSelector.UnInit();
            }
        }

        private Cell GetTarget(Func<IMechanicTargetSelector, Cell> selectorFunction, string damageSourceKey)
        {
            if (_mechanicTargetSelectors.TryGetValue(damageSourceKey, out var targetSelector))
            {
                var cell = selectorFunction(targetSelector);

                if (cell != null)
                {
                    return cell;
                }
            }

            return _grid.Cells.DeterministicRandomInSelf();
        }

        public Cell SelectTarget(DamageSource damageSource, Coords sourceCoords,
            TileSpeciality skipTileSpeciality = TileSpeciality.None, bool includeSourceCoords = false)
        {
            return GetTarget(targetSelector =>
                    targetSelector.SelectTarget(sourceCoords, skipTileSpeciality, includeSourceCoords),
                damageSource.ToString());
        }

        public Cell SelectComboTarget(DamageSource damageSource, TileSpeciality boosterSpec)
        {
            return GetTarget(targetSelector => targetSelector.SelectComboTarget(boosterSpec), damageSource.ToString());
        }
    }
}