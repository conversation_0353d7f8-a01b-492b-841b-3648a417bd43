using System.Text;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class AutoMatchWaitCondition : WaitConditionBase
    {
        private readonly int _ownTileId;
        private readonly int[] _tileIds;

        public AutoMatchWaitCondition(HitWaitParams hitWaitParams, int tileId) : base(hitWaitParams)
        {
            _ownTileId = tileId;
            _tileIds = hitWaitParams.MatchIds;
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (_tileIds == null)
                return false;
            
            foreach (var tileId in _tileIds)
            {
                if (proxy.TileTickPlayer.NotFrozen(tileId))
                    return true;
            }

            return false;
        }

        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (_tileIds == null) return "[ ]";

            var result = new StringBuilder("[ tile ids : ");
            foreach (var tileId in _tileIds)
            {
                const string space = " ";
                result.Append(tileId).Append(space);
            }

            result.Append("]");

            return result.ToString();
        }
    }
}