using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace BBB.Match3
{
    //may be needed later
    public class BatchCondition
    {
        private readonly DamageSource _damageSource;
        private readonly BoltInfo _boltInfo;
        
        public BatchCondition(HitWaitParams hitWaitParams)
        {
            _damageSource = hitWaitParams.DamageSource;
            _boltInfo = hitWaitParams.BoltInfo;
        }

        public bool IsBatchableWith(BatchCondition batchCondition)
        {
            return _damageSource == DamageSource.RemoveColorTiles
                   && batchCondition._damageSource == DamageSource.RemoveColorTiles
                   && (_boltInfo?.IsCombo ?? false)
                   && (batchCondition._boltInfo?.IsCombo ?? false)
                   && _boltInfo.BoltId == batchCondition._boltInfo.BoltId;
        }

    }
}