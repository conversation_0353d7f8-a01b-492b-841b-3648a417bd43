using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace BBB.Match3
{
    public class BoltWaitCondition : WaitConditionBase
    {
        private readonly BoltInfo _boltInfo;
        private readonly Coords _coords;
        private readonly BoltBoardObjectPredicate _predicate;
        private readonly string _textData;

        public BoltWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _boltInfo = hitWaitParams.BoltInfo;
            _coords = hitWaitParams.Coords;
            _predicate = new BoltBoardObjectPredicate()
            {
                BoltId = _boltInfo.BoltId,
                Coords = _coords,
            };
            _textData = $"[ coords={_coords} ]";
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            bool wait = proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_predicate);
            return wait;
        }

        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }

    sealed class BoltBoardObjectPredicate : IBoardObjectPredicate
    {
        public int BoltId;
        public Coords Coords;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            var boltTarget = BoardObject as BoltTarget;
            var result = boltTarget != null && boltTarget.BoltId == BoltId &&
                         boltTarget.Coords.Equals(Coords);
            
            // If we are waiting for this object, let the object know about it so that, in the case it's auto-destroyable,
            // countdown for auto-destruction can start
            if (result)
            {
                boltTarget.WaitConditionStarted = true;
            }
            return result;
        }
    }
}