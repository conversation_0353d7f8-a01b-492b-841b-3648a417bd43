using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class DirectionalBoostersWaitCondition : WaitConditionBase
    {
        private readonly DirectionalBoosterActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly DirectionalBoosterTrashBinBoardObjectPredicate _trashBinObjectPredicate;
        private readonly Coords _hitCoords;
        private readonly string _textData;

        public DirectionalBoostersWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new ()
            {
                BoosterId = hitWaitParams.DirectionalBoosterInfo.Id,
                Coords = hitWaitParams.Coords
            };
            _trashBinObjectPredicate = new ()
            {
                BoosterId = hitWaitParams.DirectionalBoosterInfo.Id
            };
            _hitCoords = hitWaitParams.Coords;
            _textData = $"[ coords={_hitCoords} ]";
        }

        // Returns true for when there are RocketTargets with a particular Rocket Id on this coords
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            _activeObjectPredicate.Proxy = proxy;
            return !proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) &&
                !proxy.TileTickPlayer.AnyTrashBinObjectSatisfiesPredicate(_trashBinObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }

    sealed class DirectionalBoosterActiveBoardObjectPredicate : IBoardObjectPredicate
    {
        public int BoosterId;
        public Coords Coords;
        public PlaySimulationActionProxy Proxy;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            if (BoardObject is DirectionalBooster directionalBooster && directionalBooster.BoosterId == BoosterId)
            {
                return directionalBooster.OverlappedOrPassed(Coords, Proxy);
            }

            return false;
        }
    }
    
    sealed class DirectionalBoosterTrashBinBoardObjectPredicate : IBoardObjectPredicate
    {
        public int BoosterId;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            return BoardObject is DirectionalBooster directionalBooster && directionalBooster.BoosterId == BoosterId;
        }
    }
}