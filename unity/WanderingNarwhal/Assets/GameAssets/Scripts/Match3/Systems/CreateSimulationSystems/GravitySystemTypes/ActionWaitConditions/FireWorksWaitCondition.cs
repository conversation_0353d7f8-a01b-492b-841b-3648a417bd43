using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class FireWorksWaitCondition : WaitConditionBase
    {
        private readonly FireWorksActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly string _textData;

        public FireWorksWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new FireWorksActiveBoardObjectPredicate
            {
                FireWorksId = hitWaitParams.FireWorksInfo.FireWorksId
            };
            var fireWorksInfo = hitWaitParams.FireWorksInfo;
            _textData = $"[ fireworksId={fireWorksInfo.FireWorksId} ]";
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }
}