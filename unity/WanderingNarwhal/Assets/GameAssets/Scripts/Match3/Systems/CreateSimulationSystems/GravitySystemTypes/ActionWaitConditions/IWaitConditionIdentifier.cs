namespace BBB.Match3
{
    public interface IWaitConditionIdentifier
    {
        
    }

    public sealed class PropellerIdentifier : IWaitConditionIdentifier
    {
        public int PropellerId { get; }

        public PropellerIdentifier(int propellerId)
        {
            PropellerId = propellerId;
        }
    }
    
    public sealed class FireWorksIdentifier : IWaitConditionIdentifier
    {
        public int FireWorksId { get; }

        public FireWorksIdentifier(int fireWorksId)
        {
            FireWorksId = fireWorksId;
        }
    }
    
    public sealed class BoltIdentifier : IWaitConditionIdentifier
    {
        public int BoltId { get; }

        public BoltIdentifier(int boltId)
        {
            BoltId = boltId;
        }
    }

    public sealed class LineBreakerIdentifier : IWaitConditionIdentifier
    {
        public int LinebreakerId { get; }
        public Coords Coords { get;  }

        public LineBreakerIdentifier(int linebreakerId, Coords coords)
        {
            LinebreakerId = linebreakerId;
            Coords = coords;
        }
    }

    public sealed class WhirlpoolIdentifier : IWaitConditionIdentifier
    {
        public int WhirlpoolId { get; }
        public Coords Coords { get; }

        public WhirlpoolIdentifier(int whirlpoolId, Coords coords)
        {
            WhirlpoolId = whirlpoolId;
            Coords = coords;
        }
    }
    
    public sealed class TukTukIdentifier : IWaitConditionIdentifier
    {
        public int TukTukId { get; }
        public Coords Coords { get;  }

        public TukTukIdentifier(int tukTukId, Coords coords)
        {
            TukTukId = tukTukId;
            Coords = coords;
        }
    }

    public class DirectionalBoosterIdentifier : IWaitConditionIdentifier
    {
        public int BoosterId { get; }
        public Coords Coords { get;  }

        public DirectionalBoosterIdentifier(int boosterId, Coords coords)
        {
            BoosterId = boosterId;
            Coords = coords;
        }
    }
}