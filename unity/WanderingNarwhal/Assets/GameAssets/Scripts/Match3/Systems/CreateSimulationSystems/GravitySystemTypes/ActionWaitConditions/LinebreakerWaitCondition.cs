using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class LinebreakerWaitCondition : WaitConditionBase
    {
        private readonly LineBreakerActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly LineBreakerTrashBinBoardObjectPredicate _trashBinObjectPredicate;
        private readonly Coords _hitCoords;
        private readonly string _textData;

        public LinebreakerWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new ()
            {
                LineBreakerId = hitWaitParams.LinebreakerInfo.Id,
                Coords = hitWaitParams.Coords
            };
            _trashBinObjectPredicate = new ()
            {
                LineBreakerId = hitWaitParams.LinebreakerInfo.Id
            };
            _hitCoords = hitWaitParams.Coords;
            _textData = $"[ coords={_hitCoords} ] ";
        }

        // Returns true for when there are RocketTargets with a particular Rocket Id on this coords
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            _activeObjectPredicate.Proxy = proxy;
            return !proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) &&
                !proxy.TileTickPlayer.AnyTrashBinObjectSatisfiesPredicate(_trashBinObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }

    sealed class LineBreakerActiveBoardObjectPredicate : IBoardObjectPredicate
    {
        public int LineBreakerId;
        public Coords Coords;
        public PlaySimulationActionProxy Proxy;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            if (BoardObject is Rocket rocket && rocket.LineBreakerId == LineBreakerId)
            {
                return rocket.OverlappedOrPassed(Coords, Proxy);
            }

            return false;
        }
    }
    
    sealed class LineBreakerTrashBinBoardObjectPredicate : IBoardObjectPredicate
    {
        public int LineBreakerId;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            return BoardObject is Rocket rocket && rocket.LineBreakerId == LineBreakerId;
        }
    }
}