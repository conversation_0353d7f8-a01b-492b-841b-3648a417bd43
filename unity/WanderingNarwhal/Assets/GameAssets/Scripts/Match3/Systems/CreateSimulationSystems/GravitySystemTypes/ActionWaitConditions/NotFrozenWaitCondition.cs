using System.Text;
using BBB.Match3.Systems;

namespace BBB.Match3
{
    public class NotFrozenWaitCondition : WaitConditionBase
    {
        private readonly int _tileId;
        private readonly Coords _tileCoords;
        private readonly Coords[] _coords;

        public NotFrozenWaitCondition(int tileId, Coords tileCoords) : base(null)
        {
            _tileId = tileId;
            _tileCoords = tileCoords;
        }

        public NotFrozenWaitCondition(Coords[] coords) : base(null)
        {
            _coords = coords;
            _tileId = -1;
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _tileId >= 0 && proxy.TileTickPlayer.NotFrozen(_tileId) ||
                   _coords != null && proxy.TileTickPlayer.NotFrozen(_coords);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            var result = new StringBuilder();
            result.Append("[ tileId=").Append(_tileId).Append(" ");

            if (_tileId >= 0)
            {
                var tileView = proxy.TileController.GetTileViewById(_tileId, false);
                if (tileView != null && tileView.TileMotionController.Mode != TileTransformMode.Frozen)
                {
                    result.Append("(tile mode is ").Append(tileView.TileMotionController.Mode).Append(")");
                }
            }

            if (_coords is { Length: > 0 })
            {
                result.Append("coords : ");
                foreach (var coord in _coords)
                {
                    result.Append(coord);
                    const string rightParenthesis = ") ";
                    const string tileText = "(tile ";
                    const string idText = " id=";

                    if (!proxy.TileController.HasTileViewInCoord(coord))
                    {
                        const string tilesNotFoundText = "(tile not found id=";
                        var tileId = grid.TryGetCell(coord, out var cell) && cell.HasTile() ? cell.Tile.Id : -1;
                        result.Append(tilesNotFoundText).Append(tileId).Append(rightParenthesis);
                    }

                    var tileView = proxy.TileController.GetTileViewByCoord(coord, false);
                    if (tileView == null || tileView.TileMotionController.Mode == TileTransformMode.Frozen) continue;
                    
                    var id = grid.TryGetCell(coord, out var cell1) && cell1.HasTile() ? cell1.Tile.Id : -1;
                    result.Append(tileText).Append(tileView.TileMotionController.Mode).Append(idText).Append(id).Append(rightParenthesis);
                }
            }

            result.Append(" ]");

            return result.ToString();
        }

    }
}