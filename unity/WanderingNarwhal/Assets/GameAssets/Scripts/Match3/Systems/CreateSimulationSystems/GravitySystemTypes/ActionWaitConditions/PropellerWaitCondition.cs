using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class PropellerWaitCondition : WaitConditionBase
    {
        private readonly PropellerActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly string _textData;

        public PropellerWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new PropellerActiveBoardObjectPredicate
            {
                PropellerId = hitWaitParams.PropellerInfo.PropellerId
            };
            var propellerInfo = hitWaitParams.PropellerInfo;
            _textData = $"[ propellerId={propellerInfo.PropellerId} ]";
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }
}