using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class SkunkWaitCondition : WaitConditionBase
    {
        private readonly SkunkActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly string _textData;

        public SkunkWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new SkunkActiveBoardObjectPredicate
            {
                SkunkId = hitWaitParams.SkunkHitInfo.SkunkHitId
            };

            var skunkHitInfo = hitWaitParams.SkunkHitInfo;
            _textData = $"[ skunkId ={skunkHitInfo.SkunkHitId} ]";
        }

        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate);
        }

        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }
}