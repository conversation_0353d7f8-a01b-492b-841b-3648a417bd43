using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class TukTukWaitCondition : WaitConditionBase
    {
        private readonly TukTukActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly TukTukTrashBinBoardObjectPredicate _trashBinObjectPredicate;
        private readonly Coords _hitCoords;
        private readonly string _textData;

        public TukTukWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new ()
            {
                TukTukId = hitWaitParams.TukTukInfo.Id,
                Coords = hitWaitParams.Coords
            };
            _trashBinObjectPredicate = new ()
            {
                TukTukId = hitWaitParams.TukTukInfo.Id
            };
            _hitCoords = hitWaitParams.Coords;
            _textData = $"[ coords={_hitCoords} ]";
        }

        // Returns true for when there are RocketTargets with a particular Rocket Id on this coords
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            _activeObjectPredicate.Proxy = proxy;
            return !proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) &&
                !proxy.TileTickPlayer.AnyTrashBinObjectSatisfiesPredicate(_trashBinObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }

    internal sealed class TukTukActiveBoardObjectPredicate : IBoardObjectPredicate
    {
        public int TukTukId;
        public Coords Coords;
        public PlaySimulationActionProxy Proxy;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            if (BoardObject is TukTuk tukTuk && tukTuk.TukTukId == TukTukId)
            {
                return tukTuk.OverlappedOrPassed(Coords, Proxy);
            }

            return false;
        }
    }
    
    sealed class TukTukTrashBinBoardObjectPredicate : IBoardObjectPredicate
    {
        public int TukTukId;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            return BoardObject is TukTuk tukTuk && tukTuk.TukTukId == TukTukId;
        }
    }
}