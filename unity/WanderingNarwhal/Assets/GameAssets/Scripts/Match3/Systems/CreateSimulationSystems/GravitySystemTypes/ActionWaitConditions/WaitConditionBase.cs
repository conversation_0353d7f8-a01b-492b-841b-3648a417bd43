using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public abstract class WaitConditionBase
    {
        //public readonly BatchCondition BatchCondition;

        protected WaitConditionBase(HitWaitParams hitWaitParams)
        {
            //BatchCondition = hitParams != null ? new BatchCondition(hitParams) : null;
        }
        
        public abstract bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy);

        public abstract string GetTextData(Grid grid, PlaySimulationActionProxy proxy);
    }
}