using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public class WhirlpoolWaitCondition : WaitConditionBase
    {
        private readonly WhirlpoolActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly Coords _hitCoords;
        private readonly string _textData;

        public WhirlpoolWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _activeObjectPredicate = new ()
            {
                WhirlpoolId = hitWaitParams.WhirlpoolInfo.WhirlpoolId,
                Coords = hitWaitParams.Coords
            };
            _hitCoords = hitWaitParams.Coords;
            _textData = $"[ coords={_hitCoords} ]";
        }
        
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }

    internal sealed class WhirlpoolActiveBoardObjectPredicate : IBoardObjectPredicate
    {
        public int WhirlpoolId;
        public Coords Coords;
        public BoardObjectBase BoardObject { private get; set; }

        public bool Evaluate()
        {
            return BoardObject is WhirlpoolTarget target && target.WhirlpoolId == WhirlpoolId && target.Coords == Coords;
        }
    }
}