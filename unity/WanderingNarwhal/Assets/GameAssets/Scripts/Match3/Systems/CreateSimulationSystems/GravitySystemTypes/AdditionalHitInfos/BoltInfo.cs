using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class BoltInfo : IEquatable<BoltInfo>, IBoostInfo, IWaitConditionIdentifier
    {
        public bool IsCombo { get; }

        public int BoltId { get; }

        public BoltInfo(int id, bool isCombo)
        {
            IsCombo = isCombo;
            BoltId = id;
        }
        
        public bool Equals(BoltInfo other)
        {
            return other != null && BoltId == other.BoltId;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is BoltInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (BoltId * 397);
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}