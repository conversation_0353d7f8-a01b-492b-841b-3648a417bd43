using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class BombInfo : IEquatable<BombInfo>, IBoostInfo
    {
        private readonly int _id;
        private readonly bool _fromBolt;

        public int BombId => _id;
        public bool FromBolt => _fromBolt;

        public BombInfo(int id, bool fromBolt)
        {
            _id = id;
            _fromBolt = fromBolt;
        }
        
        public bool Equals(BombInfo other)
        {
            return other != null && _id == other._id;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is BombInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (_id * 397) ^ _fromBolt.GetHashCode();
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}