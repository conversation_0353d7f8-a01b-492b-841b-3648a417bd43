using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class FireWorksInfo : IEquatable<FireWorksInfo>, IBoostInfo, IWaitConditionIdentifier
    {
        private readonly int _id;

        public int FireWorksId => _id;

        public FireWorksInfo(int id)
        {
            _id = id;
        }
        
        public bool Equals(FireWorksInfo other)
        {
            return other != null && _id == other._id;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is FireWorksInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (_id * 397);
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}