using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public sealed class LineBreakerInfo : IEquatable<LineBreakerInfo>, IBoostInfo
    {
        public int Id { get; }

        public CardinalDirections Direction { get; }

        public Coords OriginCoords { get; }

        public LineBreakerInfo(int id, CardinalDirections direction, Coords originCoords)
        {
            Id = id;
            Direction = direction;
            OriginCoords = originCoords;
        }

        public bool Equals(LineBreakerInfo other)
        {
            return other != null && Id == other.Id && Direction == other.Direction;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is LineBreakerInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (Id * 397) ^ (int) Direction;
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}