using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class PropellerInfo : IEquatable<PropellerInfo>, IBoostInfo, IWaitConditionIdentifier
    {
        private readonly int _id;

        public int PropellerId => _id;
        public readonly TileAsset TileAsset;
        public readonly TileSpeciality TileSpeciality;

        public PropellerInfo(int id)
        {
            _id = id;
        }
        
        public PropellerInfo(int id, TileAsset tileAsset, TileSpeciality tileSpeciality)
        {
            _id = id;
            TileAsset = tileAsset;
            TileSpeciality = tileSpeciality;
        }
        
        public bool Equals(PropellerInfo other)
        {
            return other != null && _id == other._id;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is PropellerInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (_id * 397);
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}