using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class SkunkHitInfo : IEquatable<SkunkHitInfo>, IBoostInfo, IWaitConditionIdentifier
    {
        private readonly int _id;

        public int SkunkHitId => _id;

        public SkunkHitInfo(int id)
        {
            _id = id;
        }
        
        public bool Equals(SkunkHitInfo other)
        {
            return other != null && _id == other._id;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is SkunkHitInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (_id * 397);
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}