using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public sealed class TukTukInfo : IEquatable<TukTukInfo>, IBoostInfo
    {
        public int Id { get; }
        private CardinalDirections Direction { get; }

        public TukTukInfo(int id, CardinalDirections direction)
        {
            Id = id;
            Direction = direction;
        }

        public bool Equals(TukTukInfo other)
        {
            return other != null && Id == other.Id && Direction == other.Direction;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is TukTukInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (Id * 397) ^ (int) Direction;
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}