using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public class WhirlpoolInfo : IEquatable<WhirlpoolInfo>, IBoostInfo
    {
        private readonly int _id;

        public int WhirlpoolId => _id;
        public readonly int WaveIndex;

        public WhirlpoolInfo(int id, int waveIndex = 0)
        {
            _id = id;
            WaveIndex = waveIndex;
        }

        public bool Equals(WhirlpoolInfo other)
        {
            return other != null && _id == other._id;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            return obj is WhirlpoolInfo info && Equals(info);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (_id * 397);
            }
        }

        public bool BoostEquals(IBoostInfo other)
        {
            return Equals(other);
        }
    }
}