using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BebopBee.Core.Collections;
using GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.Match3Actions;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class SimulationDebugInfo
    {
        public int MatchesHappened = 0;
    }

    public sealed class GameSimulation
    {
        private IPlayerInput _playerInput;

        private SortedNumberedMultiMap<Match3ActionBase> _logicalSimulation = new();


        public readonly Dictionary<int, TileKinds> DefinedTileKinds = new Dictionary<int, TileKinds>();

        public SimulationResult Result { get; set; }

        public bool IsNotEmpty => _logicalSimulation.NotEmpty;

        public SimulationDebugInfo DebugInfo { get; set; }

        public int ShuffleCount = 0;

        public int MaxTurn => _logicalSimulation.MaxNumber;

        public int MinTurn => _logicalSimulation.MinNumber;

        public GameSimulation(IPlayerInput playerInput)
        {
            Init(playerInput);
        }

        public void Init(IPlayerInput playerInput)
        {
            _playerInput = playerInput;
            Result = SimulationResult.Fail;
            
#if UNITY_EDITOR
            DebugInfo = new SimulationDebugInfo();
#endif
        }

        public void ReplaceLogicalActionOfSameType(int turnToApply, Match3ActionBase action)
        {
            
            _logicalSimulation.ReplaceOfSameType(turnToApply, action);
        }

        public void AddLogicalAction(int turnToApplyOn, Match3ActionBase newAction)
        {
            lock (this)
            {
                _logicalSimulation.Add(turnToApplyOn, newAction);
            }
        }

        public bool ModifyActionsSatisfyingCondition(Predicate<Match3ActionBase> condition,
            Action<Match3ActionBase> modifier, bool onlyModifyLast = true)
        {
            var anyModified = false;
            foreach (var action in _logicalSimulation.ReversedElements)
            {
                if (condition(action))
                {
                    modifier(action);
                    anyModified = true;
                    if (onlyModifyLast) break;
                }
            }

            return anyModified;
        }

        public override string ToString()
        {
            /*if (M3Debug.Disabled)
                return string.Empty;*/

            var str = "\nPlayerInput: " + _playerInput +
                      "\nResult: " + Result.ToString<SimulationResult>() +
                      "\nCreated simulation:\n";
            foreach (var sim in _logicalSimulation)
            {
                var turn = sim.Item1;
                str += "\nTurn " + turn + " =>\n";
                foreach (var tileAction in sim.Item2)
                {
                    str += tileAction + "\n";
                }
            }

            return str;
        }

        public void VirtualApplyToSuperBoost(SuperBoostSystem superBoostSystem)
        {
            foreach (var tuple in _logicalSimulation)
            {
                var list = tuple.Item2;
                foreach (var action in list)
                {
                    var superBoostAction = action as ActionTryIncrementSuperBoost;
                    superBoostAction?.VirtualApplyToSuperBoost(superBoostSystem);
                }
            }
        }

        public bool IsBreakPointForGondola(Coords coords, HitWaitParams hitWaitParams)
        {
            foreach (var (_, actions) in _logicalSimulation)
            {
                foreach (var action in actions)
                {
                    switch (action)
                    {
                        case ActionPropellerFlight propellerAction when hitWaitParams.PropellerInfo != null && 
                                                                        coords == propellerAction.GetTargetCoords():
                        case ActionPlaySkunkHit skunkAction when hitWaitParams.SkunkHitInfo != null && 
                                                                 coords == skunkAction.GetTargetCoords():
                        case ActionFireWorksFlight fireWorksAction when hitWaitParams.FireWorksInfo != null && 
                                                                        coords == fireWorksAction.GetTargetCoords():
                            return true;
                    }
                }
            }
            
            return false;
        }

        public int CountSpawnsFor(TileSpeciality tileSpeciality)
        {
            var result = 0;
            foreach (var tuple in _logicalSimulation)
            {
                var list = tuple.Item2;
                foreach (var action in list)
                {
                    if (action is ActionSpawn spawnAction)
                        result += spawnAction.DoesTileSatisfySpeciality(tileSpeciality) ? 1 : 0;
                }
            }
            return result;
        }

        public int CountRemovesFor(Predicate<TileState> predicate)
        {
            var result = 0;
            foreach (var tuple in _logicalSimulation)
            {
                var list = tuple.Item2;
                foreach (var action in list)
                {
                    if (action is not ActionRemoveTile removeAction)
                        continue;

                    result += removeAction.DoesTileStateSatisfy(predicate) ? 1 : 0;
                }
            }

            return result;
        }

        /// <summary>
        /// Is current simulation contains action for removing specific tile.
        /// </summary>
        /// <param name="tileState">Tile definition.</param>
        /// <remarks>
        /// Actions collection is suitable for checks right at the moment of simulation end,
        /// and before executing visual update of game field (because all stored actions will be executed and disposed).
        ///
        /// This is used, for example, to determine if SandTile was destroyed during this turn. -VK
        /// </remarks>
        public bool HasRemoveActionFor(TileState tileState)
        {
            foreach (var tuple in _logicalSimulation)
            {
                var list = tuple.Item2;
                foreach (var action in list)
                {
                    switch (action)
                    {
                        case ActionRemoveTile tile when tile.IsTileOfState(tileState):
                        case ActionRemoveState state when (state.targetState & tileState) != 0:
                            return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Determine what tile ids received HP damage during this turn.
        /// </summary>
        public bool HasReduceAdjacentHpFor(List<int> tiles)
        {
            var result = false;
            foreach (var tuple in _logicalSimulation)
            {
                var list = tuple.Item2;
                foreach (var action in list)
                {
                    if (action is not ActionChangeTileParam { TileParam: { Count: > 0 } } hpAction) continue;
                    foreach (var m in hpAction.TileParam)
                    {
                        if (m.Item1 != TileParamEnum.AdjacentHp) continue;
                        tiles.Add(hpAction.TileId);
                        result = true;
                        break;
                    }
                }
            }
            return result;
        }

        public void RemoveLastEmptyTurnsFromLogicalActions()
        {
            _logicalSimulation.RemoveEmptyCollections();
        }

        public IEnumerable<(int turn, List<Match3ActionBase> actions)> GetAllSimulationSteps()
        {
            return _logicalSimulation;
        }

        public bool HasAnyPendingAction()
        {
            foreach (var (_, actions) in _logicalSimulation)
            {
                foreach (var action in actions)
                {
                    if (action is IMatch3SettleAction { SettleFound: false })
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public bool CheckTurnForAction(int turn, Predicate<Match3ActionBase> predicate)
        {
            foreach (var (turnNumber, actions) in _logicalSimulation)
            {
                if (turnNumber != turn) continue;
                foreach (var action in actions)
                {
                    if (predicate(action))
                    {
                        return true;
                    }
                }
                return false;
            }
            return false;
        }
    }
}
