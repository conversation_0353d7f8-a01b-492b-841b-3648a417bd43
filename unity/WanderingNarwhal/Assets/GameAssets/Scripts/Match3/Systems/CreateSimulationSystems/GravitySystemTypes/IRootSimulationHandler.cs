using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public interface IRootSimulationHandler
    {
        void AddAction(Match3ActionBase newAction);
        void OnTileKindDefined(int tileId, TileKinds kind);
        void HandleMovementAction(Tile tile, Coords coords);
        void FinalHandleMovementAction(Tile tile, Coords coords);
        void DeferredTargetSelected(int uniqueId, Cell targetCell);
        void HandleGondolaMovementAction(Coords startCoords, Coords nextCellCoords, Coords goalCellCoords, int tileId,
            int orientationAngle, bool collectGoal, HitWaitParams hitWaitParams, List<(TileParamEnum, int)> tileParamValues);
    }
}