using System;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary> 
    /// Spawn, SpawnFromSpawner and Move can't be in one Action
    /// </summary>
    [Flags]
    public enum LogicalActionType
    {
        None                = 0b00_000_000_000_000_000_000_000_000_000_000, //0,
        RemoveAll           = 0b00_000_000_000_000_000_000_000_000_000_001, //1,
        Move                = 0b00_000_000_000_000_000_000_000_000_000_010, //2,
        Spawn               = 0b00_000_000_000_000_000_000_000_000_000_100, //4,
        SpawnFromSpawner    = 0b00_000_000_000_000_000_000_000_000_001_000, //8,
        RemoveAdjacentLayer = 0b00_000_000_000_000_000_000_000_000_010_000, //16,
        SwapBoth            = 0b00_000_000_000_000_000_000_000_000_100_000, //32,
        RemoveTile          = 0b00_000_000_000_000_000_000_000_010_000_000, //128,
        AddTile             = 0b00_000_000_000_000_000_000_000_100_000_000, //256,
        RemoveHp            = 0b00_000_000_000_000_000_000_001_000_000_000, //512,
        RemoveBackgroundHp  = 0b00_000_000_000_000_000_000_010_000_000_000, //1024,
        Shuffle             = 0b00_000_000_000_000_000_000_100_000_000_000, //2048,
        RemoveTileGroup     = 0b00_000_000_000_000_000_001_000_000_000_000, //4096,
    }
}