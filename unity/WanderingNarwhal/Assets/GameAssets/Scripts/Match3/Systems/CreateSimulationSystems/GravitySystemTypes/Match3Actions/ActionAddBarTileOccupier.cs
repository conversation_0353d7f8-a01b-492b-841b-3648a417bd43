namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionAddBarTileOccupier : Match3ActionBase
    {
        private Coords _coords;
        private readonly TileSpeciality _tileSpeciality;
        public ActionAddBarTileOccupier(Coords coords, TileSpeciality tileSpeciality)
        {
            _coords = coords;
            _tileSpeciality = tileSpeciality;
            AffectedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var busyTime = _tileSpeciality switch
            {
                TileSpeciality.IceBar => proxy.Settings.IceBarDestroyBusyTime,
                TileSpeciality.MetalBar => proxy.Settings.MetalBarDestroyBusyTime,
                _ => 0f
            };

            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(busyTime, _coords.ToUnityVector2());
            
            foreach (var coord in AffectedCoords)
            {
                ReleasedCoords.Add(coord);
            }
        }
    }
}