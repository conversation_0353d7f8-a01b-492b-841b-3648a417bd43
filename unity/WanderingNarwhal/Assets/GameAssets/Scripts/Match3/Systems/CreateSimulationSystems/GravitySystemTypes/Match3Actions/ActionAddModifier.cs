using BBB.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionAddModifier : Match3ActionBase
    {
        private readonly TileState _mod;
        private readonly Coords _target;
        private readonly Coords _source;
        private readonly int _tileId;

        public ActionAddModifier(Coords target, Coords source, Tile tile, TileState mod)
        {
            _target = target;
            _source = source;
            _mod = mod;
            _tileId = tile.Id;
            if (_mod == TileState.None)
            {
                BDebug.LogError(LogCat.Match3, $"Trying to add empty mod to tile at {target}");
            }

            AffectedCoords.Add(_target);
            if (source != Coords.OutOfGrid)
                AffectedCoords.Add(source);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy, out var cellTile);
        }

        private bool ModifyGrid(Grid grid, PlaySimulationActionProxy proxy, out (Cell cell, Tile tile) cellTile)
        {
            var cell = grid.GetCell(_target);
            var tile = cell.Tile;

            cellTile.cell = cell;
            cellTile.tile = tile;
            
            if (tile.IsNull() || tile.Id != _tileId)
            {
                cell = null;
                tile = null;
                foreach (var c in grid.Cells)
                {
                    if (!c.Tile.IsNull() && c.Tile.Id == _tileId)
                    {
                        cell =c;
                        tile = c.Tile;
                        break;
                    }
                }
            }

            if (tile is null)
            {
                BDebug.LogError(LogCat.Match3, $"Trying to add mod to tile, but tile was not found. Mod={_mod}, tile at {_target}, id=<{_tileId}>");
                
                return false;
            }

            var coords = cell.Coords;
            if (tile.IsAnyOf(_mod))
            {
                BDebug.LogError(LogCat.Match3, $"Trying to add mod to tile, which already have this mod. Mod={_mod}, tile at {_target}, id=<{_tileId}>");
                return false;
            }

            tile.Add(_mod);

            return true;
        }

        /*public override bool WaitingForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.NotFrozen(_tileId);
        }*/

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            foreach (var coord in AffectedCoords)
            {
                ReleasedCoords.Add(coord);
            }

            if (ModifyGrid(grid, proxy, out var cellTile))
            {
                proxy.GoalPanel.OnTileModCreated(_mod);
                var tileView = proxy.TileController.GetOrCreateTileView(cellTile.cell.Tile);
                tileView.UpdateView(cellTile.cell, cellTile.tile, cellTile.cell.Coords);
                tileView.TileMotionController.SetPosAndFreeze(cellTile.cell.Coords.ToUnityVector2());
            }
            
        }
    }

}