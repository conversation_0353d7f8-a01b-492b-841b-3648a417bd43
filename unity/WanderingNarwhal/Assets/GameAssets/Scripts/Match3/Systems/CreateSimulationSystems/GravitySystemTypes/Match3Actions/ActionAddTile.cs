using BBB.Core;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionAddTile : Match3ActionBase
    {
        private readonly Tile _tile;
        private readonly Coords _start;

        public ActionAddTile(Coords start, Tile tile)
        {
            _start = start;
            _tile = tile.Clone();
            
            AffectedCoords.Add(_start);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy);
        }

        private bool ModifyGrid(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCell = grid.GetCell(_start);
            if (!startCell.Tile.IsNull())
            {
                BDebug.LogWarning(LogCat.Match3, $"Trying to add tile when place is already occupied. " +
                                                 $"Tile to add:{_tile.Id} at {_start}, already contains tile:{startCell.Tile.Id}");
                return false;
            }
            startCell.AddTile(_tile);
            grid.TilesSpawnedCount = Mathf.Max(grid.TilesSpawnedCount, startCell.Tile.Id + 1);

            return true;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCell = grid.GetCell(_start);
            var coords = startCell.Coords;

            if (ModifyGrid(grid, proxy))
            {
                var tileView = proxy.TileController.GetOrCreateTileView(startCell.Tile);
                tileView.UpdateView(startCell, _tile, coords);
                tileView.TileMotionController.SetPosAndFreeze(coords.ToUnityVector2());
                proxy.GoalPanel.OnTileCreated(_tile);

                ReleasedCoords.Add(_start);
            }
        }
    }
}