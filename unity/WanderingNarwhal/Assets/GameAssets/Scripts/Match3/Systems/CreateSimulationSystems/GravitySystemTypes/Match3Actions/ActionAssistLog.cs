namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionAssistLog : Match3ActionBase
    {
        private readonly string _str;
        public override bool ShouldBlockOtherActions()
        {
            return false;
        }

        public ActionAssistLog(string str)
        {
            _str = str;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.AssistPanel.AppendToLog(_str);
        }
    }
}