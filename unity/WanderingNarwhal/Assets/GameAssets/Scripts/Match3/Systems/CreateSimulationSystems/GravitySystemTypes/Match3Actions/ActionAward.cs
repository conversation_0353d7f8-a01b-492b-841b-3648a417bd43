using System.Collections.Generic;
using BBB.UI.Level;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionAward : Match3ActionBase
    {
        private static readonly List<Match3CharacterAnim> CharacterAnims = new List<Match3CharacterAnim>
        {
            Match3CharacterAnim.Celebration,
            Match3CharacterAnim.Celebration_02,
        };
        
        private readonly int _matchesCount;
        public override bool ShouldBlockOtherActions()
        {
            return false;
        }

        public ActionAward(int matchesCount)
        {
            _matchesCount = matchesCount;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.AwardRenderer.RenderAwardForCombo(_matchesCount);
            proxy.CharacterAnimator.PlayAnimation(CharacterAnims.GetRandomItem());
        }
    }
}