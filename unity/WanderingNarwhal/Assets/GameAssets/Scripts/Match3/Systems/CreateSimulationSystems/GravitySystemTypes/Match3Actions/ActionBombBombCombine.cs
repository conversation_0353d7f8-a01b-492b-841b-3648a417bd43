using BBB.MMVibrations.Plugins;
using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionBombBombCombine : Match3ActionBase
    {
        private readonly Coords _targetCoord;
        private readonly Coords _fromCoord;

        public ActionBombBombCombine(Coords targetCoord, Coords fromCoord)
        {
            _targetCoord = targetCoord;
            _fromCoord = fromCoord;
            
            AffectedCoords.Add(_fromCoord);
            AffectedCoords.Add(_targetCoord);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;
            var m3BoardSettings = proxy.Settings;
            var bombBombActivationTime = m3BoardSettings.BombBombActivationTime;
            
            
            var tc = proxy.TileController;
            var fromTileView = tc.GetTileViewByCoord(_fromCoord);
            fromTileView.Hide();
            // if (fromTileView != null)
            // {
            //     fromTileView.MoveLayersToBottom();
            // }
            
            var targetTileView = tc.GetTileViewByCoord(_targetCoord);
            targetTileView.Hide();

            // if (targetTileView != null)
            // {
            //     targetTileView.MoveLayersOnTop();
            //     targetTileView.TriggerPreactivationAnimation();
            // }

            proxy.Vibrations.PlayHapticSequence(ImpactPreset.MediumImpact, 10, bombBombActivationTime);
            proxy.FXRenderer.SpawnBombBombCombine(_fromCoord, _targetCoord, bombBombActivationTime);

            Rx.Invoke(bombBombActivationTime, _ =>
            {
                WaitingForCompletion = false;
            });
            
            ReleasedCoords.Add(_fromCoord);
            ReleasedCoords.Add(_targetCoord);
        }
    }
}