using BBB.Audio;
using BBB.GameAssets.Scripts.Match3.Render;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BBB.UI.Level;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionBonusTimeTitle : Match3ActionBase
    {
        private static readonly int HideTrigger = Animator.StringToHash("Hide");
        private const string BonusTimeMatch3PopupText = "BONUS_TIME_MATCH3_AWARD_TEXT";

        private readonly int _remainingMoves;
        private bool _hasCompletedAnimation;
        private Tweener _awardOverlayTweener;
        private Tweener _bonusOverlayTweener;
        public override bool ShouldBlockOtherActions() => false;
        public override bool HasFinishedAsync => HasFinished && _hasCompletedAnimation;

        public ActionBonusTimeTitle(int remainingMoves)
        {
            _remainingMoves = remainingMoves;
            _awardOverlayTweener = null;
            _bonusOverlayTweener = null;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            _awardOverlayTweener?.Kill();
            _awardOverlayTweener = null;

            _bonusOverlayTweener?.Kill();
            _bonusOverlayTweener = null;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var player = proxy.VoiceoverPlayer;
            AudioProxy.PlaySound(Match3SoundIds.AllGoalsDone);
            player.Play(_remainingMoves <= 2 ? M3SpecialVoiceovers.LevelWonNearLoss : M3SpecialVoiceovers.LevelWon, true, out var textUid);

            proxy.GoalPanel.SetAllGoalsFinished();
            proxy.GameController.IsBonusTime = true;
            proxy.GameController.LockInput(true);

            var skipTriggered = false;
            var animationCompleted = false;
            OverlayEffect bonusTimeView = null;

            proxy.InputController.BonusTimeSkipEvent -= SkipBlockedHandler;
            proxy.InputController.BonusTimeSkipEvent += SkipBlockedHandler;

            _awardOverlayTweener = Rx.Invoke(EndGameSettings.BonusAnimPreDelay, _ =>
            {
                proxy.OverlayRenderer.LoadAndLaunchOverlay(Match3ResKeys.BonusTimeOverlayName, OnLaunchedCallback);
                proxy.LevelSkipper.ShowSkipText();
            });
            return;

            void SkipBlockedHandler()
            {
                proxy.LevelSkipper.TriggerTap();
                proxy.Vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                proxy.InputController.BonusTimeSkipEvent -= SkipBlockedHandler;

                if (animationCompleted)
                {
                    proxy.GameController.SimulationEnded();
                }
                else
                {
                    skipTriggered = true;
                    if (bonusTimeView != null)
                    {
                        bonusTimeView.Animator.SetTrigger(HideTrigger);
                    }
                }
            }

            void OnLaunchedCallback(OverlayEffect effectCtrl)
            {
                if (skipTriggered)
                {
                    effectCtrl.Animator.SetTrigger(HideTrigger);
                }
                else
                {
                    bonusTimeView = effectCtrl;
                }

                effectCtrl.SetTitle(textUid);

                AudioProxy.PlaySound(Match3SoundIds.BonusTimePopup);
                _bonusOverlayTweener = Rx.Invoke(EndGameSettings.BonusTimeTitlePause, DelayedAction);

                effectCtrl.AnimationCompleted -= AnimationCompletedHandler;
                effectCtrl.AnimationCompleted += AnimationCompletedHandler;
                _awardOverlayTweener = null;
                return;

                void AnimationCompletedHandler()
                {
                    animationCompleted = true;
                    effectCtrl.AnimationCompleted -= AnimationCompletedHandler;
                    effectCtrl.StopAndClear();
                    _hasCompletedAnimation = true;

                    if (skipTriggered)
                    {
                        proxy.GameController.SimulationEnded();
                    }
                }

                void DelayedAction(long _)
                {
                    proxy.CharacterAnimator.PlayAnimation(Match3CharacterAnim.Celebration_BonusTime);
                    proxy.CharacterAnimator.Lock(true);
                    proxy.AwardRenderer.RenderAward(BonusTimeMatch3PopupText, string.Empty, true);
                    _bonusOverlayTweener = null;
                }
            }
        }
    }
}