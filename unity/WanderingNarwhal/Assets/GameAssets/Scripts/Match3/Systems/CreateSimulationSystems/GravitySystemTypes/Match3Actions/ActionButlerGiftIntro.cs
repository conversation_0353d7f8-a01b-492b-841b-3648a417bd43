using System;
using System.Collections.Generic;
using BebopBee.Core;
using UniRx;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionButlerGiftIntro : Match3ActionBase
    {
        private readonly List<Coords> _butlerBoostCoords = new();
        private readonly List<AutoBoostInstance> _butlerBoosters;

        public ActionButlerGiftIntro(List<Cell> cellsList, List<AutoBoostInstance> butlerBoosters)
        {
            _butlerBoosters = butlerBoosters;

            foreach (var cell in cellsList)
            {
                _butlerBoostCoords.Add(cell.Coords);
            }

            AffectedCoords.UnionWith(_butlerBoostCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.ButlerGiftGlobeController.SetupBoosterData(_butlerBoosters);
            proxy.ButlerGiftGlobeController.Play();

            var waitUntil = new Func<bool>(() => !proxy.ButlerGiftGlobeController.IsActiveAndRunning);

            MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                waitUntil,
                SpawnButlerBooster
            ));
            return;

            void SpawnButlerBooster()
            {
                ReleasedCoords.UnionWith(_butlerBoostCoords);
            }
        }
    }
}