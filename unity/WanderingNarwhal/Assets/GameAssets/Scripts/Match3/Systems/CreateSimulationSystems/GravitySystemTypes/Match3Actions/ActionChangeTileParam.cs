using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionChangeTileParam : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly List<int> _newValue;
        public int TileId { get; }
        public List<(TileParamEnum, int)> TileParam { get; }

        public ActionChangeTileParam(int tileId, Coords coords, List<(TileParamEnum, int)> tileParamValues,
            HitWaitParams hitWaitParams = null)
        {
            TileId = tileId;
            _coords = coords;
            TileParam = tileParamValues;
            
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tileId, coords);
            
            AffectedCoords.Add(_coords);
            
            // Excluding Targeting Mechanics because hitWaitParams.Coords are the source coords in this case (and we don't want
            // to block that coordinate in this case)

            if (hitWaitParams != null && (hitWaitParams.DamageSource & DamageSource.TargetingMechanics) == 0)
            {
                AffectedCoords.Add(hitWaitParams.Coords);
            }
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy, out _);
        }

        private bool ModifyGrid(Grid grid, PlaySimulationActionProxy proxy, out Cell cell)
        {
            if (!grid.TryGetCell(_coords, out cell) || cell.Tile.IsNull() || cell.Tile.Id != TileId)
            {
                cell = null;
                foreach (var c in grid.Cells)
                {
                    if (!c.Tile.IsNull() && c.Tile.Id == TileId)
                    {
                        cell = c;
                        break;
                    }
                }
            }

            if (cell == null)
            {
                BDebug.LogWarning(LogCat.Match3, $"ChangeTileParam action couldn't find tile <{TileId}> at {_coords}");
                return false;
            }

            var tile = cell.Tile;

            if (TileParam is { Count: > 0 })
            {
                foreach (var (tileParamEnum, value) in TileParam)
                {
                    if (tileParamEnum != TileParamEnum.None)
                    {
                        tile.SetParam(tileParamEnum, value);
                    }
                }
            }
            
            grid.RefrehsAllCellsMultisizeCaches();

            return true;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, proxy, out Cell cell))
            {
                var view = proxy.TileController.GetTileViewByCoord(cell.Coords, false);
                if (view != null)
                {
                    proxy.TileController.ForceUpdateTileView(grid, view);
                }
                
                foreach(var coord in AffectedCoords)
                    ReleasedCoords.Add(coord);
            }
        }

        protected override string GetMembersString()
        {
            return $"tileId={TileId} coord={_coords}";
        }
    }
}
