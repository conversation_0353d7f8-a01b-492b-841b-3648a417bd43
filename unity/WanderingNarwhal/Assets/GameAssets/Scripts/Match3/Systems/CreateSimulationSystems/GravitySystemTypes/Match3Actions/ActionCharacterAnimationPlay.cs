using BBB.UI.Level;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionCharacterAnimationPlay : Match3ActionBase
    {
        private readonly Match3CharacterAnim _anim;

        public ActionCharacterAnimationPlay(Match3CharacterAnim anim)
        {
            _anim = anim;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.CharacterAnimator.PlayAnimation(_anim);
        }
    }
}