using BBB.Match3.Logic;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Logic;
using UniRx;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionCollectGoal : Match3ActionBase, InitializeBoardProcessAction
    {
        private readonly GoalTypeTagPair _goal;
        private readonly Coords _from;
        private readonly int _tileId;
        private readonly int? _skin;
        private readonly Coords _coordsOffset;
        private readonly int _matchIndex;
        private readonly int _sameTileCount;
        private readonly DamageSource _damageSource;

        public ActionCollectGoal(GoalTypeTagPair goal, Coords @from, int tileId, int? skin, Coords coordsOffset,
            HitWaitParams hitWaitParams, TileKinds tileKinds)
        {
            _goal = goal;
            _from = from;
            _tileId = tileId;
            _skin = skin;
            _coordsOffset = coordsOffset;
            _damageSource = hitWaitParams?.DamageSource ?? DamageSource.None;
            var matchIds = hitWaitParams?.MatchIds;
            _matchIndex = matchIds?.IndexOf(tileId) ?? -1;
            _sameTileCount = hitWaitParams?.SameTileMatchSet?[tileKinds]?.Count ?? 0;
            
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tileId, from);
            
            AffectedCoords.Add(_from);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (_goal.GoalType == GoalType.None)
                return;
            
            if (proxy.TilesUnderLongSpawningAnimation.ContainsKey(_tileId))
            {
                //var pos = proxy.TilesUnderLongSpawningAnimation[tile.Id];
                bool Predicate()
                {
                    return !proxy.TilesUnderLongSpawningAnimation.ContainsKey(_tileId);
                }

                //TODO: Revisit this. -VK
                void VisualizeGoalProgressAction()
                {
                    // If long spawn animation existed for the destroyed tile,
                    // then we must spawn goal from the position of spawn animation,
                    // not from the current position of the tile (which was not visualized on screen). -VK
                    proxy.GoalPanel.VisualizeGoalProgressIfNeeded(_goal, _damageSource, _from, _tileId, _skin, _coordsOffset, _matchIndex, _sameTileCount);
                }

                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(Predicate, action: VisualizeGoalProgressAction));
            }
            else
            {
                proxy.GoalPanel.VisualizeGoalProgressIfNeeded(_goal, _damageSource, _from, _tileId, _skin, _coordsOffset, _matchIndex, _sameTileCount);
            }
            
            ReleasedCoords.Add(_from);
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tileId} from={_from}";
        }

        public void ExecuteForBoardInitialize(PlaySimulationActionProxy proxy)
        {
            proxy.GoalPanel.VisualizeGoalProgressIfNeeded(_goal, _damageSource, _from, _tileId, _skin, _coordsOffset, _matchIndex);
        }
    }
}