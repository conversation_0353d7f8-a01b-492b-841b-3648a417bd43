namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionCombineBoltRegularTile : Match3ActionBase
    {
        private readonly Coords _first;
        private readonly float _boltAnimDuration;

        public ActionCombineBoltRegularTile(Coords first, float boltAnimDuration)
        {
            _first = first;
            _boltAnimDuration = boltAnimDuration;
            AffectedCoords.Add(_first);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var firstTileView = proxy.TileController.GetTileViewByCoord(_first, false);
            if (firstTileView != null)
            {
                proxy.FXRenderer.SpawnBoltRegularTileCombine(proxy.Settings, firstTileView, _boltAnimDuration);
            }

            ReleasedCoords.Add(_first);
        }
    }
}