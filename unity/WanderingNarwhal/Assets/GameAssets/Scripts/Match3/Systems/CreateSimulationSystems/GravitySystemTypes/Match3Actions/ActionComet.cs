using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionComet : Match3ActionBase
    {        
        private readonly Coords _targetCoords;

        public ActionComet(Coords targetCoords)
        {
            _targetCoords = targetCoords;
            
            AffectedCoords.Add(_targetCoords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.FXRenderer.ReduceOneTurn();
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var timeForComet = proxy.Settings.TimeForComet;
            var timeBetweenComets = proxy.Settings.TimeBetweenComets;
            proxy.FXRenderer.SpawnComet(_targetCoords, timeForComet);
            WaitingForCompletion = true;
            Rx.Invoke(timeBetweenComets, UnpauseAction);

            void UnpauseAction(long _)
            {
                ReleasedCoords.Add(_targetCoords);
                WaitingForCompletion = false;
            }

            Rx.Invoke(timeForComet, CometHit);
            void CometHit(long _)
            {
                proxy.GridController.MoveGlassBehind(_targetCoords);
                var tileView = proxy.TileController.GetTileViewByCoord(_targetCoords);
                proxy.TileController.ForceUpdateTileView(grid, tileView);
            }
        }
    }
}