using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionDestroyShelfItem : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly HitWaitParams _hitWaitParams;
        private readonly HashSet<Coords> _connectedCoords;

        public ActionDestroyShelfItem(Coords coords, HashSet<Coords> connectedCoords, HitWaitParams hitWaitParams)
        {
            _coords = coords;
            _hitWaitParams = hitWaitParams;
            _connectedCoords = connectedCoords;
            AffectedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.GoalPanel.VisualizeGoalProgressIfNeeded(GoalType.Shelf, _hitWaitParams.DamageSource, _coords);
            ReleasedCoords.Add(_coords);

            foreach (var coord in _connectedCoords)
            {
                if (grid.TryGetCell(coord, out var cell) &&
                    cell.HasTile() && cell.Tile.GetParam(TileParamEnum.AdjacentHp) == ShelfTile.ShelfHp)
                {
                    var tileView = proxy.TileController.GetTileViewByCoord(coord, false);
                    if (tileView != null)
                    {
                        tileView.TriggerShelfItemShake();
                    }
                }
            }
        }
    }
}