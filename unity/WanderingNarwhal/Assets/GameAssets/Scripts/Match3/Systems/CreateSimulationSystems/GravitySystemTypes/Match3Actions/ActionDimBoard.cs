using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionDimBoard : Match3ActionBase
    {
        private readonly bool _dimValue;

        public ActionDimBoard(bool dimValue)
        {
            _dimValue = dimValue;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var delay = proxy.Settings.DimTime;
            if (delay <= 0) return;

            WaitingForCompletion = true;
            proxy.GridController.DimBoard(_dimValue);
            Rx.Invoke(delay, WaitingForCompletionAction);

            void WaitingForCompletionAction(long _)
            {
                WaitingForCompletion = false;
            }
        }
    }
}