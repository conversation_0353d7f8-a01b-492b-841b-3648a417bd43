using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionDynamiteBlast : Match3ActionBase
    {
        private Coords _sourceCoords;
        private readonly int _whirlpoolId;
        private readonly List<Coords> _coordsList;

        public ActionDynamiteBlast(int whirlpoolId, List<Coords> coordsList, Coords sourceCoords)
        {
            _whirlpoolId = whirlpoolId;
            _coordsList = coordsList;
            _sourceCoords = sourceCoords;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var centerPosition = _sourceCoords.ToUnityVector2();
            foreach (var coord in _coordsList)
            {
                var distance = coord.DistanceFrom(centerPosition);
                var lifeTime = distance / proxy.Settings.DynamiteBlastSpeed;
                proxy.TileTickPlayer.BoardObjectFactory.CreateWhirlpoolTarget(_whirlpoolId, coord,
                    lifeTime + proxy.Settings.DynamiteBoxDestroyBusyTime, null);
            }
        }
    }
}