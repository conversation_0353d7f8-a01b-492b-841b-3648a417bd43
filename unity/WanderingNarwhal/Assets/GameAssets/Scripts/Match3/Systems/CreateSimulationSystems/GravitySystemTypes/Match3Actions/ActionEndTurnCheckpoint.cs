namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary>
    /// This action is used as a checkpoint, so that actions executed after winning the level are executed 1 by 1
    /// </summary>
    public class ActionEndTurnCheckpoint : Match3ActionBase
    {
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            // do nothing
        }

        public override bool ShouldWaitForAsyncActions()
        {
            return true;
        }
    }
}