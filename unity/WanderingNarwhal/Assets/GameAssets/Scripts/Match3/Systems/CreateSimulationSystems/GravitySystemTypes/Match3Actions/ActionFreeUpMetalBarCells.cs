using BBB.CellTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionFreeUpMetalBarCells : Match3ActionBase
    {
        private readonly Coords _sourceCoords;

        public ActionFreeUpMetalBarCells(Coords sourceCoords)
        {
            _sourceCoords = sourceCoords;
            AffectedCoords.Add(_sourceCoords);
        }

        private void ModifyGrid(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (!grid.TryGetCell(_sourceCoords, out var cell)) return;
            cell.MetalBarStatus = false;
            cell.Remove(CellState.MetalBar);
            proxy.CellController.Update(cell);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy);
            ReleasedCoords.Add(_sourceCoords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy);
        }
    }
}