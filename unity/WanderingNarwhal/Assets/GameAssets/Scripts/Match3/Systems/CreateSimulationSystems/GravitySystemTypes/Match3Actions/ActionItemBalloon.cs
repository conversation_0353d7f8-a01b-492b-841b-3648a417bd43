using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionItemBalloon : Match3ActionBase
    {
        private readonly List<Coords> _coordsList;
        private readonly int _pause;

        public ActionItemBalloon(List<Coords> coordsList, int pause)
        {
            _pause = pause;
            _coordsList = coordsList;
            
            AffectedCoords.UnionWith(_coordsList);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            // var totalTime = _pause * Match3Settings.SpeedAdjustedDeltaTime;
            proxy.FXRenderer.SpawnBalloon(_coordsList, _pause*0.15f);
            ReleasedCoords.UnionWith(_coordsList);
        }
    }
}