using BebopBee.Core;
using DG.Tweening;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public struct LightningRecord
    {
        public Coords Coords;
        public TileKinds TileKind;
    }
    
    public sealed class ActionLightning : Match3ActionBase
    {
        private Tweener _tweener;
        private readonly LightningRecord _lightningRecord;
        private readonly int _offset;

        public ActionLightning(LightningRecord lightningRecords, int offset)
        {
            _lightningRecord = lightningRecords;
            _offset = offset;
            AffectedCoords.Add(_lightningRecord.Coords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            _tweener?.Kill();
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var timeForLightning = proxy.Settings.TimeForOneLightning;
            var timeBetweenLightnings = proxy.Settings.TimeBetweenLightnings;
            var timeOffset = _offset == -1 ? timeBetweenLightnings : timeBetweenLightnings * _offset;
            
            _tweener?.Kill();
            _tweener = Rx.Invoke(timeOffset, _ =>
            {
                proxy.FXRenderer.SpawnLightningFromSky(_lightningRecord.Coords,
                    _lightningRecord.TileKind, timeForLightning,
                    () =>
                    {
                        ReleasedCoords.Add(_lightningRecord.Coords);
                    });
            });
        }
    }
}