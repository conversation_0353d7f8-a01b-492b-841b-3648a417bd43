using BBB.Audio;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionMatchesSound : Match3ActionBase
    {
        private static readonly string[] _cascadeSoundIds;

        private readonly int _matchIndex;
        private readonly int _longestMatchLength;
        private readonly bool _longestMatchCorner;

        static ActionMatchesSound()
        {
            _cascadeSoundIds = new[]
            {
                Match3SoundIds.Cascade1,
                Match3SoundIds.Cascade2,
                Match3SoundIds.Cascade3,
                Match3SoundIds.Cascade4,
                Match3SoundIds.Cascade5,
                Match3SoundIds.Cascade6,
            };
        }

        public ActionMatchesSound(int matchIndex, int longestMatchLength, bool longestMatchCorner)
        {
            _matchIndex = matchIndex;
            _longestMatchLength = longestMatchLength;
            _longestMatchCorner = longestMatchCorner;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            //proxy.FXRenderer.SpawnMatch(grid, _matches);

            if (_matchIndex <= 0)
            {
                if (_longestMatchLength > 0)
                {
                    if (_longestMatchLength == 3)
                    {
                        AudioProxy.PlaySound(Match3SoundIds.Match3);
                    }
                    else if (_longestMatchLength == 4)
                    {
                        AudioProxy.PlaySound(_longestMatchCorner ? Match3SoundIds.MatchCorner : Match3SoundIds.Match4);
                    }
                    else if (_longestMatchLength > 4)
                    {
                        AudioProxy.PlaySound(Match3SoundIds.Match5);
                    }
                }
            }
            else
            {
                var arrayIndex = (_matchIndex - 1) % _cascadeSoundIds.Length;
                var soundId = _cascadeSoundIds[arrayIndex];
                AudioProxy.PlaySound(soundId);
            }
        }
        
        public override bool ShouldBlockOtherActions()
        {
            return false;
        }
    }
}