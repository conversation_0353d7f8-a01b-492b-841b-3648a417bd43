namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionMove : Match3ActionBase, IFreeFallTargetPriority
    {
        public int TileId => _tileId;
        public Coords Coords => _end;

        private readonly Coords _start;
        private readonly Coords _end;
        private readonly int _tileId;

        public ActionMove(Coords start, Coords end, Tile tile, int jumpingThroughGapOfSize)
        {
            _start = start;
            _end = end;
            _tileId = tile.Id;
            
            AffectedCoords.Add(_start);
            AffectedCoords.Add(_end);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCell = grid.GetCell(_start);
            var endMoveCell = grid.GetCell(_end);
            var tile = startCell.Tile;
            var tileView = proxy.TileController.GetTileViewById(_tileId, false);

            if (tileView == null)
            {
                if (proxy.TileController.IsTileViewDestroyed(_tileId))
                    return true;
            }
            
            return !tile.IsNull() && tile.Id == _tileId && endMoveCell.CanAcceptTile() && tileView != null;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }

        private void ModifyGrid(Grid grid)
        {
            var startCell = grid.GetCell(_start);
            var endMoveCell = grid.GetCell(_end);
            var tile = startCell.Tile;
            endMoveCell.AddTile(tile);
            startCell.HardRemoveTile(0);
            ReleasedCoords.Add(_start);
            ReleasedCoords.Add(_end);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tileId} startCoord={_start} endCoord={_end}";
        }
    }
}