using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionMoveDirectionalBooster : Match3ActionBase
    {
        private Coords _coords;
        private readonly CardinalDirections _direction;
        private readonly int _boosterId;
        private readonly List<Coords> _allCoords;

        public ActionMoveDirectionalBooster(Coords coords, CardinalDirections direction,
            int boosterId, List<Coords> coordsList)
        {
            _coords = coords;
            _direction = direction;
            _boosterId = boosterId;
            AffectedCoords.Add(_coords);
            _allCoords = coordsList;
            AffectedCoords.UnionWith(coordsList);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var factory = proxy.TileTickPlayer.BoardObjectFactory;
            
            var directionalBoosterTargets = new List<DirectionalBoosterTarget>();

            foreach (var coords in _allCoords)
            {
                var target = factory.CreateDirectionBoosterTarget(_boosterId, coords);
                directionalBoosterTargets.Add(target);
            }
            
            ReleasedCoords.UnionWith(AffectedCoords);
            
            var message = proxy.EventDispatcher.GetMessage<BoosterUsedEvent>();
            switch (_direction)
            {
                case CardinalDirections.E:
                    message.Set(BoosterItem.Horizontal);
                    proxy.EventDispatcher.TriggerEvent(message);

                    factory.CreateHorizontalBooster(_boosterId, directionalBoosterTargets, _direction.ToVector2(),
                        _coords.ToUnityVector2());
                    proxy.FXRenderer.ShakeBoard(_coords, ShakeSettingsType.HorizontalBooster);
                    AudioProxy.PlaySound(Match3SoundIds.HorizontalBooster);
                    break;
                
                case CardinalDirections.N:
                    message.Set(BoosterItem.Vertical);
                    proxy.EventDispatcher.TriggerEvent(message);

                    proxy.LevelRevealer.HideBottomPanel();
                    
                    Rx.Invoke(proxy.Settings.VerticalBoosterBoardHideDelay, _ =>
                    {
                        factory.CreateVerticalBooster(_boosterId, directionalBoosterTargets, _direction.ToVector2(),
                            _coords.ToUnityVector2());
                        proxy.FXRenderer.ShakeBoard(_coords, ShakeSettingsType.VerticalBooster);
                        AudioProxy.PlaySound(Match3SoundIds.VerticalBooster);
                    });

                    Rx.Invoke(proxy.Settings.VerticalBoosterBoardRevealDelay, _ =>
                    {
                        proxy.LevelRevealer.RevealBottomPanel();
                    });
                       
                    break;
            }
        }
        
        protected override string GetMembersString()
        {
            return $"coords={_coords}";
        }
    }
}