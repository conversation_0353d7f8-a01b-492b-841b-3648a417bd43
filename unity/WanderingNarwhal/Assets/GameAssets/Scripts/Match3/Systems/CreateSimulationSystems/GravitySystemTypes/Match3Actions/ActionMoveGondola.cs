using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;
using BBB.Core;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionMoveGondola : Match3ActionBase
    {
        private Coords _startCoords;
        private Coords _targetCoords;
        private Coords _goalCoords;
        private bool _goalCollected;
        public readonly int TileId;
        public readonly List<Coords> Path;
        private readonly List<int> _orientation;
        private readonly HitWaitParams _hitWaitParams;
        private readonly bool _fastSpeed;
        private List<(TileParamEnum, int)> _tileParams;

        public ActionMoveGondola(Coords startCoords, Coords targetCoords, Coords goalCoords, int tileId,
            int orientation, bool goalCollected, HitWaitParams hitWaitParams, bool fastSpeed,
            List<(TileParamEnum, int)> tileParamValues)
        {
            _startCoords = startCoords;
            _targetCoords = targetCoords;
            _goalCollected = goalCollected;
            _hitWaitParams = hitWaitParams;
            _goalCoords = goalCoords;
            TileId = tileId;
            _fastSpeed = fastSpeed;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tileId, _startCoords);
            AffectedCoords.Add(_startCoords);
            AffectedCoords.Add(_targetCoords);
            Path = new List<Coords>
            {
                _startCoords,
                _targetCoords
            };
            _orientation = new List<int>
            {
               orientation
            };
            _tileParams = tileParamValues;
        }

        public void AppendToPath(Coords targetCoords, Coords goalCoords, int orientation, bool goalCollected,
            List<(TileParamEnum, int)> tileParamValues)
        {
            AffectedCoords.Remove(_targetCoords);
            _targetCoords = targetCoords;
            AffectedCoords.Add(_targetCoords);
            
            _goalCoords = goalCoords;
            Path.Add(_targetCoords);
            
            _orientation.Add(orientation);
            _goalCollected = goalCollected;
            _tileParams = tileParamValues;
        }
        

        private bool ModifyGrid(Grid grid, out Cell targetCell)
        {
            targetCell = grid.GetCell(_targetCoords);
            var startCell = grid.GetCell(_startCoords);

            if (targetCell == null || startCell == null)
            {
                BDebug.LogErrorFormat(LogCat.Match3,"Cell {0} not found during move gondola", _startCoords);
                return false;
            }

            targetCell.ReplaceTile(startCell.Tile);
            startCell.HardRemoveTile(0);
            
            if (_tileParams is { Count: > 0 })
            {
                foreach (var (tileParamEnum, value) in _tileParams)
                {
                    if (tileParamEnum != TileParamEnum.None)
                    {
                        targetCell.Tile.SetParam(tileParamEnum, value);
                    }
                }
            }
            
            grid.RefrehsAllCellsMultisizeCaches();
            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out _);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (_startCoords.Equals(_targetCoords))
            {
                ReleasedCoords.UnionWith(AffectedCoords);
                return;
            }
            
            if (ModifyGrid(grid, out var cell))
            {
                ReleasedCoords.Add(_startCoords);
                
                var tileView =  proxy.TileController.GetOrCreateTileView(cell.Tile);
                tileView.TriggerGondolaMovement(Path, _orientation, _goalCollected, cell.Tile, _fastSpeed, _goalCoords, ()=> OnMoveDone(tileView, _targetCoords));
            }
            
            void OnMoveDone(TileView tileView,Coords targetCoords)
            {
                tileView.TileMotionController.SetPosAndFreeze(targetCoords.ToUnityVector2());

                var goalCell = grid.GetCell(_goalCoords);
                if (goalCell != null && _goalCollected && goalCell.IsAnyOf(CellState.FlagEnd))
                {
                    proxy.GoalPanel.VisualizeGoalProgressIfNeeded(GoalType.Gondola, _hitWaitParams.DamageSource, goalCell.Coords);
                }
                ReleasedCoords.Add(targetCoords);
            }
        }
        
        protected override string GetMembersString()
        {
            return $"tileId={TileId} coords={_startCoords}";
        }

        public bool ShouldAppendToPath(HitWaitParams hitWaitParams)
        {
            var isPropellerGondola = (_hitWaitParams?.DamageSource & DamageSource.Gondola) != 0 &&
                                     (hitWaitParams?.DamageSource & DamageSource.Gondola) != 0;
            var isNotPropellerGondola = (_hitWaitParams?.DamageSource & DamageSource.Gondola) == 0 &&
                                        (hitWaitParams?.DamageSource & DamageSource.Gondola) == 0;
            return isPropellerGondola || isNotPropellerGondola;
        }
    }
}