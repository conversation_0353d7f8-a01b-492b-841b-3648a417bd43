using System;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Settings;
using UniRx;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionMoveTukTuk : Match3ActionBase
    {
        private Coords _coords;
        private readonly CardinalDirections _direction;
        private readonly int _tuktukId;
        private readonly int _tuktukColor;
        private readonly List<Coords> _allCoords;
        private readonly HitWaitParams _hitWaitParams;

        public ActionMoveTukTuk(Coords coords, int currentColor, CardinalDirections distances,
            int tuktukId, List<Coords> coordsList, HitWaitParams hitWaitParams)
        {
            _coords = coords;
            _direction = distances;
            _tuktukId = tuktukId;
            _tuktukColor = currentColor;
            AffectedCoords.Add(_coords);
            _allCoords = coordsList;
            _hitWaitParams = hitWaitParams;
            AffectedCoords.UnionWith(coordsList);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var factory = proxy.TileTickPlayer.BoardObjectFactory;
            
            var tuktukTargets = new List<TukTukTarget>();

            foreach (var coords in _allCoords)
            {
                var target = factory.CreateTukTukTarget(_tuktukId, coords, null);
                tuktukTargets.Add(target);
            }
            
            var waitUntil = new Func<bool>(() => true);
            
            if (_hitWaitParams != null)
            {
                var waitCondition = ActionWaitConditionFactory.Create(_hitWaitParams);
                waitUntil = () => !waitCondition.WaitForExpectedState(grid, proxy);
            }

            if (waitUntil())
            {
                SpawnTukTuk();
            }
            else
            {
                // // Do not spawn rocket until waiting condition is met
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                    waitUntil,
                    SpawnTukTuk
                ));
            }

            void SpawnTukTuk()
            {
                ReleasedCoords.UnionWith(AffectedCoords);
                factory.CreateTukTuk(_tuktukId, _tuktukColor, tuktukTargets, _direction.ToVector2(), _coords.ToUnityVector2());
            }
            
            proxy.FXRenderer.ShakeBoard(_coords, ShakeSettingsType.TukTuk);
            AudioProxy.PlaySound(Match3SoundIds.TukTukMove);
        }
        
        protected override string GetMembersString()
        {
            return $"coords={_coords}";
        }
    }
}