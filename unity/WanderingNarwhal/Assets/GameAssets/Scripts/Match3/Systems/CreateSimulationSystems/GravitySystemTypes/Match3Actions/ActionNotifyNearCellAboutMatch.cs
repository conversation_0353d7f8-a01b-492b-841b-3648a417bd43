namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionNotifyNearCellAboutMatch : Match3ActionBase
    {
        private readonly Coords _coords;

        public ActionNotifyNearCellAboutMatch(Coords coords, HitWaitParams hitWaitParams)
        {
            _coords = coords;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            
            AffectedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.TilesFeedbackNotificationsHandler.NotifyNearCellsAboutMatch(grid, _coords);
            
            ReleasedCoords.Add(_coords);
        }
    }
}