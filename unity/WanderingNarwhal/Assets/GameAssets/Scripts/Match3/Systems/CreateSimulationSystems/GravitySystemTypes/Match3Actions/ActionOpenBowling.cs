using System.Collections.Generic;
using BBB.Match3.Renderer;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionOpenBowling : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly TileKinds _kind;
        private readonly int _radius;

        private readonly List<Coords> _damageArea;

        public ActionOpenBowling(int tileId, Coords coords, HitWaitParams hitWaitParams = null)
        {
            _coords = coords;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tileId, coords);
            
            AffectedCoords.Add(_coords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tile = grid.GetCell(_coords).Tile;
            if (tile == null)
                return;

            tile.SetParam(TileParamEnum.BowlingOpened, 1);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tile = grid.GetCell(_coords).Tile;
            if (tile == null)
                return;

            var tileView =  proxy.TileController.GetOrCreateTileView(tile);
            if (tileView == null)
                return;

            tile.SetParam(TileParamEnum.BowlingOpened, 1);
            tileView.AnimateLayerViews(_coords, TileLayerViewAnims.Unapply, TileLayerViewAnimParams.None);
            
            ReleasedCoords.Add(_coords);
        }
    }
}
