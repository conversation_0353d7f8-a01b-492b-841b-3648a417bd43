using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPauseForCoord : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly float _pauseDuration;

        public ActionPauseForCoord(Coords coords, float pauseDuration)
        {
            _coords = coords;
            _pauseDuration = pauseDuration;
            AffectedCoords.Add(coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            Rx.Invoke(_pauseDuration, _ =>
            {
                ReleasedCoords.Add(_coords);
            });
        }
    }
}