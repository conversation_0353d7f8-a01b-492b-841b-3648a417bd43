using System.Collections.Generic;
using BBB.Core;
using GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.Match3Actions;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionPerformTileMovement : Match3ActionBase, IMatch3SettleAction
    {
        public int TileId => _tileId;
        private readonly int _tileId;
        
        private readonly Coords _startCoords;
        private readonly List<Coords> _path;
        private readonly string _tileDebugString;
        private Coords? _settleCoords;
        private bool _hasSettled;

        public override bool HasFinishedAsync => HasFinished && _hasSettled;
        public bool SettleFound => _settleCoords.HasValue;

        public ActionPerformTileMovement(Tile tile, Coords startCoords)
        {
            _tileId = tile.Id;
            _startCoords = startCoords;
            _path = new List<Coords>{startCoords};
            _tileDebugString = tile.ToString();
            
            AffectedCoords.Add(_startCoords);
            
            WaitCondition = new NotFrozenWaitCondition(_tileId, _startCoords);
        }
        
        protected override string GetMembersString()
        {
            return $"startcoord={_startCoords}" +
                   $" settlecoords={(_settleCoords.HasValue ? _settleCoords.Value : Coords.OutOfGrid)}" +
                   $" tileId={_tileId} path={string.Join(",", _path)}";
        }

        public void AppendToPath(Coords coords)
        {
            if(_path.Count == 0 || _path[^1] != coords)
                _path.Add(coords);
        }

        public void SetNextSettleCoord(Coords coords)
        {
            _settleCoords = coords;
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tileView = proxy.TileController.GetTileViewById(_tileId, false);

            if (tileView == null)
            {
                return proxy.TileController.IsTileViewDestroyed(_tileId);
            }

            return base.CanExecute(grid, proxy);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (_settleCoords.HasValue)
            {
                proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_settleCoords.Value, _tileId);
            }
            ReleasedCoords.Add(_startCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tileView = proxy.TileController.GetTileViewById(_tileId, false);

            if (tileView == null)
            {
                if (proxy.TileController.IsTileViewDestroyed(_tileId))
                {
                    if(_settleCoords.HasValue)
                        proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_settleCoords.Value, _tileId);
                    
                    ReleasedCoords.Add(_startCoords);
                    _hasSettled = true;
                    return;
                }
                
                BDebug.LogError(LogCat.Match3, "Tile is null in ActionPerformTileMovement but not destroyed");
            }
            
            if (_settleCoords.HasValue)
            {
                tileView.TileMotionController.MakeFreeFallToTarget(_path, _settleCoords.Value,
                    _tileId, OnSettled);
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Settle coord is not specified for tile movement " +
                                                $"startCoord={_startCoords} tile={_tileDebugString}");
            }
            
            ReleasedCoords.Add(_startCoords);
            return;

            void OnSettled()
            {
                if (_settleCoords.HasValue)
                {
                    proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_settleCoords.Value, _tileId);
                }
                _hasSettled = true;
            }
        }
    }
}