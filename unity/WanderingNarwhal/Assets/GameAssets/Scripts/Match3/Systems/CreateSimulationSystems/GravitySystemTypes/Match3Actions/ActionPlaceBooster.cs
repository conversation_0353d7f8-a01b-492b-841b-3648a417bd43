using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionPlaceBooster : Match3ActionBase
    {
        private readonly Coords _cellPos;
        private readonly int _index;
        private readonly bool _isButlerBoost; 
        private readonly string _boosterName;

        public ActionPlaceBooster(Coords cellPos, int tileId, int index, bool isButlerBoost, string boosterName)
        {
            _cellPos = cellPos;
            _index = index;
            _isButlerBoost = isButlerBoost; 
            _boosterName = boosterName;
            AffectedCoords.Add(cellPos);
            WaitCondition = ActionWaitConditionFactory.Create(null, tileId, cellPos);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var boostRevealSettings = proxy.Settings.BoardRevealSettings.GetBoostersRevealSettings(_isButlerBoost);
            var pauseBetween = boostRevealSettings.PauseBetweenBoosters;
            var revealTime = boostRevealSettings.BoosterFlightTime;
            var alphaFadeInTime = boostRevealSettings.AlphaFadeInTime;
            var startScale = boostRevealSettings.StartScale;
            var movementCurve = boostRevealSettings.ScaleCurve;
            var fxTrailDuration = boostRevealSettings.FxTrailLifetime;
            
            if (grid.TryGetCell(_cellPos, out var cell) && !ReferenceEquals(cell.Tile, null))
            {
                var tileView = proxy.TileController.GetExistingTileView(cell.Tile.Id);
                if (tileView != null)
                {
                    tileView.SetAlpha(0f);
                    tileView.TileMotionController.Clean();
                    tileView.TileMotionController.SetPosAndFreeze(cell.Coords.ToUnityVector2());
                    Rx.Invoke(pauseBetween * _index, _ =>
                    {
                        if (_isButlerBoost && proxy.ButlerGiftManager.ShowGlobeIntro)
                        {
                            proxy.ButlerGiftGlobeController.HideImage(_boosterName, alphaFadeInTime);
                            var finalPos = proxy.GridController.ToDisplacedWorldPosition(tileView.Coords.ToUnityVector2());
                            var followingTransform = proxy.ButlerGiftGlobeController.GetBoosterPath(_boosterName, finalPos);
                            tileView.SpawnFromPosition(tileView.LocalPosition, revealTime, followingTransform,
                                fxTrailDuration);
                        }

                        tileView.FadeInAppear(startScale, revealTime, alphaFadeInTime, movementCurve);
                        ReleasedCoords.Add(_cellPos);
                    });
                }
            }
        }
    }
}