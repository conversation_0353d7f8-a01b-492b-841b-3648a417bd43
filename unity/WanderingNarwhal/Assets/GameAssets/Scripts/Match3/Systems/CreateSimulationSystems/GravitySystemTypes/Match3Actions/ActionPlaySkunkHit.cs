using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPlaySkunkHit : Match3ActionBase, IDeferredTargetSelection
    {
        private readonly Coords _skunkPos;
        private Cell _targetCell;
        public int UniqueId { get; }
        public bool SettleFound => _targetCell != null;

        public ActionPlaySkunkHit(int skunkId, Coords skunkPos, HitWaitParams hitWaitParams, int sourceTileId)
        {
            _skunkPos = skunkPos;
            UniqueId = skunkId;
            AffectedCoords.Add(skunkPos);
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, sourceTileId, skunkPos);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var view = proxy.TileController.GetTileViewByCoord(_skunkPos, false);

            if (view != null)
            {
                view.TriggerSkunkHitTileAtPos(_targetCell.Coords, CompleteAction);
            }
            else
            {
                CompleteAction();
            }

            return;

            void CompleteAction()
            {
                proxy.Vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                proxy.TileTickPlayer.BoardObjectFactory.CreateSkunkTarget(_targetCell.Coords, UniqueId);
                ReleasedCoords.Add(_skunkPos);
            }
        }
        
        public void SetTargetCell(Cell targetCell)
        {
            _targetCell = targetCell;
        }

        public Coords GetTargetCoords()
        {
            return _targetCell?.Coords ?? Coords.OutOfGrid;
        }
    }
}