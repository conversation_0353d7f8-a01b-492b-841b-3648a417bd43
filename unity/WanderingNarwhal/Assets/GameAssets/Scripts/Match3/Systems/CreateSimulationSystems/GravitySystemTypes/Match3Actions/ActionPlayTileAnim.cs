using BBB.Core;
using BBB.Match3.Renderer;
using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPlayTileAnim : Match3ActionBase
    {
        private readonly Coords _coord;
        private readonly TileLayerViewAnims _anim;
        private readonly float _delay;

        public ActionPlayTileAnim(Coords coord, TileLayerViewAnims anim, float delay = 0f)
        {
            _coord = coord;
            _anim = anim;
            _delay = delay;
            
            AffectedCoords.Add(_coord);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var cell = grid.GetCell(_coord);
            if (cell.Tile.IsNull())
            {
                BDebug.LogWarning(LogCat.Match3, $"Trying to make tile anim at pos: {_coord}. Tile does not exists there.");
                return;
            }

            var tileView = proxy.TileController.GetTileViewByCoord(_coord, false);
            if (tileView != null)
            {
                if (cell.Tile.Speciality == TileSpeciality.Propeller)
                {
                    tileView.MoveLayersOnTop();
                }

                tileView.AnimateLayerViews(_coord, _anim, TileLayerViewAnimParams.None);
            }
            
            // This delay accounts for the tile animation duration 
            Rx.Invoke(_delay, _ => ReleasedCoords.Add(_coord));
        }
    }
}