using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPropellerComboFlight : Match3ActionBase, IDeferredTargetSelection
    {
        private readonly int _propellerId;
        private Coords _propellerCoords;
        private readonly Coords _otherCoords;
        private Coords _targetCoords;
        private readonly TileSpeciality _otherTileSpec;
        private PlaySimulationActionProxy _proxy;
        private Grid _grid;

        private Cell _targetCell;
        public int UniqueId => _propellerId;
        public bool SettleFound => _targetCell != null;
        private int _targetTileId;

        public ActionPropellerComboFlight(int propellerId, Coords propellerCoords, Coords otherCoords,
            TileSpeciality otherTileSpec)
        {
            _propellerId = propellerId;
            _propellerCoords = propellerCoords;
            _otherCoords = otherCoords;
            _otherTileSpec = otherTileSpec;

            AffectedCoords.Add(_propellerCoords);
            AffectedCoords.Add(_otherCoords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            _proxy.TileTickPlayer.DeleteObjects(obj => obj is PropellerTarget pt && pt.PropellerId == _propellerId);
            ReleasedCoords.Add(_propellerCoords);
            ReleasedCoords.Add(_otherCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var multiplierDeterminer = new TrajectoryMultiplierDeterminer(grid);
            proxy.FXRenderer.StartFlightForPropellerCombo(_propellerCoords, _otherCoords,
                _targetCoords, multiplierDeterminer, OnPropellerLaunched, OnPropellerReached, _targetTileId);
            proxy.TileTickPlayer.BoardObjectFactory.CreatePropellerTarget(_targetCoords, _propellerId);
            _grid = grid;
            _proxy = proxy;
            
            var damageArea = new DamageArea(_otherTileSpec, _targetCoords, _grid);
            foreach (var coords in damageArea.GetAllCoords())
            {
                proxy.TileTickPlayer.BoardObjectFactory.CreatePropellerTarget(coords, _propellerId);
            }
            
            return;
            
            void OnPropellerLaunched()
            {
                ReleasedCoords.Add(_propellerCoords);
                ReleasedCoords.Add(_otherCoords);
            }
        }

        private void OnPropellerReached()
        {
            if (_otherTileSpec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker)
            {
                _proxy.FXRenderer.SpawnPropellerLinebreakerImpact(_grid, _targetCoords);
            }
            
            _proxy.TileTickPlayer.DeleteObjects(obj => obj is PropellerTarget pt && pt.PropellerId == _propellerId);
            _proxy = null;
            _grid = null;
        }

        public void SetTargetCell(Cell targetCell)
        {
            _targetCell = targetCell;
            _targetCoords = _targetCell.Coords;
            _targetTileId = targetCell != null && targetCell.Tile != null ? targetCell.Tile.Id : 0;
        }
    }
}