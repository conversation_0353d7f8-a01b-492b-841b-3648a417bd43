using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPropellerFlight : Match3ActionBase, IDeferredTargetSelection
    {
        private Coords _sourceCoords;
        private Cell _targetCell;
        private readonly int _propellerId;
        private readonly int _propellerComboIndex;

        public int UniqueId => _propellerId;
        public bool SettleFound => _targetCell != null;

        public ActionPropellerFlight(int sourceTileId, Coords sourceCoords, int propellerId,
            HitWaitParams hitWaitParams, int propellerComboIndex)
        {
            _sourceCoords = sourceCoords;
            _propellerId = propellerId;
            _propellerComboIndex = propellerComboIndex;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, sourceTileId, _sourceCoords);
            
            AffectedCoords.Add(_sourceCoords);
        }

        protected override string GetMembersString()
        {
            return $"sourceCoords={_sourceCoords}";
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj =>
                obj is PropellerTarget pt && pt.PropellerId == _propellerId);
            ReleasedCoords.Add(_sourceCoords);
            if (_targetCell != null)
            {
                ReleasedCoords.Add(_targetCell.Coords);
            }
        }
        
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            AudioProxy.PlaySound(Match3SoundIds.PropellerStartFly);
            var settings = proxy.Settings.GetSettingsForEffect<PropellerEffectSettings>(FxType.PropellerFlight);
            var targetPos = _targetCell.Coords.ToUnityVector2();
            targetPos = Tile.GetCenterPointForIfSquareMechanic(grid, _targetCell, targetPos, settings.PropellerTargetCenterOfSquareMechanic);
            
            var distance = _sourceCoords.DistanceFrom(targetPos);
            var duration = settings.GetFlightDuration(distance);
            var cellSize = proxy.TilesResources.CellSize;
            var trajDeterminer = new TrajectoryMultiplierDeterminer(grid);
            
            proxy.FXRenderer.SpawnPropellerFlight(_propellerComboIndex, trajDeterminer,
                _sourceCoords, _targetCell.Coords, targetPos, cellSize, duration, OnPropellerLaunched, OnPropellerReached);
            
            return;
            
            void OnPropellerLaunched()
            {
                ReleasedCoords.Add(_sourceCoords);
            }

            void OnPropellerReached()
            {
                proxy.TileTickPlayer.BoardObjectFactory.CreatePropellerTarget(_targetCell.Coords, _propellerId);
            }
        }

        public void SetTargetCell(Cell targetCell)
        {
            _targetCell = targetCell;
        }

        public Coords GetTargetCoords()
        {
            return _targetCell?.Coords ?? Coords.OutOfGrid;
        }

        public int GetPropellerId()
        {
            return _propellerId;
        }
    }
}
