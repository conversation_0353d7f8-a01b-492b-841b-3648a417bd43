using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionRealtimePause : Match3ActionBase
    {
        private readonly float _pause;

        public ActionRealtimePause(float pause) 
        {
            _pause = pause;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;

            void WaitingForCompletionAction(long _)
            {
                WaitingForCompletion = false;
            }

            Rx.Invoke(_pause, WaitingForCompletionAction);
        }
    }
}