using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Core;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionReduceTntCount : Match3ActionBase
    {
        private const float NormalDistance = 250f;
        private const float DelayBetweenNumbers = 0.1f;
        private const int StackSize = 2;

        private readonly Coords _tntCoords;
        private readonly Coords _sourceCoords;
        private readonly DamageSource _damageSource;

        private static readonly Dictionary<Coords, Queue<Cell>> CellsUpdatedStateStack = new(StackSize);

        public ActionReduceTntCount(Coords tntCoords, Coords sourceCoords, int sizeX, int sizeY,
            HitWaitParams hitWaitParams = null)
        {
            _tntCoords = tntCoords;
            _sourceCoords = sourceCoords;
            _damageSource = hitWaitParams?.DamageSource ?? DamageSource.None;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            AffectedCoords.Add(_sourceCoords);
            GetTntCoords(AffectedCoords, sizeX, sizeY);
        }

        private void GetTntCoords(ISet<Coords> coordsSet, int sizeX, int sizeY)
        {
            var topRight = _tntCoords + new Coords(sizeX - 1, sizeY - 1);
            for (var x = _tntCoords.X; x <= topRight.X; x++)
            for (var y = _tntCoords.Y; y <= topRight.Y; y++)
            {
                coordsSet.Add(new Coords(x, y));
            }
        }

        public override bool ShouldExecuteAfterAllPreviousFinished()
        {
            return false;
        }

        private bool ModifyGrid(Grid grid, out bool modified, out Cell outCell)
        {
            modified = false;
            if (!grid.TryGetCell(_tntCoords, out var cell))
            {
                BDebug.LogError(LogCat.Match3, "Cell not found at " + _tntCoords);
                outCell = cell;
                return false;
            }

            outCell = cell;

            if (cell.TntCount <= 0)
            {
                modified = true;
                cell.Remove(CellState.Tnt);
                cell.TntCount = 0;
                cell.SizeX = 0;
                cell.SizeY = 0;
                grid.RefrehsAllCellsMultisizeCaches();
            }
            else
            {
                cell.TntCount--;
            }

            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out _, out _);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            grid.TryGetCell(_tntCoords, out var originalCell);
            var sizeX = originalCell.SizeX;
            var sizeY = originalCell.SizeY;
            var coords = originalCell.Coords;
            var targetType = originalCell.TntTarget;
            var kind = originalCell.TntKind;

            var success = ModifyGrid(grid, out var modified, out var cell);

            if (!success)
                return;

            if (modified)
            {
                var bombBusyTime = proxy.Settings.TntExplosionBusyTime;

                Rx.Invoke(bombBusyTime,
                    _ => { proxy.GoalPanel.VisualizeGoalProgressIfNeeded(GoalType.Tnt, _damageSource, _tntCoords); });
            }

            var queue = CellsUpdatedStateStack.GetValueOrDefault(_tntCoords);
            if (queue == null)
            {
                queue = new Queue<Cell>(5);
                CellsUpdatedStateStack[_tntCoords] = queue;
            }

            // We keep cell state in static stack,
            // because we need to ensure that order of data in callbacks invocation will be strict.
            // For example, if this method is called in a streak for tnt states 2, 1, 0,
            // but OnTargetReached invocation happen with states 2, 0, 1,
            // then this would result in destroying TNT (at state 0), and then re-appearing on grid with state 1.
            // stack resolves this ordering issue.

            queue.Enqueue(cell.Clone());

            proxy.TileResourceSelector.GetAppearFlySettings(TileAsset.Tnt, out var fx, out var settings);

            if (modified)
            {
                OnTargetReachedCallback(-1);
                return;
            }
            
            proxy.TileResourceSelector
                .GetTntTargetGoalSpriteAsync(targetType, kind, proxy.TilesResources)
                .ContinueWith(goalIcon =>
                {
                    var from = proxy.GridController.ToDisplacedLocalPosition(_sourceCoords);
                    var localToWorld = proxy.GridController.Transform.localToWorldMatrix;
                    from = localToWorld.MultiplyPoint(from);
                    var to0 = proxy.GridController.ToDisplacedLocalPosition(_tntCoords);
                    var tntTopRightCornerCoords = _tntCoords + new Coords(sizeX - 1, sizeY - 1);
                    var to1 = proxy.GridController.ToDisplacedLocalPosition(tntTopRightCornerCoords);
                    var tntCenterLocalPosition = Vector3.Lerp(to0, to1, 0.5f);
                    var tntCenterWorldPosition = localToWorld.MultiplyPoint(tntCenterLocalPosition);
                    var durationNormalFactor = ((Vector2) tntCenterWorldPosition - from).magnitude / NormalDistance;
                    var duration = settings.DurationFirstPart * durationNormalFactor;
                    var totalDuration = duration + settings.DurationSecondPart * durationNormalFactor + settings.StartDelay;
                    var flySettings = new GoalIconFlySettings
                    {
                        startDelay = settings.StartDelay,
                        fromPos = from,
                        midPos = settings.IsMiddlePointInCenterOfScreen ? proxy.GridController.GetCenterPosition() : from + settings.RelateiveOffset,
                        toPos = tntCenterWorldPosition,
                        flightDuration = duration,
                        flightDurationFirstSegment = duration,
                        flightDurationSecondSegment = settings.DurationSecondPart * durationNormalFactor,
                        motionFirstSegment = settings.StartAnimCurve,
                        motionSecondSegment = settings.EndAnimCurve,
                        motionSecondaryX = settings.ParalelMotionXCurve,
                        motionSecondaryY = settings.ParalelMotionYCurve,
                        motionSecondSecondaryX = settings.ParalelSecondMotionXCurve,
                        motionSecondSecondaryY = settings.ParalelSecondMotionYCurve,
                        startSoundDelay = settings.SoundDelay,
                        startSoundUid = settings.StartSoundUid,
                        factoryMethod = FactoryMethod,
                        onTargetReachedCallback = OnTargetReachedCallback,
                        releaseMethod = ReleaseMethod
                    };

                    GoalIconFlyAnimator.Instance.Launch(flySettings);
                    ReleasedCoords.UnionWith(AffectedCoords);
                    return;

                    GameObject FactoryMethod()
                    {
                        var effect = proxy.FXRenderer.SpawnFx(fx);
                        var tf = effect.transform;

                        var normalTile = effect.GetComponent<NormalTileRenderer>();
                        if (normalTile != null)
                        {
                            normalTile.SetData(new TileViewData
                            {
                                Sprite = goalIcon,
                                CellSize = proxy.TilesResources.CellSize
                            });                        }

                        if (settings.ScaleType == GoalIconFlySettingsScriptableObject.ScaleCurveType.Relative)
                        { 
                            if (settings.UseSeparateScaleCurves)
                            {
                                GoalIconFlySettingsScriptableObject.TweenScaleRelativeOnSeparateAxis(tf, 
                                    settings.ScaleCurveX, settings.ScaleCurveY, totalDuration);
                            }
                            else
                            {
                                GoalIconFlySettingsScriptableObject.TweenScaleRelative(tf, settings.ScaleCurve, totalDuration);
                            }
                        }
                        else
                        {
                            GoalIconFlySettingsScriptableObject.TweenScaleAbsolute(tf, settings.ScaleCurve,
                                totalDuration);
                        }

                        if (settings.UseShadow)
                        {
                            proxy.FXRenderer.SpawnFlyShadow(tf as RectTransform, totalDuration, settings.ShadowOffset, settings.ScaleCurve);
                        }
                        
                        return effect;
                    }

                    void ReleaseMethod(GameObject go) => go.Release();
                    
                }).Forget();
            
            return;
            
            void OnTargetReachedCallback(int index)
            {
                if (index == -1)
                {
                    MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(() => queue.Count == 1,
                        () => { UpdateVisualState(true); }));
                }
                else
                {
                    if (settings.HapticPreset != ImpactPreset.None)
                    {
                        proxy.FXRenderer.PlayHapticFeedback(settings.HapticPreset, true);
                    }

                    UpdateVisualState(false);
                }
            }
            
            void UpdateVisualState(bool destroy)
            {
                Rx.Invoke(DelayBetweenNumbers, _ =>
                {
                    var visualState = CellsUpdatedStateStack[_tntCoords].Count == 0 ? cell : CellsUpdatedStateStack[_tntCoords].Dequeue();

                    proxy.CellController.Update(visualState);

                    if (destroy)
                    {
                        ShakeAllTilesAndPlayDestroyEffect(grid, proxy, coords, sizeX, sizeY);
                    }
                    else
                    {
                        ReleasedCoords.UnionWith(AffectedCoords);
                    }
                });
            }
        }

        private void ShakeAllTilesAndPlayDestroyEffect(Grid grid, PlaySimulationActionProxy proxy,
            Coords cellCoords, int sizeX, int sizeY)
        {
            for (var x = 0; x < sizeX; x++)
            {
                for (var y = 0; y < sizeY; y++)
                {
                    var coords = new Coords(cellCoords.X + x, cellCoords.Y + y);

                    proxy.FXRenderer.SpawnConfigurableEffectWithCustomParameters(coords.ToUnityVector2(),
                        FxType.TntDestroy, new FxOptionalParameters {x = sizeX, y = sizeY}, releaseTime: 3f);

                    if (!grid.TryGetCell(coords, out var affectedCell)) continue;

                    var tileView = proxy.TileController.GetTileViewByCoord(affectedCell.Coords);
                    if (tileView != null)
                    {
                        Rx.Invoke(proxy.Settings.TntShakeMechanicsDelay, _ =>
                        {
                            if (!tileView.Animator.IsAnyPlaying(StateType.Fall))
                            {
                                tileView.ShakeWithSiblings();
                            }
                        });
                    }
                }
            }

            Rx.Invoke(proxy.Settings.TntShakeMechanicsDelay, _ => { ReleasedCoords.UnionWith(AffectedCoords); });
        }
    }
}