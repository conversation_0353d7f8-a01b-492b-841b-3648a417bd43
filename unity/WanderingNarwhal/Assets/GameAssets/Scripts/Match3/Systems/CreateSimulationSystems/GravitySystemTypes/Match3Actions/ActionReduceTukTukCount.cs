using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionReduceTukTukCount : Match3ActionBase
    {
        private const float NormalDistance = 250f;

        private const float DelayBetweenNumbers = 0.05f;

        private readonly Coords _tuktukCoords;
        private readonly Coords _sourceCoords;

        private static readonly Dictionary<Coords, Queue<Cell>> CellsUpdatedStateStack = new(2);

        public ActionReduceTukTukCount(Coords tuktukCoords, Coords sourceCoords, HitWaitParams hitWaitParams = null)
        {
            _tuktukCoords = tuktukCoords;
            _sourceCoords = sourceCoords;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);

            AffectedCoords.Add(_sourceCoords);
            AffectedCoords.Add(_tuktukCoords);
        }

        public override bool ShouldExecuteAfterAllPreviousFinished()
        {
            return false;
        }

        private bool ModifyGrid(Grid grid, out bool modified, out Cell outCell, out TileKinds tileKind)
        {
            modified = false;
            tileKind = TileKinds.Blue;
            if (!grid.TryGetCell(_tuktukCoords, out var cell))
            {
                BDebug.LogError(LogCat.Match3, "Cell not found at " + _tuktukCoords);
                outCell = cell;
                return false;
            }

            outCell = cell;
            
            if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk)
            {
                tileKind = (TileKinds) cell.Tile.GetParam(TileParamEnum.TukTukColor);
                var currentCount = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                currentCount--;
                if (currentCount <= 0)
                {
                    modified = true;
                    cell.Tile.SetParam(TileParamEnum.TukTukCount, 0);
                }
                else
                {
                    cell.Tile.SetParam(TileParamEnum.TukTukCount, currentCount);
                }
            }

            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out _, out _, out _);
        }
        
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            grid.TryGetCell(_tuktukCoords, out _);

            var success = ModifyGrid(grid, out var modified, out var cell, out var tileKind);

            if (!success)
            {
                return;
            }

            var queue = CellsUpdatedStateStack.GetValueOrDefault(_tuktukCoords);
            
            if (queue == null)
            {
                queue = new Queue<Cell>(5);
                CellsUpdatedStateStack[_tuktukCoords] = queue;
            }

            // We keep cell state in static stack,
            // because we need to ensure that order of data in callbacks invocation will be strict.
            // For example, if this method is called in a streak for tnt states 2, 1, 0,
            // but OnTargetReached invocation happen with states 2, 0, 1,
            // then this would result in destroying TukTuk (at state 0), and then re-appearing on grid with state 1.
            // stack resolves this ordering issue.
            
            queue.Enqueue(cell.Clone());
            
            proxy.TileResourceSelector
                .GetTntTargetGoalSpriteAsync(TntTargetType.Simple, tileKind, proxy.TilesResources)
                .ContinueWith(goalIcon =>
                { 
                    proxy.TileResourceSelector.GetAppearFlySettings(TileAsset.TukTuk, out var fx, out var settings); 
                    var from = proxy.GridController.ToDisplacedLocalPosition(_sourceCoords);
                    var localToWorld = proxy.GridController.Transform.localToWorldMatrix;
                    from = localToWorld.MultiplyPoint(from);
                    var to0 = proxy.GridController.ToDisplacedLocalPosition(_tuktukCoords);
                    var tntCenterWorldPosition = localToWorld.MultiplyPoint(to0);
                    var durationNormalFactor = ((Vector2)tntCenterWorldPosition - from).magnitude / NormalDistance;
                    var duration = settings.DurationFirstPart * durationNormalFactor;
                    var totalDuration = duration + settings.DurationSecondPart * durationNormalFactor + settings.StartDelay;
                    var flySettings = new GoalIconFlySettings()
                    {
                        startDelay = settings.StartDelay,
                        fromPos = from,
                        midPos = settings.IsMiddlePointInCenterOfScreen ? proxy.GridController.GetCenterPosition() : from + settings.RelateiveOffset,
                        toPos = tntCenterWorldPosition,
                        flightDuration = duration,
                        flightDurationFirstSegment = duration,
                        flightDurationSecondSegment = settings.DurationSecondPart * durationNormalFactor,
                        motionFirstSegment = settings.StartAnimCurve,
                        motionSecondSegment = settings.EndAnimCurve,
                        motionSecondaryX = settings.ParalelMotionXCurve,
                        motionSecondaryY = settings.ParalelMotionYCurve,
                        motionSecondSecondaryX = settings.ParalelSecondMotionXCurve,
                        motionSecondSecondaryY = settings.ParalelSecondMotionYCurve,
                        startSoundDelay = settings.SoundDelay,
                        startSoundUid = settings.StartSoundUid,
                        factoryMethod = FactoryMethod,
                        onTargetReachedCallback = OnTargetReachedCallback,
                        releaseMethod = ReleaseMethod,
                    };

                    GoalIconFlyAnimator.Instance.Launch(flySettings);

                    Rx.Invoke(proxy.Settings.TukTukCollectionDelay, _ =>
                    {
                        ReleasedCoords.Add(_sourceCoords);
                    });
                    
                    if (!modified)
                    {
                        Rx.Invoke(proxy.Settings.TukTukCollectionDelay, _ => { ReleasedCoords.Add(_tuktukCoords); });
                    }

                    return;

                    GameObject FactoryMethod()
                    {
                        var effect = proxy.FXRenderer.SpawnFx(fx);
                        var tf = effect.transform;

                        var normalTile = effect.GetComponent<NormalTileRenderer>();
                        if (normalTile != null)
                        {
                            normalTile.SetData(new TileViewData
                            {
                                Sprite = goalIcon,
                                CellSize = proxy.TilesResources.CellSize
                            });
                        }

                        if (settings.ScaleType == GoalIconFlySettingsScriptableObject.ScaleCurveType.Relative)
                        {
                            if (settings.UseSeparateScaleCurves)
                            {
                                GoalIconFlySettingsScriptableObject.TweenScaleRelativeOnSeparateAxis(tf, 
                                    settings.ScaleCurveX, settings.ScaleCurveY, totalDuration);
                            }
                            else
                            {
                                GoalIconFlySettingsScriptableObject.TweenScaleRelative(tf, settings.ScaleCurve, totalDuration);
                            }
                        }
                        else
                        {
                            GoalIconFlySettingsScriptableObject.TweenScaleAbsolute(tf, settings.ScaleCurve, totalDuration);
                        }

                        if (settings.UseShadow)
                        {
                            proxy.FXRenderer.SpawnFlyShadow(tf as RectTransform, totalDuration, settings.ShadowOffset, settings.ScaleCurve);
                        }

                        return effect;
                    }

                    void OnTargetReachedCallback(int index)
                    {
                        if (settings.HapticPreset != ImpactPreset.None)
                        {
                            proxy.FXRenderer.PlayHapticFeedback(settings.HapticPreset, true);
                        }

                        // delay for the TukTuk counter to display the number
                        Rx.Invoke(DelayBetweenNumbers, _ =>
                        {
                            Cell visualState;
                            if (queue.Count == 0)
                            {
                                BDebug.LogError(LogCat.Match3, "Stack of tuktuk states is empty");
                                visualState = cell;
                            }
                            else
                            {
                                visualState = queue.Dequeue();
                            }
                            var view = proxy.TileController.GetTileViewByCoord(visualState.Coords, false);
                            proxy.TileController.ForceUpdateCurrentState(visualState, view);
                            if (modified)
                            {
                                AudioProxy.PlaySound(Match3SoundIds.TukTukRev);
                                Rx.Invoke(proxy.Settings.TukTukDestroyDelay, _ => { ReleasedCoords.Add(_tuktukCoords); });
                            }
                        });
                    }

                    void ReleaseMethod(GameObject go) => go.Release();
                }).Forget();
        }
    }
}
