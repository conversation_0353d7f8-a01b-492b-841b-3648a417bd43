using BBB.Audio;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionRemoveBackgroundHp : Match3ActionBase
    {
        private readonly Coords _start;
        private readonly GoalType _affectedGoal;
        private readonly bool _isIvyHit;
        private readonly CellState _state;
        private readonly int _bgCount;
        private readonly DamageSource _damageSource;

        public ActionRemoveBackgroundHp(Cell cell, GoalType affectedGoal, bool isIvyHit, HitWaitParams hitWaitParams)
        {
            _start = cell.Coords;
            _affectedGoal = affectedGoal;
            _isIvyHit = isIvyHit;
            _state = cell.State;
            _bgCount = cell.BackgroundCount;
            _damageSource = hitWaitParams?.DamageSource ?? DamageSource.None;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            AffectedCoords.Add(_start);
            if (_isIvyHit && cell.HasMultiSizeCellReference())
            {
                var mainCell = cell.GetMainCellReference(out _);
                foreach (var referencedCell in mainCell.ReferencedCells)
                {
                    AffectedCoords.Add(referencedCell.Coords);
                }
            }
        }

        private bool ModifyGrid(Grid grid, out Cell cell)
        {
            cell = grid.GetCell(_start);

            if (cell == null)
                return false;

            if (_isIvyHit)
            {
                if (cell.HasMultiSizeCellReference())
                {
                    var mainCell = cell.GetMainCellReference(out _);
                    foreach (var referencedCell in mainCell.ReferencedCells)
                    {
                        referencedCell.IvyCount = 0;
                        referencedCell.UpdateCellBackgroundState();
                    }
                }
                else
                {
                    cell.IvyCount = 0;
                    cell.UpdateCellBackgroundState();
                }
            }
            else
            {
                cell.BackgroundCount--;
                cell.UpdateCellBackgroundState();
            }
            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out _);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            bool success = ModifyGrid(grid, out Cell cell);

            if (!success)
                return;

            SpawnDestroyFx(cell, _state, _bgCount, _isIvyHit, proxy);

            if (cell.BackgroundCount <= 1 || cell.IvyCount == 0)
            {
                AudioProxy.PlaySound(Match3SoundIds.BackgroundDestroy);
            }

            if (_isIvyHit)
            {
                Rx.Invoke(proxy.Settings.IvyDestroyDelay, _=>
                {
                    ReleasedCoords.UnionWith(AffectedCoords);
                });
            }
            else
            {
                ReleasedCoords.UnionWith(AffectedCoords);
            }
        }

        private void SpawnDestroyFx(Cell cell, CellState state, int background, bool isIvyHit, PlaySimulationActionProxy proxy)
        {
            if ((state & CellState.Ivy) != 0 && isIvyHit)
            {
                if (cell.HasMultiSizeCellReference())
                {
                    var mainCell = cell.GetMainCellReference(out _);
                    foreach (var referencedCell in mainCell.ReferencedCells)
                    {
                        UpdateOverlayCell(referencedCell, FxType.IvyDestroy);
                    }
                }
                else
                {
                    UpdateOverlayCell(cell, FxType.IvyDestroy);
                }
            }
            else if ((state & (CellState.BackOne | CellState.BackDouble)) != 0)
            {
                var fx = background == 2 ? FxType.BackgroundDoubleRemove : FxType.BackgroundRemove;
                UpdateBackgroundCell(cell, fx);
            }
            else if ((state & CellState.Petal) != 0)
            {
                UpdateBackgroundCell(cell, FxType.PetalRemove);
            }

            void UpdateBackgroundCell(Cell cellToUpdate, FxType fxType)
            {
                proxy.FXRenderer.SpawnSingleAnimationEffect(cellToUpdate.Coords, fxType, 4f);
                proxy.CellController.Update(cellToUpdate);
                if (_affectedGoal != GoalType.None)
                {
                    proxy.GoalPanel.VisualizeGoalProgressIfNeeded(_affectedGoal, _damageSource, cellToUpdate.Coords);
                }
            }
            
            void UpdateOverlayCell(Cell cellToUpdate, FxType fxType)
            {
                if (_affectedGoal != GoalType.None)
                {
                    proxy.GoalPanel.VisualizeGoalProgressIfNeeded(_affectedGoal, _damageSource, cellToUpdate.Coords);
                }
                    
                proxy.FXRenderer.SpawnSingleAnimationEffect(cellToUpdate.Coords, fxType);
                proxy.CellController.Update(cellToUpdate);
            }
        }
    }
}
