using BBB.Match3.Logic;
using GameAssets.Scripts.Match3.Logic;
using BBB.CellTypes;
using BBB.Match3.Renderer;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionRemoveDestructibleWall : Match3ActionBase
    {
        private readonly GoalType _affectedGoal;
        private readonly DamageSource _damageSource;
        private readonly Coords _damagedCoords;
        private readonly int _wallCount;

        public ActionRemoveDestructibleWall(Coords damagedCoords, int wallCount, HitWaitParams hitWaitParams)
        {
            _damageSource = hitWaitParams?.DamageSource ?? DamageSource.None;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            _damagedCoords = damagedCoords;
            _wallCount = wallCount;
            AffectedCoords.Add(_damagedCoords);
        }

        private bool ModifyGrid(Grid grid, out Cell cell)
        {
            cell = grid.GetCell(_damagedCoords);

            if (cell == null)
                return false;
            
            foreach (var destructibleWall in cell.DestructibleWalls.DestructibleWall)
            {
                if ((_damageSource & DamageSource.TukTuk) != 0)
                {
                    destructibleWall.Count = 0;
                }
                else
                {
                    destructibleWall.Count = destructibleWall.Count > 0 ? --destructibleWall.Count : 0;
                }
            }

            var count = cell.GetDestructibleWallCount();
            if (count == 0)
            {
                cell.Remove(CellState.DestructibleWall);
            }
            
            return true;
        }
        
        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out _);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (!ModifyGrid(grid, out var cell))
            {
                ReleasedCoords.Add(_damagedCoords);
                return;
            }
                
            if (_wallCount == 0)
            {
                proxy.GoalPanel.VisualizeGoalProgressIfNeeded(GoalType.DestructibleWall, _damageSource, _damagedCoords);
            }

            var fxType = _wallCount switch
            {
                3 => FxType.DestructibleWallTripleRemove,
                2 => FxType.DestructibleWallDoubleRemove,
                _ => FxType.DestructibleWallSingleRemove
            };

            ReleasedCoords.Add(_damagedCoords);
            proxy.CellController.Update(cell);
            proxy.FXRenderer.SpawnSingleAnimationEffect(_damagedCoords, fxType);
        }
    }
}
