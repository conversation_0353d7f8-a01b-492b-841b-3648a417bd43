using BBB.Core;
using BBB.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionRemoveHp : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly Coords? _adjacentCoords;
        private readonly int _tileId;

        public ActionRemoveHp(Coords coords, Tile tile, HitWaitParams hitWaitParams)
        {
            _coords = coords;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tile.Id, coords);
            _tileId = tile.Id;
            
            AffectedCoords.Add(_coords);
            if (hitWaitParams?.DamageSource == DamageSource.Adjacent)
            {
                _adjacentCoords = hitWaitParams.Coords;
                AffectedCoords.Add(_adjacentCoords.Value);
            }
        }

        private bool ModifyGrid(Grid grid)
        {
            
            Cell cell = grid.FindCellByTileId(_tileId);
            
            if(cell == null) 
                cell = grid.GetCell(_coords);
            
            var tile = cell.Tile;
            if (tile != null)
            {
                if (tile.Id != _tileId)
                {
                    BDebug.LogError(LogCat.Match3, $"Removing tile<{tile.Id}> with unexpected id {_tileId} at cell {_coords}");
                    return false;
                }
                else
                {
                    cell.Tile.ReduceHitPoints();
                    cell.Tile.OnBeforeDieReaction();
                    return true;

                }
            }
            else
            {
                BDebug.LogWarning(LogCat.Match3, "Tile not found in cell " + _coords);
                return false;
            }
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid))
            {
                Cell cell = grid.FindCellByTileId(_tileId);
                proxy.TileController.UpdateCount(cell);
            }
            
            ReleasedCoords.Add(_coords);
            if (_adjacentCoords.HasValue)
                ReleasedCoords.Add(_adjacentCoords.Value);
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tileId} coords={_coords}";
        }
    }
}