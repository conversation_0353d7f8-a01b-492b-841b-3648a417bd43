using BBB.Match3.Logic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionRemoveState : Match3ActionBase, InitializeBoardProcessAction
    {
        private readonly int _tileId;
        private readonly Coords _start;
        private readonly GoalType _affectedGoal;
        private readonly TileState _state;
        private readonly DamageSource _damageSource;
        public TileState targetState => _state;
        
        /// <summary>
        /// Remove tile state action constructor.
        /// </summary>
        /// <param name="start">Coords.</param>
        /// <param name="state">Mod to remove.</param>
        public ActionRemoveState(int tileId, Coords start, TileState state, 
            HitWaitParams hitWaitParams, GoalType affectedGoal = GoalType.None)
        {
            _tileId = tileId;
            _start = start;
            _state = state;
            _affectedGoal = affectedGoal;
            _damageSource = hitWaitParams?.DamageSource ?? DamageSource.None;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, tileId, _start);
            
            AffectedCoords.Add(_start);
        }

        private bool ModifyGrid(Grid grid, out Tile tile)
        {
            var cell = grid.FindCellByTileId(_tileId);
            tile = null;
            
            if(cell == null)
                cell = grid.GetCell(_start);
            
            if (cell == null)
            {
                UnityEngine.Debug.LogError("VisualActionAnimalHide: could not find cell at " + _start);
                return false;
            }

            if (ReferenceEquals(cell.Tile, null))
            {
                UnityEngine.Debug.LogWarning("VisualActionAnimalHide: could not find tile at " + _start);
                return false;
            }

            tile = cell.Tile;
            tile.Remove(_state);
            
            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out var tile);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, out Tile tile))
            {
                var tileView = proxy.TileController.GetTileViewById(tile.Id);
                proxy.TileController.ForceUpdateTileView(grid, tileView);

                if (_affectedGoal != GoalType.None)
                {
                    proxy.GoalPanel.OnTileModRemoved(_start, _state, _damageSource); 
                }

                ReleasedCoords.Add(_start);
            }
        }

        public void ExecuteForBoardInitialize(PlaySimulationActionProxy proxy)
        {
            if (_affectedGoal != GoalType.None)
            {
                proxy.GoalPanel.OnTileModRemoved(_start, _state, _damageSource); 
            }
        }
    }
}