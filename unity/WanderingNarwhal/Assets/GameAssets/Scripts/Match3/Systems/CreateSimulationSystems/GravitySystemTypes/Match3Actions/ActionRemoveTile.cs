using System;
using BBB.Match3.Debug;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionRemoveTile : Match3ActionBase
    {
        private readonly Coords _target;
        private readonly HitWaitParams _hitWaitParams;
        private readonly int _tileId;
        private readonly TileState _tileState;

        private readonly bool _byAutomatch;

        public bool DoesTileStateSatisfy(Predicate<TileState> predicate)
        {
            return predicate(_tileState);
        }

        public bool IsTileOfState(TileState state)
        {
            return (_tileState & state) != 0;
        }

        public ActionRemoveTile(Coords target, Tile tile, HitWaitParams hitWaitParams)
        {
            if (tile.IsNull())
            {
#if BBB_LOG
                M3Debug.LogError("[LA]: RemoveTile. Tile is null!!!");
#endif
                return;
            }

            _target = target;
            _hitWaitParams = hitWaitParams;
            _tileId = tile.Id;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, _tileId, _target);
            _tileState = tile.State;

#if BBB_DEBUG
            _byAutomatch = tile.IsAnyOf(TileState.AutomatchProduct);
#endif

            AffectedCoords.Add(_target);
        }


        protected override string GetMembersString()
        {
            return $"tileId={_tileId} coords={_target}";
        }

        private bool ModifyGrid(Grid grid, PlaySimulationActionProxy proxy, out int tileId)
        {
            var targetCell = grid.FindCellByTileId(_tileId);

            tileId = -1;
            if (targetCell == null)
            {
                ReleasedCoords.Add(_target);
                return false;
            }

            var tile = targetCell.Tile;
            if (SimulationPlayUtils.CheckTileForRemoval(tile, _target))
            {
                ReleasedCoords.Add(_target);
                return false;
            }

            var message = proxy.EventDispatcher.GetMessage<TileCollectedEvent>();
            message.Set(targetCell.Tile);
            proxy.EventDispatcher.TriggerEvent(message);

            tileId = targetCell.Tile.Id;

            SimulationPlayUtils.AddTileToRemoval(_target);

            targetCell.HardRemoveTile(0, _hitWaitParams);

            if (targetCell.HasMultiSizeCellReference())
            {
                grid.RefrehsAllCellsMultisizeCaches();
            }

            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, proxy, out var tileId);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, proxy, out var tileId))
            {

#if BBB_LOG
                if (_byAutomatch)
                {
                    var str = $"<color=green>AUTOMATCH REMOVE AT {_target}</color>";
                    proxy.AssistPanel.AppendToLog(str);
                }
#endif

                if (tileId != -1)
                {
                    proxy.TileController.ReleaseTileWhenCan(tileId, _target);
                }

                proxy.TileTickPlayer.BoardObjectFactory.NotifyTileRemoved(tileId);

                ReleasedCoords.Add(_target);
            }
        }
    }
}