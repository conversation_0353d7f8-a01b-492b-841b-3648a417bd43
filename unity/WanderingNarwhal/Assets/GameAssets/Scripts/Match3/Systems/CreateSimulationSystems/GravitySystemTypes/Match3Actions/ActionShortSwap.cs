
using System;
using BBB.Match3.Renderer;
using Cysharp.Threading.Tasks;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionShortSwap : Match3ActionBase
    {
        private readonly Coords _start;
        private readonly Coords _end;

        public ActionShortSwap(Coords start, Coords end)
        {
            _start = start;
            _end = end;
            WaitingForCompletion = true;
            
            AffectedCoords.Add(_start);
            AffectedCoords.Add(_end);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var gridController = proxy.GridController;
            if (gridController == null || !gridController.HasTile(_start))
            {
                ReleaseCoords();
                return;
            }
            var tileView = proxy.TileController?.GetTileViewByCoord(_start);
            if (tileView == null || tileView.Animator.IsAnyPlaying(StateType.All))
            {
                ReleaseCoords();
                return;
            }

            var dir = (_end - _start).ToUnityVector2() * proxy.TilesResources.CellSize;
            var duration = 1 / proxy.Settings.SwapSpeed;
            tileView.Animator.ShortSwipe(dir, duration);

            UniTask.Delay((int)Math.Round(duration * 1000)).ContinueWith(ReleaseCoords);
        }

        private void ReleaseCoords()
        {
            ReleasedCoords.Add(_start);
            ReleasedCoords.Add(_end);

            WaitingForCompletion = false;
        }
    }

}