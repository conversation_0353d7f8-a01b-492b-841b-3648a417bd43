namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSkunkExplosion : Match3ActionBase
    {
        private readonly SkunkActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly int _skunkId;
        private readonly Cell _targetCell;

        public ActionSkunkExplosion(int skunkId, Cell cell)
        {
            _activeObjectPredicate = new SkunkActiveBoardObjectPredicate
            {
                SkunkId = skunkId
            };
            _skunkId = skunkId;
            _targetCell = cell;
            AffectedCoords.Add(_targetCell.Coords);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) &&
                   base.CanExecute(grid, proxy);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj => obj is SkunkTarget st && st.SkunkId == _skunkId);
            ReleasedCoords.Add(_targetCell.Coords);
        }
    }
}