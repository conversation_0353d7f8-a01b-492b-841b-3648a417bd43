using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSmallCrossExplosion : Match3ActionBase
    {
        private readonly Coords _pos;
        private readonly List<Coords> _crossCoords;
        private readonly CardinalDirections _dirsToInclude;

        public ActionSmallCrossExplosion(Coords coords, List<Coords> crossCoords, CardinalDirections dirsToInclude, HitWaitParams hitWaitParams)
        {
            _pos = coords;
            _crossCoords = crossCoords;
            _dirsToInclude = dirsToInclude;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            
            AffectedCoords.Add(_pos);
            AffectedCoords.UnionWith(_crossCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.FXRenderer.SpawnSmallCrossExplosion(_pos, _dirsToInclude);
            ReleasedCoords.Add(_pos);
            ReleasedCoords.UnionWith(_crossCoords);
        }
    }
}