using BBB.Audio;
using BebopBee.Core.Audio;
using BBB.MMVibrations.Plugins;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSpawn : Match3ActionBase
    {
        private readonly Coords _start;
        private readonly Tile _tile;
        private readonly GoalType _goalType;

        public ActionSpawn(Coords start, Tile tile, HitWaitParams hitWaitParams = null, GoalType goalType = GoalType.None)
        {
            _start = start;
            _tile = tile.Clone();
            _goalType = goalType;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            
            AffectedCoords.Add(_start);
        }

        public bool DoesTileSatisfySpeciality(TileSpeciality speciality)
        {
            return !(_tile is null) && _tile.Speciality == speciality;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCell = grid.GetCell(_start);
            
            startCell.AddTile(_tile);
            grid.TilesSpawnedCount = Mathf.Max(grid.TilesSpawnedCount, startCell.Tile.Id + 1);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var message = proxy.EventDispatcher.GetMessage<PowerupCreatedEvent>();
            message.Set(_tile);
            proxy.EventDispatcher.TriggerEvent(message);

            var startCell = grid.GetCell(_start);
            proxy.TileTickPlayer.DeleteObjects(obj => obj is StaticInvisibleOccupier pt && pt.Position == startCell.Coords.ToUnityVector2());
            
            startCell.AddTile(_tile);
            grid.TilesSpawnedCount = Mathf.Max(grid.TilesSpawnedCount, startCell.Tile.Id + 1);
            var tileView = proxy.TileController.GetOrCreateTileView(startCell.Tile);
            tileView.UpdateView(cell: startCell, tile: _tile, coords: _start, force: true);
            tileView.TileMotionController.SetPosAndFreeze(startCell.Coords.ToUnityVector2());
            tileView.TriggerFormationIntroAnimation();

            bool isBoost = true;
            switch (_tile.Speciality)
            {
                case TileSpeciality.Propeller:
                case TileSpeciality.Bomb:
                    AudioProxy.PlaySound(Match3SoundIds.BombFormation);
                    break;
                case TileSpeciality.ColumnBreaker:
                    AudioProxy.PlaySound(Match3SoundIds.VerLinebreakerFormation);
                    break;
                case TileSpeciality.RowBreaker:
                    AudioProxy.PlaySound(Match3SoundIds.HorLinebreakerFormation);
                    break;
                case TileSpeciality.ColorBomb:
                    AudioProxy.PlaySound(Match3SoundIds.ColorBombFormation);
                    break;
                default:
                    isBoost = false;
                    break;
            }

            if (isBoost)
            {
                tileView.MoveToBoostFormationContainerForIntro();
            }
            
            if (_goalType != GoalType.None)
            {
                proxy.GoalPanel.AddGoals(grid, proxy.GoalsSystem, _goalType).Forget();
            }
            
            ReleasedCoords.Add(_start);
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tile.Id} coords={_start} specialty={_tile.Speciality}";
        }
    }
}