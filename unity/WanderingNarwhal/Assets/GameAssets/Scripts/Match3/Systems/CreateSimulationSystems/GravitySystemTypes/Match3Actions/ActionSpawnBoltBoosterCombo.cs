namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary>
    /// Spawn combo and pause game if effect prefab contains pause setting.
    /// </summary>
    public class ActionSpawnBoltBoosterCombo : Match3ActionBase
    {
        private readonly Coords _colorBombCoords;
        private readonly Coords _specialTileCoords;
        private readonly TileKinds _kind;
        private readonly float _animDuration;

        public ActionSpawnBoltBoosterCombo(Coords colorBombCoords, Coords specialTileCoords, TileKinds kind, float animDuration)
        {
            _colorBombCoords = colorBombCoords;
            _specialTileCoords = specialTileCoords;
            _animDuration = animDuration;
            _kind = kind;
            
            AffectedCoords.Add(_colorBombCoords);
            AffectedCoords.Add(_specialTileCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.FXRenderer.SpawnBoltBoosterCombine(proxy.Settings, _colorBombCoords, _kind, _animDuration);

            var tileView = proxy.TileController.GetTileViewByCoord(_specialTileCoords, false);

            if (tileView != null)
            {
                tileView.TriggerBoltComboAnimation();
            }
            
            ReleasedCoords.Add(_colorBombCoords);
            ReleasedCoords.Add(_specialTileCoords);
        }
    }
}