namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSpawnDiscoRushCollect : Match3ActionBase
    {
        private readonly Coords _first;
        private readonly Coords? _second;
        private readonly float _totalDuration;
        private readonly int _countOfTilesToDestroy;

        public ActionSpawnDiscoRushCollect(Coords first, float totalDuration, int countOfTilesToDestroy, Coords? second = null)
        {
            _first = first;
            _second = second;
            _totalDuration = totalDuration;
            _countOfTilesToDestroy = countOfTilesToDestroy;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tc = proxy.TileController;
            if (_second.HasValue)
            {
                var firstTileView = tc.GetTileViewByCoord(_first);
                var secondTileView = tc.GetTileViewByCoord(_second.Value);
                proxy.FXRenderer.SpawnDiscoRushCollect(firstTileView, secondTileView, _totalDuration, _countOfTilesToDestroy);
            }
            else
            {
                proxy.FXRenderer.SpawnDiscoRushCollect(_first, _totalDuration, _countOfTilesToDestroy);
            }
        }
    }
}