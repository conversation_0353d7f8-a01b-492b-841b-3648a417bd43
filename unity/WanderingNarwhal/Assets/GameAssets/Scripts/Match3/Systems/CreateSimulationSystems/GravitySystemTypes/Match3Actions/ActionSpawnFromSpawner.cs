using System.Collections.Generic;
using BebopBee.Core;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSpawnFromSpawner : Match3ActionBase, IFreeFallTargetPriority
    {
        private readonly Coords _coords;
        private readonly Tile _tile;
        private readonly Dictionary<int, TileKinds> _lateDefinedTileKinds;

        public ActionSpawnFromSpawner(Coords coords, Tile tile, Dictionary<int,TileKinds> lateDefinedTileKinds)
        {
            _coords = coords;
            _tile = tile.Clone();
            _lateDefinedTileKinds = lateDefinedTileKinds;
            
            AffectedCoords.Add(_coords);
        }

        protected override string GetMembersString()
        {
            return $"coords={_coords} tileId={_tile.Id}";
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ExecutionStarted) return true;
            
            var startCell = grid.GetCell(_coords);
            return startCell.CanAcceptTile() &&
                   !proxy.TileTickPlayer.Occupied(_coords) && 
                   !proxy.TileTickPlayer.BoardObjectFactory.AreThereFutureTargetLocks(_coords, Priority) &&
                   proxy.TileTickPlayer.TryMoveTo(_tile.Id, _coords) &&
                   base.CanExecute(grid, proxy);
        }

        private bool ModifyGrid(Grid grid, out Cell cell)
        {
            cell = grid.GetCell(_coords);
            if (_lateDefinedTileKinds.ContainsKey(_tile.Id))
            {
                _tile.SetKind(_lateDefinedTileKinds[_tile.Id]);
                _lateDefinedTileKinds.Remove(_tile.Id);
            }

            cell.AddTile(_tile);
            
            grid.TilesSpawnedCount = Mathf.Max(grid.TilesSpawnedCount, cell.Tile.Id + 1);

            return true;
        }
        
        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, out var cell))
            {
                var tileView = proxy.TileController.GetOrCreateTileView(cell.Tile);
                tileView.ResetScale();
                tileView.SetAlpha(1);
            }
            ReleaseCoords(proxy);
        }
        
        private void ReleaseCoords( PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(_coords);
            proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_coords, _tile.Id);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, out var cell))
            {
                var tileView = proxy.TileController.GetOrCreateTileView(cell.Tile);
                var startCellPos = cell.Coords.ToUnityVector2();
                var fadeInStartPos = startCellPos + new Vector2(0f, 0.5f);
                tileView.ActivateFadeIn(fadeInStartPos.y, startCellPos.y);
                tileView.TileMotionController.SetPosAndFreeze(fadeInStartPos);

                bool Predicate()
                {
                    return !tileView.FadeInModeActive();
                }
                
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(Predicate, () =>
                {
                    ReleaseCoords(proxy);
                }));

                proxy.TilesFeedbackNotificationsHandler.RegisterTileHasEnteredCell(_coords);
            }
        }

        public int TileId => _tile.Id;
        public Coords Coords => _coords;
    }
}