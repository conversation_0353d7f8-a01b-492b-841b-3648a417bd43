using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSpawnInCells : Match3ActionBase
    {
        private readonly Coords _sourceCoords;
        private readonly List<Coords> _coordsList;
        private readonly CellState _cellState;
        private readonly GoalType _goalType;
        private readonly FxType _fxType;
        
        public ActionSpawnInCells(Coords sourceCoords, List<Coords> coordsList, CellState cellState,
            GoalType goalType, FxType fxType, HitWaitParams hitWaitParams)
        {
            _sourceCoords = sourceCoords;
            _coordsList = coordsList;
            _cellState = cellState;
            _goalType = goalType;
            _fxType = fxType;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams);
            
            AffectedCoords.Add(_sourceCoords);
            AffectedCoords.UnionWith(_coordsList);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var waitTime = 0f;
            
            foreach (var variable in _coordsList)
            {
                if (grid.TryGetCell(variable, out var cell))
                {
                    cell.BackgroundCount = 1;
                    cell.Add(_cellState);
                    Rx.Invoke(waitTime, _ =>
                    {
                        proxy.FXRenderer.SpawnSpreadAnticipationEffect(cell.Coords, _fxType);
                        Rx.Invoke(proxy.Settings.SpawnAnticipationDelay, _ =>
                        {
                            proxy.CellController.Update(cell);
                            proxy.GoalPanel.AddGoals(grid, proxy.GoalsSystem, _goalType).Forget();
                        });
                    });
                    waitTime +=  proxy.Settings.SpawnAnticipationWaitTime;
                }
            }
            
            proxy.Vibrations.PlayHapticSequence(ImpactPreset.LightImpact, 5, waitTime);
    
            ReleasedCoords.Add(_sourceCoords);
            ReleasedCoords.UnionWith(_coordsList);
        }
        
    }
}