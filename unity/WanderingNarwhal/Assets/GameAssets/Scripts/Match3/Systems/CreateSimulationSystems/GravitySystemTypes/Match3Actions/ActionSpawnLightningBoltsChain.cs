using System.Collections.Generic;
using BBB.Match3.Renderer;
using BebopBee.Core;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSpawnLightningBoltsChain : Match3ActionBase
    {
        private readonly int _boltId;
        private readonly List<CellWithTileKinds> _coordsList;
        private readonly float _lightningDuration;
        private readonly float _totalDuration;
        private readonly TileKinds _maxKind;
        private readonly Cell _colorBombCell;
        private readonly float _lightningsWidth;
        private readonly List<GameObject> _shakeList = new();
        private readonly List<BoltTarget> _boltTargets = new();

        public ActionSpawnLightningBoltsChain(int boltId, TileKinds maxKind, Cell colorBombCell, 
            List<CellWithTileKinds> coordsList, float lightningsWidth, float lightningDuration, float totalDuration)
        {
            _boltId = boltId;
            _maxKind = maxKind;
            _coordsList = coordsList;
            _lightningDuration = lightningDuration;
            _totalDuration = totalDuration;
            _colorBombCell = colorBombCell;
            _lightningsWidth = lightningsWidth;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;
            foreach (var item in _coordsList)
            {
                var boltTarget = proxy.TileTickPlayer.BoardObjectFactory.CreateBoltTarget(_boltId, item.Cell.Coords);
                _boltTargets.Add(boltTarget);
            }

            var tileKindsWithCellsToDestroy = CellWithTileKinds.SubdivideAndSortCoordsList(_coordsList, _maxKind);

            proxy.FXRenderer.SpawnLightningBoltsChain(_colorBombCell, tileKindsWithCellsToDestroy, _lightningDuration, _lightningsWidth,
                coords => { OnLightningSpawned(coords, proxy); });

            Rx.Invoke(_totalDuration, _ =>
            {
                WaitingForCompletion = false;
                foreach (var item in _coordsList)
                {
                    var tileView = proxy.TileController.GetTileViewByCoord(item.Cell.Coords, false);
                    if (tileView != null && tileView.Animator.IsAnyPlaying(StateType.Shaking))
                    {
                        tileView.Animator.StopShaking();
                    }
                }

                foreach (var boltTarget in _boltTargets)
                {
                    boltTarget.Delete(proxy);
                }

                _boltTargets.Clear();

                foreach (var go in _shakeList)
                {
                    go.Release();
                }

                _shakeList.Clear();
                proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(proxy.Settings.TileDestroyByBooster.BusyTime, _colorBombCell.Coords.ToUnityVector2());
            });
        }

        private void OnLightningSpawned(Coords coords, PlaySimulationActionProxy proxy)
        {
            var shakeEffect = proxy.FXRenderer.StartShake(coords, float.MaxValue);
            if (shakeEffect != null)
            {
                _shakeList.Add(shakeEffect);
            }
        }
    }
}