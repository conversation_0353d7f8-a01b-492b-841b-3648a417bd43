using System.Collections.Generic;
using BBB.Match3.Renderer;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSpawnLightningBoltsFromPoint : Match3ActionBase
    {
        private readonly int _boltId;
        private readonly Coords _sourceCoord;
        private readonly List<CellWithTileKinds> _coordsList;
        private readonly float _lightningDuration;
        private readonly float _totalDuration;
        private readonly float _lightningsWidth;
        private readonly List<BoltTarget> _boltTargets = new();
        private readonly TileSpeciality _tileSpeciality;
        private readonly List<GameObject> _shakeList = new();

        public ActionSpawnLightningBoltsFromPoint(int boltId, Coords sourceCoord, List<CellWithTileKinds> coordsList,
            float lightningDuration, float totalDuration, float lightningsWidth, TileSpeciality tileSpeciality)
        {
            _boltId = boltId;
            _sourceCoord = sourceCoord;
            _coordsList = coordsList;
            _lightningDuration = lightningDuration;
            _totalDuration = totalDuration;
            _lightningsWidth = lightningsWidth;
            _tileSpeciality = tileSpeciality;

            foreach (var item in _coordsList)
            {
                AffectedCoords.Add(item.Cell.Coords);
            }
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            foreach (var item in _coordsList)
            {
                var boltTarget = proxy.TileTickPlayer.BoardObjectFactory.CreateBoltTarget(_boltId, item.Cell.Coords);
                _boltTargets.Add(boltTarget);
            }

            var cellsAndWaitLinksToDestroy = CellWithTileKinds.SubdivideAndSortCoordsList(_coordsList);

            proxy.FXRenderer.SpawnLightningBoltsFromFirstPoint(
                _sourceCoord,
                cellsAndWaitLinksToDestroy,
                _lightningDuration,
                _lightningsWidth,
                coords => { OnLightningSpawned(coords, proxy); });

            Rx.Invoke(_totalDuration, _ =>
            {
                if (_tileSpeciality == TileSpeciality.Bomb)
                {
                    proxy.GridController.DoTweenShakeBoard(shakeSettings: ShakeSettingsType.BoltBomb);
                }

                foreach (var coords in AffectedCoords)
                {
                    var tileView = proxy.TileController.GetTileViewByCoord(coords, false);
                    if (tileView != null && tileView.Animator.IsAnyPlaying(StateType.Shaking))
                    {
                        tileView.Animator.StopShaking();
                    }
                }

                foreach (var boltTarget in _boltTargets)
                {
                    boltTarget.Delete(proxy);
                }

                _boltTargets.Clear();

                foreach (var go in _shakeList)
                {
                    go.Release();
                }

                _shakeList.Clear();
            });
        }

        private void OnLightningSpawned(Coords coords, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(coords);

            var shakeEffect = proxy.FXRenderer.StartShake(coords, float.MaxValue);
            if (shakeEffect != null)
            {
                _shakeList.Add(shakeEffect);
            }
        }
    }
}