using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionStarWave : Match3ActionBase
    {
        public override bool ShouldBlockOtherActions() => false;
        
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCoord = grid.GetCenterVec2();
            proxy.FXRenderer.SpawnCircleWave(startCoord);
            var delayBefore = EndGameSettings.DelayBetweenCircleAndStars;
            var starsTime = EndGameSettings.StarWaveEffectTotalTime;
            Rx.Invoke(delayBefore, SpawnStarsAction);

            void SpawnStarsAction(long _)
            {
                proxy.FXRenderer.SpawnStars(grid, startCoord, starsTime);
            }
        }
    }
}