using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.UI.Level;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionStartRain : Match3ActionBase
    {
        private readonly List<Tweener> _tweener = new List<Tweener>();

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            foreach (var tween in _tweener)
            {
                tween?.Kill();
            }
        }
        
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            // Adding this fake coords to _affected coords so that CompleteExecution can be called if this action is
            // cancelled in the middle
            var fakeCoords = new Coords();
            AffectedCoords.Add(fakeCoords);
            
            WaitingForCompletion = true;
            proxy.CharacterAnimator.SetCurrentIdleAnim(Match3CharacterAnim.RainIdle);
            proxy.CharacterAnimator.PlayAnimation(Match3CharacterAnim.RainIntro);

            if (_tweener != null)
            {
                foreach (var tween in _tweener)
                {
                    tween?.Kill();
                }
            }
            _tweener.Clear();
            
            _tweener.Add(Rx.Invoke(SuperBoostConstants.UmbrellaSoundOffset, UmbrellaOpenAction));
            _tweener.Add(Rx.Invoke(SuperBoostConstants.PauseBeforeSpawningRainCloud, RainCloudAction));

            void UmbrellaOpenAction(long _)
            {
                AudioProxy.PlaySound(Match3SoundIds.UmbrellaOpen);
            }

            void RainCloudAction(long arg1)
            {
                AudioProxy.PlaySound(Match3SoundIds.RainLoop);
                proxy.FXRenderer.SpawnRainCloud();
                proxy.SuperBoostSystem.ResetProgress();
                BDebug.Log(LogCat.Match3, $"Starting Rain, InputLock = true");
                proxy.GameController.LockInput(true);

                void UnPauseAction(long _)
                {
                    WaitingForCompletion = false;
                    ReleasedCoords.Add(fakeCoords);
                }

                Rx.Invoke(SuperBoostConstants.PauseAfterSpawningRainCloud, UnPauseAction);
            }
        }
    }

    public class ActionEndRain : Match3ActionBase
    {
        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            InitialExecution(grid, proxy);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            AudioProxy.StopSound(Match3SoundIds.RainLoop);
            RainEffect.StopRain();
            DimEverythingEffect.StopDim();
            AudioProxy.PlaySound(Match3SoundIds.SuperBoostEnd);
            Rx.Invoke(SuperBoostConstants.PauseBeforeHidingUmbrella, PlayAnimationAction);

            void PlayAnimationAction(long _)
            {
                proxy.CharacterAnimator.SetCurrentIdleAnim(Match3CharacterAnim.Idle);
                proxy.CharacterAnimator.PlayAnimation(Match3CharacterAnim.RainOutro);
            }
        }
    }
}
