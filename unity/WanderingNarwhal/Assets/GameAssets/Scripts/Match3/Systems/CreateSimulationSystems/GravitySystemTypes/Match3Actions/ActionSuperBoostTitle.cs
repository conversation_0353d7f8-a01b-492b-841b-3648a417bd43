using BBB.Audio;
using BBB.Core;
using BBB.GameAssets.Scripts.Match3.Render;
using BebopBee.Core;
using BebopBee.Core.Audio;
using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSuperBoostTitle : Match3ActionBase
    {        
        private const string OverlayName = "Prefabs/SuperBoostOverlay";

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;
            BDebug.Log(LogCat.Match3, $"Showing Super Boost Title, InputLock = true");
            proxy.GameController.LockInput(true);
            proxy.OverlayRenderer.LoadAndLaunchOverlay(OverlayName, OnLaunched);

            void OnLaunched(OverlayEffect effectCtrl)
            {
                AudioProxy.PlaySound(Match3SoundIds.SuperBoost);
                Rx.Invoke(SuperBoostConstants.SuperBoostOverlayTitleDisplayTime, EffectCtrlAction);

                void EffectCtrlAction(long _)
                {
                    effectCtrl.StopAndClear();
                }

                if (SuperBoostConstants.SuperBoostOverlayTitleBlockDuration > 0)
                {
                    Rx.Invoke(SuperBoostConstants.SuperBoostOverlayTitleBlockDuration, UnblockAction);
                    void UnblockAction(long _)
                    {
                        WaitingForCompletion = false;
                    }
                }
                else
                {
                    WaitingForCompletion = false;
                }
            }
        }
    }
}