using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using Bebopbee.Core.Utility;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionSwapBoth : Match3ActionBase
    {
        private readonly Coords _start;
        private readonly Coords _end;
        private readonly bool _canceled;

        private readonly Dictionary<TileView, Coroutine> _coroutinesMap = new();
        private Coroutine _baseSwapRoutine;

        public ActionSwapBoth(Coords start, Coords end, bool canceled = false)
        {
            _start = start;
            _end = end;
            _canceled = canceled;
            WaitingForCompletion = true;
            
            AffectedCoords.Add(_start);
            AffectedCoords.Add(_end);
        }

        private void ModifyGrid(Grid grid)
        {
            if (_canceled)
                return;

            var startCell = grid.GetCell(_start);
            var endSwapCell = grid.GetCell(_end);
            startCell.SwapTileWith(endSwapCell);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            TileView firstView = null;
            TileView secondView = null;

            if (proxy.GridController.HasTile(_start))
            {
                firstView = proxy.TileController.GetTileViewByCoord(_start);
                firstView.MoveLayersOnTop();

                if (_coroutinesMap.TryGetValue(firstView, out var existingFirstCoroutine) &&
                    existingFirstCoroutine != null)
                {
                    proxy.FXRenderer.StopCoroutine(existingFirstCoroutine);
                    _coroutinesMap.Remove(firstView);
                }
            }

            if (proxy.GridController.HasTile(_end))
            {
                secondView = proxy.TileController.GetTileViewByCoord(_end);

                if (_coroutinesMap.TryGetValue(secondView, out var existingSecondCoroutine) &&
                    existingSecondCoroutine != null)
                {
                    proxy.FXRenderer.StopCoroutine(existingSecondCoroutine);
                    _coroutinesMap.Remove(secondView);
                }
            }

            if (firstView is not null)
            {
                var firstRoutine = RunSwap(proxy, firstView, _end, OnSwapFinished, scaleTile: true);
                _coroutinesMap[firstView] = proxy.FXRenderer.StartCoroutine(firstRoutine);
            }

            if (secondView is not null)
            {
                var hasSpecialTiles = HasSpecialTile(_start, grid) && HasSpecialTile(_end, grid);
                var secondRoutine = RunSwap(proxy, secondView, _start, canSwap: !hasSpecialTiles);
                _coroutinesMap[secondView] = proxy.FXRenderer.StartCoroutine(secondRoutine);
            }

            var firstCell = grid.GetCell(_start);
            var secondCell = grid.GetCell(_end);

            if (_baseSwapRoutine != null)
            {
                proxy.FXRenderer.StopCoroutine(_baseSwapRoutine);
            }

            var launched = (proxy.FXRenderer as IPreSwapInitialtedEffectRenderer).LaunchPreSwapInitiatedEffect(_start, _end, firstView, secondView);
            if (launched)
            {
                if (firstView != null)
                    firstView.SetAlpha(0f);

                if (secondView != null)
                    secondView.SetAlpha(0f);
            }
            else
            {
                if (firstView != null)
                    firstView.TriggerSwapAnimation();
                if (secondView != null)
                    secondView.TriggerSwapAnimation();

                var duration = 1 / proxy.Settings.SwapSpeed;
                proxy.FXRenderer.SpawnSwapEffect(firstView, secondView, duration);
            }

            AudioProxy.PlaySound(Match3SoundIds.TileSwap);

            ModifyGrid(grid);
            
            void OnSwapFinished()
            {
                firstView?.MoveLayersToBottom();
                if (_canceled)
                {
                    CancelSwap(proxy, _start, _end, firstView, secondView);
                    return;
                }
                ReleaseCoords();
            }
        }

        private void ReleaseCoords()
        {
            ReleasedCoords.Add(_start);
            ReleasedCoords.Add(_end);

            WaitingForCompletion = false;
        }

        private void CancelSwap(PlaySimulationActionProxy proxy, Coords first, Coords second, TileView firstView, TileView secondView)
        {
            if (!ReferenceEquals(firstView, null))
            {
                if (_coroutinesMap.TryGetValue(firstView, out var value) && value != null)
                {
                    proxy.FXRenderer.StopCoroutine(value);
                }

                var firstRoutine = RunSwap(proxy, firstView, first);
                _coroutinesMap[firstView] = proxy.FXRenderer.StartCoroutine(firstRoutine);
            }

            if (!ReferenceEquals(secondView, null))
            {
                if (_coroutinesMap.TryGetValue(secondView, out var value) && value != null)
                {
                    proxy.FXRenderer.StopCoroutine(value);
                }

                var secondRoutine = RunSwap(proxy, secondView, second);
                _coroutinesMap[secondView] = proxy.FXRenderer.StartCoroutine(secondRoutine);
            }

            proxy.FXRenderer.StartCoroutine(CancelRoutine(proxy, firstView, secondView, ReleaseCoords));
            AudioProxy.PlaySound(Match3SoundIds.TileSwapCancel);
        }

        private IEnumerator CancelRoutine(PlaySimulationActionProxy proxy, TileView firstView, TileView secondView, Action callback = null)
        {
            var speed = proxy.Settings.SwapSpeed;
            var factor = 0f;

            while (factor < 1f)
            {
                factor += Time.deltaTime * speed;

                yield return null;
            }
            
            if (!ReferenceEquals(firstView, null))
                firstView.MoveLayersToBottom();

            if (!ReferenceEquals(secondView, null))
                secondView.MoveLayersToBottom();

            callback.SafeInvoke();
        }

        private void ProcessBlockedSwap(PlaySimulationActionProxy proxy, Coords first, Coords second)
        {
            var gridController = proxy.GridController;
            if (gridController == null || !gridController.HasTile(first)) 
                return;
            var tileView = proxy.TileController?.GetTileViewByCoord(first);
            if (tileView == null || tileView.Animator.IsAnyPlaying(StateType.All) || _coroutinesMap.ContainsKey(tileView))
                return;

            var dir = (second - first).ToUnityVector2() * proxy.TilesResources.CellSize;
            var duration = 1 / proxy.Settings.SwapSpeed;
            tileView.Animator.ShortSwipe(dir, duration);
        }

        private static bool HasSpecialTile(Coords coord, Grid grid)
        {
            return grid.TryGetCell(coord, out var cell) && cell.HasTile() && cell.Tile.IsBoost;
        }

        private IEnumerator RunSwap(PlaySimulationActionProxy proxy, TileView tileView, Coords destinationCoords, Action callback = null, bool canSwap = true, bool scaleTile = false)
        {
            var startingPos = tileView.TileMotionController.Position;
            var destinationPos = destinationCoords.ToUnityVector2();
            var speed = proxy.Settings.SwapSpeed;
            var factor = 0f;

            tileView.TileMotionController.HandleSwapStart();

            var startingScale = tileView.transform.localScale;
            if (canSwap)
            {
                while (factor < 1f)
                {
                    factor += Time.deltaTime * speed;
                    factor = Mathf.Min(1f, factor);
                    if (scaleTile)
                    {
                        tileView.transform.localScale = startingScale * (1f + MathUtility.QuadraticPeak(factor) * proxy.Settings.SwapScale);
                    }
                    tileView.TileMotionController.QuadLerpPos(startingPos, destinationPos, factor);
                    yield return null;
                }
            }
            else
            {
                destinationPos = startingPos;
            }

            tileView.TileMotionController.SetPosAndFreeze(destinationPos);
            _coroutinesMap.Remove(tileView);
            callback.SafeInvoke();
        }
    }

}