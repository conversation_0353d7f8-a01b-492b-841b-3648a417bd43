using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSwapElegant : Match3ActionBase
    {
        private readonly Coords _first;
        private readonly Coords _second;
        private const int MOVES = 2;
        private const float DURATION = 2f;

        public ActionSwapElegant(<PERSON>ords first, Coords second)
        {
            _first = first;
            _second = second;
            
            AffectedCoords.Add(_first);
            AffectedCoords.Add(_second);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;

            var firstView = proxy.TileController.GetTileViewByCoord(_first);
            var secondView = proxy.TileController.GetTileViewByCoord(_second);

            var movesLeft = MOVES;
            
            var firstStartPos = firstView.LocalPosition;
            var firstTargetPos = secondView.LocalPosition;
            var firstPath = PathFactory.GetCircPath(firstStartPos, firstTargetPos);

            var secondStartPos = secondView.LocalPosition;
            var secondTargetPos = firstView.LocalPosition;
            var secondPath = PathFactory.GetCircPath(secondStartPos, secondTargetPos);

            firstView.TweenLocalPosition(startCoord: _first, targetCoords: _second,
                duration: DURATION,
                path: firstPath,
                callback: OnMoveDone);
            firstView.TweenAddRotation(angle: 360f, duration: DURATION);
            
            secondView.TweenLocalPosition(startCoord:_second, targetCoords: _first,
                duration: DURATION,
                path: secondPath,
                callback: OnMoveDone);
            secondView.TweenAddRotation(angle: 360f, duration: DURATION);

            proxy.FXRenderer.SpawnWindLeaves(firstView);
            proxy.FXRenderer.SpawnWindLeaves(secondView);

            void OnMoveDone()
            {
                movesLeft--;
                if (movesLeft <= 0)
                {
                    ReleasedCoords.Add(_first);
                    ReleasedCoords.Add(_second);
                    WaitingForCompletion = false;
                }
            }
        }
    }
}