using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary>
    /// This action will make sure that all the actions that affected the coords being passed as a parameter will be
    /// executed once ALL those coords are released by previous actions 
    /// </summary>
    public class ActionSyncCoords : Match3ActionBase
    {
        public ActionSyncCoords(IEnumerable<Coords> coords)
        {
            AffectedCoords.UnionWith(coords);
        }
        
        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.UnionWith(AffectedCoords);
        }
    }
}