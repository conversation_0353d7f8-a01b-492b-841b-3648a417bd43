using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionTripleCross : Match3ActionBase
    {
        private readonly Coords _coords;
        private readonly TileSpeciality _lineBreakerSpeciality;

        public ActionTripleCross(Coords coords, TileSpeciality lineBreakerSpeciality)
        {
            _coords = coords;
            _lineBreakerSpeciality = lineBreakerSpeciality;
            AffectedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            AudioProxy.PlaySound(Match3SoundIds.TripleCrossBreaker);
            var effect = proxy.FXRenderer.SpawnTripleLineBreaker(_coords,
                proxy.Settings, _lineBreakerSpeciality);

            var pauseSetting = effect.GetComponent<PauseSimulationSettingComponent>();
            if (pauseSetting != null && pauseSetting.PauseDuration > 0)
            {
                WaitingForCompletion = true;
                void ReleasedCoordsAction(long _)
                {
                    ReleasedCoords.Add(_coords);
                    WaitingForCompletion = false;
                }

                Rx.Invoke(pauseSetting.PauseDuration, ReleasedCoordsAction);
            }
        }
    }
}