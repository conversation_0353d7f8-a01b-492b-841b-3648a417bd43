using BBB;
using BBB.Core.Analytics;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

internal class SuperBoostProgress
{
    public readonly int TileId = -1;
    public readonly TileSpeciality TileSpeciality = TileSpeciality.None;
    public readonly Coords Coords = Coords.OutOfGrid;

    public SuperBoostProgress(Cell cell)
    {
        if (cell != null && cell.HasTile())
        {
            TileId = cell.Tile.Id;
            TileSpeciality = cell.Tile.Speciality;
            Coords = cell.Coords;
        }
    }
}

public sealed class ActionTryIncrementSuperBoost : Match3ActionBase
{
    private readonly SuperBoostProgress _firstTile;
    private readonly SuperBoostProgress _secondTile;
    private readonly HitWaitParams _hitWaitParams;

    public ActionTryIncrementSuperBoost(Cell firstCell, Cell secondCell, HitWaitParams hitWaitParams = null)
    {
        _firstTile = new SuperBoostProgress(firstCell);
        _secondTile = new SuperBoostProgress(secondCell);

        AffectedCoords.Add(_firstTile.Coords);
        AffectedCoords.Add(_secondTile.Coords);

        _hitWaitParams = hitWaitParams;
    }

    private bool IsCreatedBySuperBoost(Cell cell, PlaySimulationActionProxy proxy)
    {
        return cell != null && cell.HasTile() && proxy.SuperBoostSystem.IsTileCreatedBySuperBoost(cell.Tile.Id);
    }

    private void UnregisterFromSuperBoost(Cell cell, PlaySimulationActionProxy proxy)
    {
        if (cell != null && cell.HasTile())
        {
            proxy.SuperBoostSystem.UnRegisterSuperBoostTile(cell.Tile.Id);
        }
    }

    public void VirtualApplyToSuperBoost(SuperBoostSystem superBoostSystem)
    {
        if (_hitWaitParams?.DamageSource != DamageSource.EndGame)
            superBoostSystem.AddProgress(_firstTile.TileSpeciality, _secondTile.TileSpeciality, _firstTile.Coords, _secondTile.Coords);
    }

    protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
    {
        ReleasedCoords.UnionWith(AffectedCoords);

        if (_hitWaitParams != null && (_hitWaitParams?.DamageSource & (DamageSource.AddsToSuperBoost)) == 0)
        {
            return;
        }

        var firstCell = grid.FindCellByTileId(_firstTile.TileId);
        var secondCell = grid.FindCellByTileId(_secondTile.TileId);

        var isCreatedBySuperBoost = IsCreatedBySuperBoost(firstCell, proxy) || IsCreatedBySuperBoost(secondCell, proxy);

        if (!isCreatedBySuperBoost)
        {
            var skipSuperBoostProgress = proxy.GameController.IsBonusTime || proxy.GameController.GameEnded;

            proxy.SuperBoostSystem.AddProgress(_firstTile.TileSpeciality, _secondTile.TileSpeciality,
                                                _firstTile.Coords, _secondTile.Coords, skipSuperBoostProgress);
        }

        if (isCreatedBySuperBoost)
        {
            UnregisterFromSuperBoost(firstCell, proxy);
            UnregisterFromSuperBoost(secondCell, proxy);
           // BfgFunnelEvent.UsedColorBombFromSuperBoost(proxy.GameController.Level.Config.Uid);
        }
    }
}
