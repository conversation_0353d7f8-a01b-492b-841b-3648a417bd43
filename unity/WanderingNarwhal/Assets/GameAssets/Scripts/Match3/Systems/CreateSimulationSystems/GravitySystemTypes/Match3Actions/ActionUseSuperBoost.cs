namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionUseSuperBoost : Match3ActionBase
    {
        private readonly Coords? _coords;

        public ActionUseSuperBoost(Coords? coords = null)
        {
            _coords = coords;
        }
        
        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            WaitingForCompletion = true;
            proxy.GameController.LockInput(true);
            proxy.SuperBoostController.UseColorBombAsSuperBoost(_coords, () =>
            {
                WaitingForCompletion = false;
            });
        }
    }

}