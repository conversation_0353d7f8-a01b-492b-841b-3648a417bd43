using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionWhirlpool : Match3ActionBase
    {
        private const float WhirlpoolSpeed = 9f;

        private Coords _firstCoords;
        private readonly Coords _secondCoords;
        private readonly List<int> _whirlpoolId;
        private readonly List<Coords> _coordsList;

        public ActionWhirlpool(List<int> whirlpoolIds, List<Coords> coordsList, Coords firstCoords, Coords secondCoords)
        {
            _whirlpoolId = whirlpoolIds;
            _coordsList = coordsList;
            _firstCoords = firstCoords;
            _secondCoords = secondCoords;

            AffectedCoords.Add(_firstCoords);
            AffectedCoords.Add(_secondCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var perWaveDelay = proxy.Settings.DiscoBallStageDelayWhileUsingSuperDiscoBall;
            var maxLifetime = 0f;
            var stageDelay = 0f;

            foreach (var cell in grid.Cells)
            {
                if (cell.Coords != _firstCoords || cell.Coords != _secondCoords)
                {
                    proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(_whirlpoolId.Count * perWaveDelay, cell.Coords.ToUnityVector2());
                }
            }

            var fx = FxType.Whirlpool;

            foreach (var whirlPool in _whirlpoolId)
            {
                var centerPosition = _firstCoords.ToUnityVector2();

                foreach (var coord in _coordsList)
                {
                    var lifeTime = coord.DistanceFrom(centerPosition) / WhirlpoolSpeed + stageDelay;
                    maxLifetime = Math.Max(maxLifetime, lifeTime);

                    proxy.TileTickPlayer.BoardObjectFactory.CreateWhirlpoolTarget(whirlPool, coord, lifeTime, null);
                }

                var fxToPlay = fx;
                Rx.Invoke(stageDelay, _ =>
                {
                    AudioProxy.PlaySound(Match3SoundIds.Whirlpool);
                    proxy.FXRenderer.SpawnWhirlpool(_secondCoords, fxToPlay);
                });

                stageDelay += perWaveDelay;
                fx = FxType.WhirlpoolSecondWave;
            }
            
            Rx.Invoke(maxLifetime, _ =>
            {
                proxy.VoiceoverPlayer?.Play(M3SpecialVoiceovers.BoltBolt, false, out var _);
            });

            ReleasedCoords.Add(_firstCoords);
            ReleasedCoords.Add(_secondCoords);
        }
    }
}