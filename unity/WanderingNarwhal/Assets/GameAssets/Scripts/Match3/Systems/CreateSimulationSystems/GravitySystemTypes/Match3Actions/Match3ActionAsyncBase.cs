namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public abstract class Match3ActionAsyncBase : Match3ActionBase
    {
        /*private bool _alreadyLogged = false;
        public bool HasFinished { get; private set; }
        public override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (CanExecute(grid, proxy))
            {
                ExecuteAsync(grid, proxy);
                HasFinished = true;
            }
            else if (!_alreadyLogged)
            {
                _alreadyLogged = true;
                BDebug.LogError(LogCat.General, $"Couldn't execute {this}");
            }
        }

        protected override void PreExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            ExecuteAsync(grid, proxy);
        }
        
        public override bool WaitingForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return false;
        }

        protected virtual bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return !WaitCondition?.WaitForExpectedState(grid, proxy) ?? true;
        }
        protected abstract void ExecuteAsync(Grid grid, PlaySimulationActionProxy proxy);*/
    }
}