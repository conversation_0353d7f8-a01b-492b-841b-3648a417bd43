using System.Collections.Generic;
using System.Text;
using BBB.Core;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary>
    /// Logical action base type.
    /// </summary>
    /// <remarks>
    /// Match3 actions are executed after simulation step completed.
    ///
    /// note 0: Logical action takes 'real' grid instance when executed,
    /// but when it is created, usually it is in the context of temporary cloned grid, which is living only during simulation step,
    /// so logical actions can't take references to Cells or Tiles directly,
    /// only coordinates, clones and value type arguments.
    ///
    /// note 1: 'real' grid, which is affected by actions, represents visual state of game field (displayed on screen),
    /// while simulation grid, which is used in GravitySystem, is not displayed, so all actions in simulation
    /// must be repeated on main grid instance after simulation step is completed.
    ///
    /// note 2: execution of logical actions (or playback actions visually) is divided into 'steps', each step can contain any amount of actions,
    /// and on each step every action is executed simultaniously,
    /// but between steps there is a time delay, which is added if there are any tile fall animation (delay valuae is determined by Match3Settings.FallingSpeedMult).
    /// <seealso cref="Match3SimulationPlayer.AnimateTurn"/> -VK
    /// </remarks>
    public abstract class Match3ActionBase
    {
        protected WaitConditionBase WaitCondition;
        public bool ExecutionStarted { get; private set; }
#if BBB_LOG_M3_SIMULATION
        public bool LoggedWait { get; private set; }
#endif
        
        public bool WaitingForCompletion { get; protected set; }
        
        protected readonly HashSet<Coords> AffectedCoords = new ();
        protected readonly HashSet<Coords> AllReleasedCoords = new ();
        protected readonly HashSet<Coords> ReleasedCoords = new ();

        private bool _alreadyLogged = false;
        public bool HasFinished { get; private set; }
        public virtual bool HasFinishedAsync => HasFinished;
        public int Priority { get; set; }

        private const float waitingLimit = 7f;
        private float _waitingTimer = 0f;

        public virtual void Execute(Grid grid, PlaySimulationActionProxy proxy, List<Coords> releasedCoords = null)
        {
            if (proxy != null)
            {
                var prevWaitingTimer = _waitingTimer;
                _waitingTimer += Time.deltaTime;

                if (_waitingTimer > waitingLimit && prevWaitingTimer <= waitingLimit)
                {
                    var detailedString = ToDetailedString(grid, proxy);

#if BBB_LOG
                    BDebug.LogError(LogCat.General,
                        $"WAITING LIMIT EXCEEDED on action {detailedString} " +
                        $"at time {Time.time}");
#if BBB_LOG_M3_SIMULATION
                    LoggedWait = true;
#endif
#endif
                    var level = proxy.GameController.Level;
                    proxy.LevelAnalyticsReporter.ReportStuckEvent(level, detailedString);
                }
            }

            if (!CanExecute(grid, proxy)) return;

            if (!ExecutionStarted)
            {
                InitialExecution(grid, proxy);
                ExecutionStarted = true;
            }

            releasedCoords?.AddRange(ReleasedCoords);
            AllReleasedCoords.UnionWith(ReleasedCoords);
            ReleasedCoords.Clear();
            HasFinished = AllReleasedCoords.Count == AffectedCoords.Count;
        }

        protected abstract void InitialExecution(Grid grid, PlaySimulationActionProxy proxy);

        public virtual void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            
        }

        protected virtual bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return ExecutionStarted || (!WaitCondition?.WaitForExpectedState(grid, proxy) ?? true);
        }
        
        protected virtual string GetMembersString()
        {
            return null;
        }

        public override string ToString()
        {
            var type = GetType();
            var result = new StringBuilder();
            result.Append("[id=").Append(Priority).Append("] ").Append(type.Name);

            if (WaitCondition != null)
            {
                result.Append(" W(").Append(WaitCondition.GetType().Name).Append(") ");
            }

            var membersString = GetMembersString();
            if (membersString != null)
            {
                result.Append(" [").Append(membersString).Append("]");
            }
            const string space = " ";
            if (AffectedCoords.Count > 0)
            {
                result.Append(" [ affectedCoords = ");
                foreach (var coord in AffectedCoords)
                {
                    
                    result.Append(coord).Append(space);
                }

                result.Append(" ]");
            }

            if (ReleasedCoords.Count <= 0) return result.ToString();
            
            result.Append(" [ releasedCoords = ");
            foreach (var coord in ReleasedCoords)
            {
                result.Append(coord).Append(space);
            }

            result.Append(" ]");

            return result.ToString();
        }

        public HashSet<Coords> GetAffectedCoords()
        {
            return AffectedCoords;
        }

        /// <summary>
        /// Actions that do not affect cells should be run based on their order given by the simulation, so they should
        /// wait until all previous actions have finished executing 
        /// </summary>
        /// <returns>
        /// True for actions that do not have affected cells
        /// </returns>
        public virtual bool ShouldExecuteAfterAllPreviousFinished()
        {
            return AffectedCoords.Count == 0;
        }

        /// <summary>
        /// Actions that do not affect cells may or may not block the queue from continue with the execution of other
        /// actions. For example, ActionMatchesSound does not directly affect cells, but it should not block the queue.
        /// On the other hand, ActionEndTurnCheckpoint is designed to serve as a checkpoint to make sure all actions
        /// prior to that one will finish before executing the rest of the actions in the queue
        /// </summary>
        /// <returns>
        /// True for actions that do not have affected cells
        /// </returns>
        public virtual bool ShouldBlockOtherActions()
        {
            return true;
        }

        private string ToDetailedString(Grid grid, PlaySimulationActionProxy proxy)
        {
            var type = GetType();
            var result = new StringBuilder(type.Name);

            if (WaitCondition != null)
            {
                result.Append(" W(").Append(WaitCondition.GetType().Name).Append("->")
                    .Append(WaitCondition.GetTextData(grid, proxy)).Append(") ");
            }

            var membersString = GetMembersString();
            if (membersString != null)
            {
                result.Append(" [").Append(membersString).Append("]");
            }

            if (AffectedCoords.Count > 0)
            {
                result.Append(" [ affectedCoords = ");
                foreach (var coord in AffectedCoords)
                {
                    result.Append(coord).Append(" ");
                }
                result.Append(" ]");
            }

            if (ReleasedCoords.Count <= 0) return result.ToString();
            
            result.Append(" [ releasedCoords = ");
            foreach (var coord in ReleasedCoords)
            {
                result.Append(coord).Append(" ");
            }
            result.Append(" ]");
            

            return result.ToString();
        }


        public virtual bool ShouldWaitForAsyncActions()
        {
            return false;
        }
    }
}