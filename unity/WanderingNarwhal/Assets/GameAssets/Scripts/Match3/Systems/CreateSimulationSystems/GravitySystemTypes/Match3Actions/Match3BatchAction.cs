using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class Match3BatchAction : Match3ActionBase
    {
        private readonly List<Match3ActionBase> _actionsList = new List<Match3ActionBase>();

        public void AddAction(Match3ActionBase action)
        {
            _actionsList.Add(action);
        }
        
        // public bool CanBeAddedToThisBatch(Match3ActionBase otherAction)
        // {
        //     return _actionsList.Count > 0 && _actionsList[_actionsList.Count - 1].IsBatchableWith(otherAction);
        // }

        public override void Execute(Grid grid, PlaySimulationActionProxy proxy, List<Coords> releasedCoords = null)
        {
            foreach (var action in _actionsList)
            {
                action.Execute(grid, proxy, releasedCoords);
            }
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            // not called for this kind of action
        }

        /*public override bool WaitingForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            foreach(var action in _actionsList)
                if (action.WaitingForExpectedState(grid, proxy))
                    return true;

            return false;
        }*/

        public override string ToString()
        {
            string result = "BatchAction:[ ";
            foreach (var action in _actionsList)
                result += action + " ";
            result += "]";
            return result;
        }
    }
}