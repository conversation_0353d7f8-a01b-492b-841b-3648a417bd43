using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.UI.Level.Input;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public interface IPlayerInput
    {
        PlayerInputType Type { get; }
        bool ShouldEndSimulation();
    }

    public abstract class EmptyPlayerInputBase : IPlayerInput
    {
        public abstract PlayerInputType Type { get; }

        public bool ShouldEndSimulation() => true;
    }

    public sealed class PlayerInputNone : EmptyPlayerInputBase
    {
        public override PlayerInputType Type => PlayerInputType.None;
    }

    public sealed class PlayerInputEmpty : EmptyPlayerInputBase
    {
        public override PlayerInputType Type => PlayerInputType.Empty;
    }

    public sealed class PlayerInputDoubleTap : IPlayerInput
    {
        public PlayerInputType Type => PlayerInputType.DTap;

        public bool ShouldEndSimulation()
        {
            return true;
        }

        public readonly Coords TargetCoords;

        public PlayerInputDoubleTap(Coords coords)
        {
            TargetCoords = coords;
        }
    }

    public sealed class PlayerInputSingleTap : IPlayerInput
    {
        public PlayerInputType Type => PlayerInputType.Tap;

        public bool ShouldEndSimulation()
        {
            return true;
        }

        public readonly Coords TargetCoords;

        public PlayerInputSingleTap(Coords coords)
        {
            TargetCoords = coords;
        }
    }

    public sealed class PlayerInputSwap : IPlayerInput
    {
        public PlayerInputType Type => PlayerInputType.Swap;
        public readonly EasyTouchInputController.CoordsPair CoordsPair;

        public bool ShouldEndSimulation()
        {
            return true;
        }

        public PlayerInputSwap(EasyTouchInputController.CoordsPair coordsPair)
        {
            CoordsPair = coordsPair;
        }

        public static PlayerInputSwap CreateBasedOnDirection(Coords firstCoords, Coords directionCoords)
        {
            var direction = CardinalDirectionsHelper.GetCardinalDirectionInSameLineFromCoords(firstCoords, directionCoords);
            var swapCoord = firstCoords.GoSingleCardinalDirection(direction);
            var newPair = new EasyTouchInputController.CoordsPair(firstCoords, swapCoord);
            return new PlayerInputSwap(newPair);
        }

        public override string ToString()
        {
            return "Swap: " + CoordsPair.FirstCoords + " " + CoordsPair.SecondCoords;
        }
    }

    // Items:
    public class PlayerInputItemBase : IPlayerInput
    {
        public PlayerInputType Type => PlayerInputType.Item;

        public virtual bool ShouldEndSimulation()
        {
            return true;
        }

        public virtual BoosterItem Item => BoosterItem.None;

        public override string ToString()
        {
            return "Item: " + Item.ToString<BoosterItem>();
        }
    }

    public sealed class PlayerInputItemReshuffle : PlayerInputItemBase
    {
        public override BoosterItem Item => BoosterItem.Reshuffle;
    }


    // Single cell:
    public class PlayerInputItemSingleCell : PlayerInputItemBase
    {
        public readonly Coords TargetCoord;

        public PlayerInputItemSingleCell(Coords targetCoord)
        {
            TargetCoord = targetCoord;
        }
        
        public override string ToString()
        {
            return "Item: " + Item.ToString<BoosterItem>() + " " + TargetCoord;
        }
    }
    
    public sealed class PlayerInputItemShovel : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.Shovel;

        public PlayerInputItemShovel(Coords targetCoord) : base(targetCoord) {}
    }
    
    public sealed class PlayerInputItemVerticalBooster : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.Vertical;

        public PlayerInputItemVerticalBooster(Coords targetCoords) : base(targetCoords) {}
    }
    
    public sealed class PlayerInputItemHorizontalBooster : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.Horizontal;

        public PlayerInputItemHorizontalBooster(Coords targetCoords) : base(targetCoords) {}
    }

    public sealed class PlayerInputItemCreateBomb : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreateBomb;

        public PlayerInputItemCreateBomb(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }

    public sealed class PlayerInputItemCreateColorBomb : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreateColorBomb;

        public PlayerInputItemCreateColorBomb(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    } 
    
    public sealed class PlayerInputItemCreateColorBombButler : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreateColorBombButler;

        public PlayerInputItemCreateColorBombButler(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }
    public sealed class PlayerInputItemCreatePropeller : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreatePropeller;

        public PlayerInputItemCreatePropeller(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }
    
    public sealed class PlayerInputItemCreatePropellerButler : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreatePropellerButler;

        public PlayerInputItemCreatePropellerButler(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }
    
    public sealed class PlayerInputItemCreateBombButler : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.CreateBombButler;

        public PlayerInputItemCreateBombButler(Coords targetCoord) : base(targetCoord) { }
        
        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }

    public sealed class PlayerInputItemBallon : PlayerInputItemSingleCell
    {
        public override BoosterItem Item => BoosterItem.Balloon;

        public PlayerInputItemBallon(Coords targetCoord) : base(targetCoord) { }
    }

    // Two cells:
    public class PlayerInputItemTwoCells : PlayerInputItemBase
    {
        public readonly Coords FirstCoord;
        public readonly Coords SecondCoord;

        public PlayerInputItemTwoCells(Coords firstCoord, Coords secondCoord)
        {
            FirstCoord = firstCoord;
            SecondCoord = secondCoord;
        }
        
        public override string ToString()
        {
            return "Item: " + Item.ToString<BoosterItem>() + " " + FirstCoord + " " + SecondCoord;
        }
    }

    public sealed class PlayerInputItemWind : PlayerInputItemTwoCells
    {
        public override BoosterItem Item => BoosterItem.Wind;

        public PlayerInputItemWind(Coords firstCoord, Coords secondCoord) : base(firstCoord, secondCoord) { }
    }

    public sealed class PlayerInputItemRain : PlayerInputItemBase
    {
        public override BoosterItem Item => BoosterItem.Rain;
        public float Percent { get; }

        public List<Coords> HitPositions;

        public PlayerInputItemRain(float percent)
        {
            Percent = percent;
        }
    }

    // Single cell with direction:
    public class PlayerInputItemSingleCellWithDirections : PlayerInputItemSingleCell
    {
        public readonly SimplifiedDirections Direction;

        public PlayerInputItemSingleCellWithDirections(Coords targetCoord, SimplifiedDirections direction) : base(targetCoord)
        {
            Direction = direction;
        }

        public override bool ShouldEndSimulation()
        {
            return false;
        }
    }
    public sealed class PlayerInputItemCreateLineBreaker : PlayerInputItemSingleCellWithDirections
    {
        public override BoosterItem Item => BoosterItem.CreateLineBreaker;

        public PlayerInputItemCreateLineBreaker(Coords targetCoord, SimplifiedDirections direction) : base(targetCoord, direction)
        {
        }
    }
    
    public sealed class PlayerInputItemCreateLineBreakerButler : PlayerInputItemSingleCellWithDirections
    {
        public override BoosterItem Item => BoosterItem.CreateLineBreakerButler;

        public PlayerInputItemCreateLineBreakerButler(Coords targetCoord, SimplifiedDirections direction) : base(targetCoord, direction)
        {
        }
    }

    public enum PlayerInputType
    {
        None,
        Swap,
        Item,
        Tap,
        DTap,
        Empty,
    }

    [Flags]
    public enum BoosterItem
    {
        None = 0,

        Reshuffle = 1,
        Shovel = 2,
        CreateLineBreaker = 4,
        CreateBomb = 8,
        CreateColorBomb = 16,
        Balloon = 32,
        Wind = 64,
        Rain = 128,
        ExtraMove = 256,
        CreatePropeller= 512,
        CreateBombButler = 1024,
        CreateColorBombButler = 2048,
        CreateLineBreakerButler = 4096,
        CreatePropellerButler = 8192,
        Vertical = 16384,
        Horizontal = 32768,

        All = Reshuffle | Shovel | CreateLineBreaker | CreateBomb | CreateColorBomb | Balloon | Wind | Rain |
              ExtraMove | CreatePropeller | CreateBombButler | CreateColorBombButler | CreateLineBreakerButler |
              CreatePropellerButler | Vertical | Horizontal
    }
}
