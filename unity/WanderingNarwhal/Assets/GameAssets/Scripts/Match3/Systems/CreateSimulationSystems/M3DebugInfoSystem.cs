using System;
using System.Collections.Generic;
using System.IO;
using BBB.CellTypes;
using BBB.M3Editor;
using BBB.Match3.Systems.Exceptions;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    [Flags]
    public enum StopReason
    {
        None = 0,
        TileIsNull = 1,
        TileIsZeroGravity = 2,
        CellIsLocked = 4,
        DropItemDespawn = 8,
        PathBlocked = 16,
        CouldNotMoveSouth = 64,
        NoSouthDirFoundInRoutes = 128,
    }
    
    public class M3DebugInfoSystem
    {
        private const string ReasonNotFound = "Reason not found";
        public static bool AllowSnapShots { get; set; }
        private static readonly bool _internalAllowSnapshots = false;

        private static ILevel _level;

        public static void SetupLevel(ILevel level)
        {
            _level = level;
        }

        public static void CleanupLevel()
        {
            _level = null;
        }

        public static void SnapshotGrid(Grid startGrid, Grid exceptionGrid)
        {
#if UNITY_EDITOR
            if (!_internalAllowSnapshots) return;
            if (!AllowSnapShots) return;
            if (_level == null) return;
            
            try
            {
                var now = DateTime.Now;
                var filePath = Application.dataPath + "/GameAssets/_Sandbox/LevelSnapshots/"
                                                    + _level.Config.Uid + "_"
                                                    + now.Millisecond.GetHashCode();

                var dir = System.IO.Path.GetDirectoryName(filePath + "_start.M3L.bytes");
                if (!dir.IsNullOrEmpty())
                {
                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(dir);
                    }

                    Save(_level, startGrid, filePath + "_start.M3L.bytes");
                    Save(_level, exceptionGrid, filePath + "_exception.M3L.bytes");
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError("Shapshot grid exception:\n" + ex);
            }
#endif
        }

        private static void Save(ILevel sourceLevel, Grid grid, string filePath)
        {
            var prevGrid = sourceLevel.Grid;
            sourceLevel.Grid = grid;
            M3SaveLoadUtility.SaveLevelIntoFile(sourceLevel, filePath);
            sourceLevel.Grid = prevGrid;
        }
    
        private readonly Dictionary<Coords, StopReason> _debugCellSkipReasonsDict = new();

        public void DebugFindUndefinedTilesCheck(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                if (!ReferenceEquals(cell.Tile, null))
                {
                    if (cell.Tile.Kind == TileKinds.Undefined)
                    {
                        throw new Exception($"Undefined tile found in {cell.Coords}");
                    }
                }
            }
        }
        
        public void FindStuckColumns(Grid grid)
        {
            grid.RefrehsAllCellsMultisizeCaches();
            
            foreach (var cell in grid.Cells)
            {
                if (ReferenceEquals(cell.Tile, null) && !cell.HasMultiSizeCellReference())
                {
                    var northCoord = cell.Coords.GoSingleCardinalDirection(CardinalDirections.N);
                    grid.TryGetCell(northCoord, out var northCell);
                    if (northCell != null && !ReferenceEquals(northCell.Tile, null)
                                          && !northCell.Tile.IsAnyOf(TileState.ZeroGravity)
                                          && !cell.IsAnyOf(CellState.Water)
                                          && !northCell.HasAnyWall(CardinalDirections.S)
                                          && northCell.GetMainCellReference(out Coords _, true).TntCount == 0)
                    {
                        var str = "ERROR: falling not finished in " + northCoord + " " +
                                  ((_debugCellSkipReasonsDict.TryGetValue(northCoord, out var reason)
                                       ? reason.ToString()
                                       : ReasonNotFound)
                                   + " Busy: " + northCell.IsBusy);

                        throw new TileFallNotFinishedException(str);
                    }
                }
            }
        }

        public void Clear()
        {
            _debugCellSkipReasonsDict.Clear();
        }

        public void AddFallStopReason(Coords cellCoords, StopReason reason)
        {
            _debugCellSkipReasonsDict.Add(cellCoords, reason);
        }
    }
}