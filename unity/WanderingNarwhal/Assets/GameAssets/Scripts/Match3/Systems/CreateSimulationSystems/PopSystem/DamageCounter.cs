using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.PopSystem
{
    public class DamageCounter
    {
        private readonly HashSet<int> _tileHitIdFilter = new HashSet<int>();
        
        //external dictionary int key is tile id, internal dictionary int value is number of hits on this move
        private readonly Dictionary<int, Dictionary<DamageSource, int>> _tileDamageCounterMapPerTurn =
            new Dictionary<int, Dictionary<DamageSource, int>>();
        
        private readonly Dictionary<int, Dictionary<DamageSource, int>> _tileDamageCounterMapPerStep =
            new Dictionary<int, Dictionary<DamageSource, int>>();

        public void ClearPerTurn()
        {
            _tileHitIdFilter.Clear();
            _tileDamageCounterMapPerTurn.Clear();
        }

        public void ClearPerStep()
        {
            _tileDamageCounterMapPerStep.Clear();
        }

        public bool IsFiltered(int tileSourceId)
        {
            return _tileHitIdFilter.Contains(tileSourceId);
        }

        public void AddTileHitId(int tileSourceId)
        {
            _tileHitIdFilter.Add(tileSourceId);
        }
        
        public int GetCountForDamagePerStep(int tileId, DamageSource damageSource)
        {
            if (_tileDamageCounterMapPerStep.TryGetValue(tileId, out var dict))
            {
                if (dict.TryGetValue(damageSource, out var count))
                    return count;
            }

            return 0;
        }

        public void IncrementDamageCountPerStep(int tileId, DamageSource damageSource)
        {
            if (_tileDamageCounterMapPerStep.TryGetValue(tileId, out var dict))
            {
                if (dict.TryGetValue(damageSource, out var count))
                    dict[damageSource] = count+1;
                else
                {
                    dict[damageSource] = 1;
                }
            }
            else
            {
                _tileDamageCounterMapPerStep[tileId] = new Dictionary<DamageSource, int>
                {
                    {damageSource, 1}
                };
            }
        }
    
        public int GetCountForDamagePerTurn(int tileId, DamageSource damageSource)
        {
            if (_tileDamageCounterMapPerTurn.TryGetValue(tileId, out var dict))
            {
                if (dict.TryGetValue(damageSource, out var count))
                    return count;
            }

            return 0;
        }

        public void IncrementDamageCountPerTurn(int tileId, DamageSource damageSource)
        {
            if (_tileDamageCounterMapPerTurn.TryGetValue(tileId, out var dict))
            {
                if (dict.TryGetValue(damageSource, out var count))
                    dict[damageSource] = count+1;
                else
                {
                    dict[damageSource] = 1;
                }
            }
            else
            {
                _tileDamageCounterMapPerTurn[tileId] = new Dictionary<DamageSource, int>
                {
                    {damageSource, 1}
                };
            }
        }
    }
}