using System;
using System.Collections.Generic;
using System.Text;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.PopSystem;

namespace BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes
{
    public abstract class Hit
    {
        public static ulong HitCounter;
        public readonly ulong HitId;
        public readonly int BusyWait;
        public readonly DamageSource DamageSource;
        public readonly IBoostInfo BoostInfo;
        public readonly HashSet<Tile> ImmuneTiles;
        public readonly TileKinds SourceKind;
        public readonly AdditionalEffect AdditionalEffect;
        public readonly Coords Coords;
        public readonly Coords SourceCoords;
        public readonly int HitSourceUid;
        public readonly int HitDistance;
        public readonly DamageSource PreviousDamageSource;

        private readonly List<int> _matchIds;
        private readonly Dictionary<TileKinds, HashSet<Coords>> _sameTileMatchSet;

        public abstract IGridActor GetTarget();

        protected Hit(HashSet<Tile> immuneTiles, int busyWait,
            DamageSource damageSource, AdditionalEffect additionalEffect, IBoostInfo boostInfo,
            TileKinds sourceKind = TileKinds.None,
            List<int> automatchIds = null,
            Coords coords = default(Coords),
            Coords sourceCoords = default(Coords),
            int hitSourceUid = -1,
            int hitDistance = 0,
            Dictionary<TileKinds, HashSet<Coords>> sameTileMatchSet = null,
            DamageSource previousDamageSource = DamageSource.None)
        {
            ImmuneTiles = immuneTiles;
            BusyWait = busyWait;
            _matchIds = automatchIds;
            DamageSource = damageSource;
            AdditionalEffect = additionalEffect;
            BoostInfo = boostInfo;
            SourceKind = sourceKind;
            Coords = coords;
            SourceCoords = sourceCoords;
            HitSourceUid = hitSourceUid;
            HitId = HitCounter++;
            HitDistance = hitDistance;
            _sameTileMatchSet = sameTileMatchSet;
            PreviousDamageSource = previousDamageSource;
        }

        public HitWaitParams GetHitParams()
        {
            var hitParams = new HitWaitParams(Coords, DamageSource, SourceCoords)
                .AddBoostInfo(BoostInfo)
                .AddMatchIds(_matchIds?.ToArray())
                .AddHitSourceUid(HitSourceUid)
                .AddHitDistance(HitDistance)
                .AddMatchSet(_sameTileMatchSet);
            return hitParams;
        }

        public bool CanApplyDamageToTile()
        {
            return AdditionalEffect == AdditionalEffect.None;
        }
    }

    public sealed class Hit<T> : Hit, IEquatable<Hit> where T : IGridActor
    {
        public readonly T Target;

        public override IGridActor GetTarget()
        {
            return Target;
        }

        public Hit(HashSet<Tile> immuneTiles, T target, int busyWait = 0,
            DamageSource damageSource = DamageSource.None, AdditionalEffect additionalEffect = AdditionalEffect.None,
            IBoostInfo boostInfo = null, TileKinds sourceKind = TileKinds.None,
            List<int> matchIds = null,
            Coords coords = default(Coords),
            Coords sourceCoords = default(Coords),
            int hitSourceUid = -1,
            int hitDistance = 0,
            Dictionary<TileKinds, HashSet<Coords>> sameTileMatchSet = null,
            DamageSource previousDamageSource = DamageSource.None
        )
            : base(immuneTiles, busyWait, damageSource, additionalEffect, boostInfo,
                sourceKind, matchIds, coords, sourceCoords, hitSourceUid, 
                hitDistance, sameTileMatchSet, previousDamageSource)
        {
            Target = target;
        }

        public List<Hit> GetAdjacentHits(Tile sourceTile, Grid grid, DamageSource damageSource, CardinalDirections adjacentDamageShape, int hitSourceUid = -1)
        {
            //this check guarantees that tile would die if it takes a single hit
            //we spawn adjacent damage only in this case
            if (!MatchHelper.WillTileDieIfHit(sourceTile)) return null;
            if (adjacentDamageShape == CardinalDirections.None) return null;

            var hits = new HashSet<Hit>();
            var directions = adjacentDamageShape.SplitIntoSingleDirections();
            var adjacentDamageType = damageSource == DamageSource.RemoveColorTiles
                ? DamageSource.AdjacentFromBoost
                : DamageSource.Adjacent;
            foreach (var direction in directions)
            {
                CheckAndAddHit(hits, grid, direction, adjacentDamageType, hitSourceUid);
            }

            return new List<Hit>(hits);
        }

        private void CheckAndAddHit(HashSet<Hit> hits, Grid grid, CardinalDirections cardinalDirections,
            DamageSource adjacentDamageType, int hitSourceUid = -1)
        {
            T actor;
            var coords = Target.GetCoords(grid).GoSingleCardinalDirection(cardinalDirections);
            if (grid.GetGridActor(coords, out actor))
            {
                var hit = new Hit<T>(
                    immuneTiles: null,
                    target: actor,
                    busyWait: M3Constants.BusyTimeOnStickerRemoval,
                    damageSource: adjacentDamageType,
                    sourceKind: SourceKind, coords: coords,
                    hitSourceUid: hitSourceUid);
                hits.Add(hit);
            }
        }

        public bool Equals(Hit other)
        {
            return other != null
                   && (Target?.Equals(other.GetTarget()) ?? other.GetTarget() == null)
                   && BusyWait == other.BusyWait
                   && DamageSource == other.DamageSource
                   && (BoostInfo?.BoostEquals(other.BoostInfo) ?? other.BoostInfo == null)
                   && Equals(ImmuneTiles, other.ImmuneTiles)
                   && HitSourceUid == other.HitSourceUid;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            return obj is Hit && Equals((Hit)obj);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = Target == null ? 7 : Target.GetHashCode();
                hashCode = (hashCode * 397) ^ BusyWait;
                hashCode = (hashCode * 397) ^ DamageSource.GetHashCode();
                hashCode = (hashCode * 397) ^ (ImmuneTiles != null ? ImmuneTiles.GetHashCode() : 0);
                hashCode = (hashCode * 397) ^ HitSourceUid;
                return hashCode;
            }
        }

        public override string ToString()
        {
            var sb = new StringBuilder(50);
            sb.Append(Target)
                .Append(" Busy=")
                .Append(BusyWait)
                .Append(" DamageType: ")
                .Append(DamageSource.ToString<DamageSource>());

            if (ImmuneTiles != null && ImmuneTiles.Count != 0)
            {
                sb.Append(" Immune tiles: ");
                foreach (var tile in ImmuneTiles)
                {
                    const string space = " ";
                    sb.Append(tile).Append(space);
                }
            }

            sb.Append(" HitSourceUid=")
                .Append(HitSourceUid);

            return sb.ToString();
        }

    }
}