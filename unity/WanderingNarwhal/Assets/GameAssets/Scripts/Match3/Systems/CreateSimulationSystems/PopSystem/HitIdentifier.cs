using BBB;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.PopSystem
{
    public struct HitIdentifier
    {
        public DamageSource DamageSource;
        public int HitSourceUid;
        public int TileId;
        public Coords? TargetCoords;

        public override bool Equals(object obj)
        {
            return obj is HitIdentifier hitIdentifier &&
                   DamageSource == hitIdentifier.DamageSource &&
                   HitSourceUid == hitIdentifier.HitSourceUid &&
                   TileId == hitIdentifier.TileId &&
                   (!TargetCoords.HasValue ||
                    !hitIdentifier.TargetCoords.HasValue ||
                    TargetCoords.Value.Equals(hitIdentifier.TargetCoords.Value));
        }
            
        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = DamageSource.GetHashCode();
                hashCode = (hashCode * 397) ^ HitSourceUid;
                hashCode = (hashCode * 397) ^ TileId;
                return hashCode;
            }
        }
    }
}