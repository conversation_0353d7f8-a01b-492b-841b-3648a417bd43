using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes
{
    public sealed class Hits
    {
        public readonly HashSet<Hit> HitList;

        public Hits(Hit hit)
        {
            HitList = new HashSet<Hit> { hit };
        }

        public Hits(HashSet<Hit> hits)
        {
            HitList = hits;
        }

        public static Hits operator +(Hits first, Hits second)
        {
            if (first == null) return second;
            if (second == null) return first;

            first.HitList.UnionWith(second.HitList);

            return first;
        }

        public override string ToString()
        {
            string str = "";
            foreach (var hit in HitList)
            {
                str += hit + ", ";
            }
            return str.TrimEnd(',', ' ');
        }
    }
}
