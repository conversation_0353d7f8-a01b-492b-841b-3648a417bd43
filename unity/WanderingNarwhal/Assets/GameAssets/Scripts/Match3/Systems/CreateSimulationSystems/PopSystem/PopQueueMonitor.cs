#if M3_QUEUE_DEBUG

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    /// <summary>
    /// Monitor system that handles recording of match3 damage system steps.
    /// The recorded data is than displayed in M3 Utils editor window.
    /// </summary>
    /// <remarks>
    /// M3 Damage is implemented with controller class `PopSystem`
    /// and container class `Queue` which contains details for certain damage for tiles and cells during simulation.
    ///
    /// PopQueueMonitor hooks up into these classes in certain places to capture snapshots of data. -VK
    /// </remarks>
    public class PopQueueMonitor
    {
        public static int CurrentFrame { get; set; }

        public static bool LogsEnabled { get; set; }

        private static PlayerInputType _inputType;

        public static int SimulationSnapshotsCount
        {
            get { return _simulationSnapshots.Count; }
        }

        public static int StepsSnapshotsInSimulationAtIndex(int simIndex)
        {
            if (simIndex < 0 || simIndex >= _simulationSnapshots.Count)
            {
                return 0;
            }

            return _simulationSnapshots[simIndex].allPastFramesSnapshots.Count;
        }

        private static List<SimulationSnapshot> _simulationSnapshots = new List<SimulationSnapshot>();

        private static SimulationSnapshot GetCurrentSimSnapshot()
        {
            if (_simulationSnapshots.Count > 0)
            {
                return _simulationSnapshots[_simulationSnapshots.Count - 1];
            }

            return null;
        }

        public static SimulationSnapshot GetSimulationSnapshot(int simIndex)
        {
            return _simulationSnapshots[simIndex];
        }

        public static QueueSnapshot GetSnapshot(int simIndex, int stepIndex)
        {
            return _simulationSnapshots[simIndex].allPastFramesSnapshots[stepIndex];
        }

        /// <summary>
        /// Record the delayed hit to cell.
        /// </summary>
        /// <remarks>
        /// Recorded hit is included in the damage simulation snapshot.
        /// </remarks>
        public static void RegisterDelayWithHit(string prefix, int delay, Hit hit, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                activeSnapshot.state.AddDelayWithHit(prefix, delay, hit, out var alreadyExists);
                PrintLog($"Register delay '{delay}' with hit '{prefix}' at <{hit.Coords}>, alreadyExists={alreadyExists}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record hit to tile.
        /// </summary>
        /// <remarks>
        /// Recorded hit is included in the damage simulation snapshot.
        ///
        /// The hit is stored as `TileHitOneInTurn` in Queue class,
        /// so the register method name is same for consistency (even if it's not very clear). 
        /// </remarks>
        public static void RegisterTileHitOneInTurn(string prefix, Hit<Tile> hit, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                var hitSnapshot = new HitSnapshot(prefix, hit);
                bool alreadyExists = false;
                foreach (var h in activeSnapshot.state.tilesToDamageOneInTurn)
                {
                    if (h.prefix == hitSnapshot.prefix && h.coords == hitSnapshot.coords)
                    {
                        alreadyExists = true;
                        break;
                    }
                }

                if (!alreadyExists)
                {
                    activeSnapshot.state.tilesToDamageOneInTurn.Add(hitSnapshot);
                }

                PrintLog($"Register tile hit '{prefix}' at <{hit.Coords}> [{hit.DamageSource}], alreadyExists = {alreadyExists}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the end-turn hit to tiles (usually this is only blinking tiles).
        /// </summary>
        /// <remarks>
        /// Recorded hit is included in the damage simulation snapshot.
        /// </remarks>
        public static void RegisterTileToBlowUpAtEnd(string prefix, Hit<Tile> hit, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                var hitSnapshot = new HitSnapshot(prefix, hit);
                bool alreadyExists = false;
                foreach (var h in activeSnapshot.state.tilesToBlowUpInTheEnd)
                {
                    if (h.prefix == hitSnapshot.prefix && h.coords == hitSnapshot.coords)
                    {
                        alreadyExists = true;
                        break;
                    }
                }

                if (!alreadyExists)
                {
                    activeSnapshot.state.tilesToBlowUpInTheEnd.Add(hitSnapshot);
                }

                PrintLog($"Register end tile hit '{prefix}' at <{hit.Coords}> [{hit.DamageSource}], alreadyExists = {alreadyExists}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the hit to cell at the end of simulation (when other steps of simulations already finished).
        /// </summary>
        /// <remarks>
        /// Recorded hit is included in the damage simulation snapshot.
        /// </remarks>
        public static void RegisterCellHitAtEnd(string prefix, Hit<Cell> hit, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                bool alreadyExists = false;
                foreach (var h in activeSnapshot.state.cellsToHitInTheEnd)
                {
                    if (h.coords == hit.Coords)
                    {
                        alreadyExists = true;
                        break;
                    }
                }

                if (!alreadyExists)
                {
                    var hitSnapshot = new HitSnapshot(prefix, hit);
                    activeSnapshot.state.cellsToHitInTheEnd.Add(hitSnapshot);
                }

                PrintLog($"Register cell hit '{prefix}' at {hit.Coords} [{hit.DamageSource}], alreadyExists = {alreadyExists}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the cell that become busy.
        /// </summary>
        /// <remarks>
        /// Busy cell are locked so they can't receive tiles. 
        /// </remarks>
        public static void RegisterBusyCell(string prefix, Cell cell, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                bool alreadyExists = false;
                for (int i = 0; i < activeSnapshot.state.busyCells.Count; i++)
                {
                    if (activeSnapshot.state.busyCells[i].coords == cell.Coords)
                    {
                        alreadyExists = true;
                        activeSnapshot.state.busyCells[i].waitValue = cell.IsBusy;
                        break;
                    }
                }

                if (!alreadyExists)
                {
                    activeSnapshot.state.busyCells.Add(new CellSnapshot() { prefix = prefix, coords = cell.Coords, waitValue = cell.IsBusy });
                }

                PrintLog($"Register cell busy '{cell.IsBusy}' at {cell.Coords}, alreadyExists={alreadyExists} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the cell that become frozen.
        /// </summary>
        /// <remarks>
        /// Frozen cell is locked similarly to busy cells.
        /// </remarks>
        public static void RegisterFrozenCell(string prefix, Cell cell, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                bool alreadyExists = false;
                for (int i = 0; i < activeSnapshot.state.frozenCells.Count; i++)
                {
                    if (activeSnapshot.state.frozenCells[i].coords == cell.Coords)
                    {
                        alreadyExists = true;
                        activeSnapshot.state.frozenCells[i].waitValue = cell.IsLocked;
                        break;
                    }
                }

                if (!alreadyExists)
                {
                    activeSnapshot.state.frozenCells.Add(new CellSnapshot() { prefix = prefix, coords = cell.Coords, waitValue = cell.IsLocked });
                }

                PrintLog($"Register cell frozen '{cell.IsLocked}' at {cell.Coords}, alreadyExists={alreadyExists} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the clearance of the Queue instance.
        /// </summary>
        public static void RegisterQueueClear(int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                if (sim == null) return;
                var activeShanpshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeShanpshot.state.Clear();
                PrintLog($"Register queue clear <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the routes recalculation flag change in Queue.
        /// </summary>
        public static void RegisterShouldRecalculateRoutes(string prefix, bool isTrue, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                activeSnapshot.state.shouldRecalculateRoutes = isTrue;
                PrintLog($"Register should recalculate routes = {isTrue} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the delay before simulation end change in Queue.
        /// </summary>
        public static void RegisterDelayBeforeEnd(string prefix, int delay, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                activeSnapshot.state.delayBeforeEnd = delay;
                PrintLog($"Register delay before end: {delay} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the end cell hit removal.
        /// </summary>
        /// <remarks>
        /// Hits are removed from the Queue when it is applied to the grid.
        /// </remarks>
        public static void RegisterRemoveEndCellHit(string prefix, Coords expectedCoords, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                Coords removedHitCoord = new Coords(-1, -1);

                if (activeSnapshot.state.cellsToHitInTheEnd.Count > 0)
                {
                    removedHitCoord = activeSnapshot.state.cellsToHitInTheEnd[0].coords;
                    activeSnapshot.state.cellsToHitInTheEnd.RemoveAt(0);
                }

                if (removedHitCoord != expectedCoords)
                {
                    PrintError($"RegisterRemoveEndTileHit doesn't match expected coord: {removedHitCoord} <> {expectedCoords}");
                }

                PrintLog($"Register remove end cell hit at {expectedCoords} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the end tile hit removal.
        /// </summary>
        /// <remarks>
        /// Hits are removed from the Queue when it is applied to the grid.
        /// </remarks>
        public static void RegisterRemoveEndTileHit(string prefix, Coords expectedCoords, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                Coords removedHitCoord = new Coords(-1, -1);

                if (activeSnapshot.state.tilesToBlowUpInTheEnd.Count > 0)
                {
                    removedHitCoord = activeSnapshot.state.tilesToBlowUpInTheEnd[0].coords;
                    activeSnapshot.state.tilesToBlowUpInTheEnd.RemoveAt(0);
                }

                if (removedHitCoord != expectedCoords)
                {
                    PrintError($"RegisterRemoveEndTileHit doesn't match expected coord: {removedHitCoord} <> {expectedCoords}");
                }

                PrintLog($"Register remove end tile hit at {expectedCoords} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the HitOneInTurn removal.
        /// </summary>
        /// <remarks>
        /// Hits are removed from the Queue when it is applied to the grid.
        /// </remarks>
        public static void RegisterRemoveHitOneInTurn(string prefix, Coords expectedCoords, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                Coords removedHitCoord = new Coords(-1, -1);

                if (activeSnapshot.state.tilesToDamageOneInTurn.Count > 0)
                {
                    removedHitCoord = activeSnapshot.state.tilesToDamageOneInTurn[0].coords;
                    activeSnapshot.state.tilesToDamageOneInTurn.RemoveAt(0);
                }

                if (removedHitCoord != expectedCoords)
                {
                    PrintError($"RemovedHitOneInTurn doesn't match expected coord: {removedHitCoord} <> {expectedCoords} <{queueUid}>");
                }

                PrintLog($"Register remove hit one in turn at {expectedCoords} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the ZeroDelayHit removal.
        /// </summary>
        /// <remarks>
        /// Hits are removed from the Queue when it is applied to the grid.
        /// </remarks>
        public static void RegisterRemoveZeroDelayHits(string prefix, int expectedCountToRemove, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;

                int removedCount = 0;
                if (activeSnapshot.state.delaysWithHits.ContainsKey(0))
                {
                    removedCount = activeSnapshot.state.delaysWithHits[0].Count;
                    activeSnapshot.state.delaysWithHits[0].Clear();
                }

                if (removedCount != expectedCountToRemove)
                {
                    PrintError($"Removed monitored hits doesn't match expected count: {removedCount} <> {expectedCountToRemove} <{queueUid}>");
                }

                PrintLog($"Register remove zero delay hits '{expectedCountToRemove}' <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the hit applied to the grid.
        /// </summary>
        public static void RegisterAppliedHit(string prefix, Hit hit, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;

                activeSnapshot.state.appliedDamage.Add(hit.Coords);

                PrintLog($"Add applied damage hit at '{hit.Coords}' <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the delay change for hit.
        /// </summary>
        /// <remarks>
        /// Delay is decreased on each m3 simulation step.
        /// </remarks>
        public static void RegisterDecreaseDelayForHitsWithDelay(string prefix, int delayKey, int expectedCount, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;

                int movedCount = 0;
                if (activeSnapshot.state.delaysWithHits.ContainsKey(delayKey))
                {
                    var list = activeSnapshot.state.delaysWithHits[delayKey];
                    activeSnapshot.state.delaysWithHits.Remove(delayKey);
                    activeSnapshot.state.delaysWithHits[delayKey - 1] = list;
                    movedCount = list.Count;
                }

                if (movedCount != expectedCount)
                {
                    PrintError($"Delay shift for hits at key '{delayKey}' doesn't match expected count: {movedCount} <> {expectedCount}, <{queueUid}>, prefix={prefix}");
                }

                PrintLog($"Register decrease delay for hits: {delayKey} count='{expectedCount}' <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the busy cell delay change for hit.
        /// </summary>
        /// <remarks>
        /// Delay is decreased on each m3 simulation step.
        /// </remarks>
        public static void RegisterDecreaseBusyCell(string prefix, Cell cell, int expectedNewValue, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;

                bool isWaitEnd = false;
                CellSnapshot foundItem = null;

                foreach (var cellSnapshot in activeSnapshot.state.busyCells)
                {
                    if (cellSnapshot.coords == cell.Coords)
                    {
                        cellSnapshot.waitValue--;
                        if (cellSnapshot.waitValue != expectedNewValue)
                        {
                            PrintError($"Busy cell value doesn't match expected value: {cellSnapshot.waitValue} <> {expectedNewValue} at {cell.Coords}, <{queueUid}>");
                        }

                        foundItem = cellSnapshot;
                        isWaitEnd = cellSnapshot.waitValue <= 0;

                        break;
                    }
                }

                if (!(foundItem is null))
                {
                    if (isWaitEnd)
                    {
                        activeSnapshot.state.busyCells.Remove(foundItem);
                    }

                    PrintLog($"Register decrease busy cell at {cell.Coords} with wait={expectedNewValue} <{queueUid}>");
                    return;
                }

                PrintError($"Busy cell not found at coords: {cell.Coords}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the frozen cell delay change for hit.
        /// </summary>
        /// <remarks>
        /// Delay is decreased on each m3 simulation step.
        /// </remarks>
        public static void RegisterDecreaseFrozenCell(string prefix, Cell cell, int expectedNewValue, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;

                bool isWaitEnd = false;
                CellSnapshot foundItem = null;

                foreach (var cellSnapshot in activeSnapshot.state.frozenCells)
                {
                    if (cellSnapshot.coords == cell.Coords)
                    {
                        cellSnapshot.waitValue--;
                        if (cellSnapshot.waitValue != expectedNewValue)
                        {
                            PrintError($"Frozen cell value doesn't match expected value: {cellSnapshot.waitValue} <> {expectedNewValue} at {cell.Coords} <{queueUid}>");
                        }

                        foundItem = cellSnapshot;
                        isWaitEnd = cellSnapshot.waitValue <= 0;

                        break;
                    }
                }

                if (!(foundItem is null))
                {
                    if (isWaitEnd)
                    {
                        activeSnapshot.state.frozenCells.Remove(foundItem);
                    }

                    PrintLog($"Register decrease frozen cell at {cell.Coords} with wait={expectedNewValue} <{queueUid}>");
                    return;
                }

                PrintError($"Frozen cell not found at coords: {cell.Coords}, prefix={prefix}, <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the new Queue instance.
        /// </summary>
        public static void RegisterNewQueue(string queueContext, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                if (sim == null) return;
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.context = queueContext;
                PrintLog($"Register new queue : {queueContext} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the start of changes to the Queue instance.
        /// </summary>
        /// <remarks>
        /// Usually during simulation we have multiple instances of Queue,
        /// and this helps track switch of context in simulation snapshot. 
        /// </remarks>
        public static void RegisterQueueStateBeginChange(string prefix, int queueUid)
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                if (sim == null) return;
                var activeSnapshot = sim.GetCurrentActiveSnapshot(queueUid);
                activeSnapshot.state.prefix = prefix;
                PrintLog($"Register queue state begin change: {prefix} <{queueUid}>");
            });
        }

        /// <summary>
        /// Record the input type on start of the move.
        /// </summary>
        public static void RegisterCurrentPlayerInputType(PlayerInputType inputType)
        {
            _inputType = inputType;
            PrintLog("Set player input type=" + inputType);
        }

        /// <summary>
        /// Start of new turn by player input.
        /// </summary>
        public static void RegisterNewSimulationProcessInvoked(string context)
        {
            InvokeActionSafe(() =>
            {
                var prevSim = _simulationSnapshots.Count > 0 ? _simulationSnapshots[_simulationSnapshots.Count - 1] : null;
                _simulationSnapshots.Add(new SimulationSnapshot(context, _inputType, prevSim));
                PrintLog($"Register new simulation invoke: "+context);
            });
        }

        /// <summary>
        /// Record the start of next m3 simulation step.
        /// </summary>
        public static void RegisterNewGravitySimulationStep()
        {
            InvokeActionSafe(() =>
            {
                var sim = GetCurrentSimSnapshot();
                sim.IncGravitySimStep();
                PrintLog("Register gravity next sim step");
            });
        }

        private static void PrintLog(string msg)
        {
            if (LogsEnabled)
            {
                UnityEngine.Debug.Log("#DamageMonitor# " + msg);
            }
        }

        private static void PrintError(string msg)
        {
            UnityEngine.Debug.LogError("#DamageMonitor# "+msg);
        }

        /// <summary>
        /// Invoke delegate and catch-ignore exceptions.
        /// Used only to record debug snapshots so it is safe to skip any exception.
        /// </summary>
        private static void InvokeActionSafe(Action action)
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogException(ex);
            }
        }

        public static void Clear()
        {
            _simulationSnapshots.Clear();
        }

        /// <summary>
        /// Simulation snapshot container.
        /// New simulation holds list of actions that happened during one complete logical 'turn'.
        /// During the simulation multiple damange Queue's are created and their state change is also recorded here.
        /// The snapshot then can be browsed in M3 Util window. -VK
        /// </summary>
        public class SimulationSnapshot
        {
            public string context;
            public PlayerInputType playerInputType;
            public List<QueueSnapshot> allPastFramesSnapshots = new List<QueueSnapshot>();
            private SimulationSnapshot _previousSimulationRef;
            private int _gravitySimulationStep;

            public SimulationSnapshot(string context, PlayerInputType inputType, SimulationSnapshot previousSimulationRef = null)
            {
                this.context = context;
                this.playerInputType = inputType;
                _previousSimulationRef = previousSimulationRef;
            }

            public void IncGravitySimStep()
            {
                _gravitySimulationStep++;
            }

            public QueueSnapshot GetCurrentActiveSnapshot(int queueUid)
            {
                QueueState sameQueueState = null;
                for (int i = allPastFramesSnapshots.Count - 1; i >= 0; i--)
                {
                    if (allPastFramesSnapshots[i].queueUid == queueUid)
                    {
                        sameQueueState = allPastFramesSnapshots[i].state;
                        break;
                    }
                }

                if (sameQueueState is null && !(_previousSimulationRef is null))
                {
                    for (int i = _previousSimulationRef.allPastFramesSnapshots.Count - 1; i >= 0; i--)
                    {
                        if (_previousSimulationRef.allPastFramesSnapshots[i].queueUid == queueUid)
                        {
                            sameQueueState = _previousSimulationRef.allPastFramesSnapshots[i].state;
                            break;
                        }
                    }
                }

                QueueState state;
                if (sameQueueState is null)
                {
                    state = new QueueState();
                }
                else
                {
                    state = sameQueueState.Clone();
                }

                var result = new QueueSnapshot(CurrentFrame, queueUid, _gravitySimulationStep, state);
                allPastFramesSnapshots.Add(result);
                return result;
                
            }
        }

        public class QueueSnapshot
        {
            public int frameIndex;
            public int gravitySimulationStep;
            public int queueUid;
            public QueueState state;

            public QueueSnapshot(int frame, int queueUid, int gravitySimulationStep, QueueState state)
            {
                this.frameIndex = frame;
                this.queueUid = queueUid;
                this.gravitySimulationStep = gravitySimulationStep;
                this.state = state;
            }
        }

        public class QueueState
        {
            /// <summary>
            /// Short description of the creation purpose of this queue.
            /// </summary>
            public string context;

            /// <summary>
            /// Short description of current action.
            /// </summary>
            public string prefix;
            public SortedDictionary<int, List<HitSnapshot>> delaysWithHits = new SortedDictionary<int, List<HitSnapshot>>();
            public List<HitSnapshot> tilesToDamageOneInTurn = new List<HitSnapshot>();
            public List<HitSnapshot> tilesToBlowUpInTheEnd = new List<HitSnapshot>();
            public List<HitSnapshot> cellsToHitInTheEnd = new List<HitSnapshot>();
            public List<CellSnapshot> busyCells = new List<CellSnapshot>();
            public List<CellSnapshot> frozenCells = new List<CellSnapshot>();
            public List<Coords> appliedDamage = new List<Coords>();
            public bool shouldRecalculateRoutes;
            public int delayBeforeEnd;

            public void AddDelayWithHit(string prefix, int delay, Hit hit, out bool alreadyExists)
            {
                var hitSnapshot = new HitSnapshot(prefix, hit);
                alreadyExists = false;

                if (delaysWithHits.ContainsKey(delay))
                {
                    foreach (var h in delaysWithHits[delay])
                    {
                        if (h.prefix == hitSnapshot.prefix && h.coords == hitSnapshot.coords)
                        {
                            alreadyExists = true;
                            break;
                        }
                    }

                    //if (!alreadyExists) // Hits can repeat in queue.
                    {
                        delaysWithHits[delay].Add(new HitSnapshot(prefix, hit));
                    }
                }
                else
                {
                    delaysWithHits[delay] = new List<HitSnapshot>();
                    delaysWithHits[delay].Add(hitSnapshot);
                }
            }

            public void Clear()
            {
                prefix = null;
                delaysWithHits.Clear();
                tilesToDamageOneInTurn.Clear();
                tilesToBlowUpInTheEnd.Clear();
                cellsToHitInTheEnd.Clear();
                busyCells.Clear();
                frozenCells.Clear();
                appliedDamage.Clear();
                shouldRecalculateRoutes = false;
                delayBeforeEnd = 0;
            }

            public QueueState Clone()
            {
                var clone = new QueueState();
                clone.context = context;
                clone.prefix = prefix;
                foreach (var kv in delaysWithHits)
                {
                    var cloneHits = new List<HitSnapshot>();
                    foreach (var h in kv.Value)
                    {
                        cloneHits.Add(h.Clone());
                    }
                    clone.delaysWithHits.Add(kv.Key, cloneHits);
                }

                foreach (var h in tilesToDamageOneInTurn)
                {
                    clone.tilesToDamageOneInTurn.Add(h.Clone());
                }
                foreach (var h in tilesToBlowUpInTheEnd)
                {
                    clone.tilesToBlowUpInTheEnd.Add(h.Clone());
                }
                foreach (var h in cellsToHitInTheEnd)
                {
                    clone.cellsToHitInTheEnd.Add(h.Clone());
                }
                foreach (var c in busyCells)
                {
                    clone.busyCells.Add(c.Clone());
                }
                foreach (var c in frozenCells)
                {
                    clone.frozenCells.Add(c.Clone());
                }
                clone.shouldRecalculateRoutes = shouldRecalculateRoutes;
                clone.delayBeforeEnd = delayBeforeEnd;
                return clone;
            }
        }

        public class CellSnapshot
        {
            public string prefix;
            public Coords coords;
            public int waitValue;

            public CellSnapshot Clone()
            {
                return (CellSnapshot)MemberwiseClone();
            }
        }

        public class HitSnapshot
        {
            public string prefix;
            public bool isTileHit;
            public Coords coords;
            public DamageSource damageSource;
            public List<int> immuneTiles;
            public int busyWait;
            public CardinalDirections adjacentDamageShape;

            public HitSnapshot(string prefix, Hit hit)
            {
                this.prefix = prefix;
                isTileHit = hit is Hit<Tile>;
                coords = hit.Coords;
                damageSource = hit.DamageSource;
                if (hit.ImmuneTiles != null)
                {
                    immuneTiles = new List<int>(hit.ImmuneTiles.Count);
                    foreach (var tile in hit.ImmuneTiles)
                    {
                        immuneTiles.Add(tile.Id);
                    }
                }

                busyWait = hit.BusyWait;
                adjacentDamageShape = hit.AdjacentDamageShape;
            }

            public HitSnapshot Clone()
            {
                var clone = (HitSnapshot)MemberwiseClone();
                clone.immuneTiles = immuneTiles == null ? null : new List<int>(immuneTiles);
                return clone;
            }
        }
    }
}

#endif