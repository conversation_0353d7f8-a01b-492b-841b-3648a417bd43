using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BebopBee;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.PopSystem;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public struct HiddenAnimal
    {
        public int TileId;
        public Coords Coords;
        public Tile Tile;
        public HitWaitParams HitWaitParams;
    }

    public sealed class PopSystem
    {
        private int _matchesCounter;
        private GoalsSystem _goalSystem;
        private TileHitReactionHandler _reactionHandler;

        private IRootSimulationHandler _handler;
        private readonly List<HiddenAnimal> _hiddenAnimals = new();
        private MechanicTargetingManager _mechanicTargetingManager;

        private static readonly List<Coords> TempAnimalsSpreadCacheCoords = new();

        public M3SpawnSystem SpawnSystem;
        public SettleTilesSystem _settleTileSystem;

        private readonly HashSet<HitIdentifier> _hitFilter = new(20);
        private readonly Dictionary<Match, int> _matchesHitSourceUid = new(5);
        private int _matchHitSourceUid;

        private readonly Dictionary<TileKinds, HashSet<Coords>> _sameTileSetMatch = new();

        private ILevelAnalyticsReporter _levelAnalyticsReporter;
        private readonly DamageSource[] _damageSourcesToProcessAtLast = { DamageSource.Gondola, DamageSource.Propeller, DamageSource.FireWorks, DamageSource.Skunk, DamageSource.TapOrSwap };
        private readonly List<Hit> _targetingHits = new();

        public void Init(IRootSimulationHandler handler, GoalsSystem goalSystem, TileHitReactionHandler reactionHandler,
            MechanicTargetingManager mechanicTargetingManager,
            M3SpawnSystem spawnSystem, SettleTilesSystem settleTilesSystem, ILevelAnalyticsReporter levelAnalyticsReporter)
        {
            Clear();

            _handler = handler;
            _goalSystem = goalSystem;
            _reactionHandler = reactionHandler;
            _mechanicTargetingManager = mechanicTargetingManager;
            SpawnSystem = spawnSystem;
            _settleTileSystem = settleTilesSystem;
            _levelAnalyticsReporter = levelAnalyticsReporter;
        }

        private void Clear()
        {
            _matchesCounter = 0;
            _hitFilter.Clear();
        }

        internal void MarkAnimalReceivedDamage(Coords coords, int tileId, Tile tile, HitWaitParams hitWaitParams)
        {
            _hiddenAnimals.Add(new HiddenAnimal { Coords = coords, TileId = tileId, Tile = tile, HitWaitParams = hitWaitParams });
        }

        public bool ProcessNewMatches(
            Grid grid,
            TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem,
            Queue queue,
            ICollection<Match> matches,
            List<Coords> preferredCoords,
            IRootSimulationHandler handler,
            FixedSizeQueue<int> lastSettledTileIdsQueue)
        {
            var inputMatches = !preferredCoords.IsNullOrEmpty();
            var specialTileCalculationResult = SpecialTileCalculationResult.Spawn();
            if (matches != null && matches.Count != 0)
            {
                // Calculate places to spawn special tiles:
                CalculateSpecialTilesSystem.CalculateSpecialTiles(specialTileCalculationResult,
                    grid, matches,
                    preferredCoords, true, lastSettledTileIdsQueue);

                // Popping simple matches:
                var newCellsToDamage = AddSimpleMatchesToQueue(grid, matches, specialTileCalculationResult, inputMatches);
                queue.Append(newCellsToDamage);

                var longestMatchLength = 0;
                var longestMatchCorner = false;
                foreach (var match in matches)
                {
                    if (match.Length > longestMatchLength)
                    {
                        longestMatchLength = match.Length;
                        longestMatchCorner = specialTileCalculationResult.ContainsCornerMatch(match);
                    }
                }

                _handler.AddAction(new ActionMatchesSound(_matchesCounter, longestMatchLength, longestMatchCorner));
                _matchesCounter++;
            }

            var matchesAction =
                FormMatchesAnimationAction(grid, specialTileCalculationResult, goalSystem, matches, inputMatches);

            if (matchesAction != null)
            {
                _handler.AddAction(matchesAction);
            }

            // Recursive damage all cells around:
            var isTilePopped = DamageCells(grid, reactionHandler, inputParams, goalSystem, queue, queue,
                specialTileCalculationResult);

            if (!specialTileCalculationResult.IsEmpty())
            {
                // Spawning new cells:
                isTilePopped = SpawnSystem.SpawnPendingSpecialTiles(handler, grid, reactionHandler, specialTileCalculationResult, inputMatches) || isTilePopped;
            }

            specialTileCalculationResult.Release();

            return isTilePopped;
        }

        private ActionAnimateMatches FormMatchesAnimationAction(Grid grid,
            SpecialTileCalculationResult specialTileCalculationResult,
            GoalsSystem goalSystem,
            ICollection<Match> matches, bool inputMatches)
        {
            if (matches == null || matches.Count == 0)
                return null;

            var formationCoordsFilter = new HashSet<Coords>();
            var boostConvergeEvents =
                new List<ActionAnimateMatches.BoostConvergeEvent>();
            foreach (var spawnCoords in specialTileCalculationResult.CoordsToSpawn)
            {
                var formationCoords = specialTileCalculationResult.GetFormationCoords(spawnCoords);
                if (formationCoords == null) continue;
                formationCoords.Sort((a, b) =>
                    (a - spawnCoords).SqrMagnitude.CompareTo((b - spawnCoords).SqrMagnitude));
                var formationCoordsArray = formationCoords.ToArray();
                boostConvergeEvents.Add(new ActionAnimateMatches.BoostConvergeEvent
                {
                    SourceCoords = formationCoordsArray,
                    TargetCoords = spawnCoords
                });
                foreach (var coord in formationCoords) formationCoordsFilter.Add(coord);
            }

            return new ActionAnimateMatches(grid, boostConvergeEvents, formationCoordsFilter, matches, goalSystem,
                _reactionHandler, inputMatches);
        }


        private Queue AddSimpleMatchesToQueue(Grid grid, ICollection<Match> matches,
            SpecialTileCalculationResult specialTileCalculationResult, bool inputMatches)
        {
            var q = new Queue("AddSimpleMatchesToQ");
            var damageSource = inputMatches ? DamageSource.InputMatch : DamageSource.AutoMatch;

            foreach (var tileKind in _sameTileSetMatch) tileKind.Value.Clear();

            foreach (var match in matches)
            {
                var immuneTilesForMatch = specialTileCalculationResult.GetImmuneTilesForMatch(match);

                var matchIds = new List<int>();
                var hitSourceUid = GetMatchHitSourceUid(match);
                foreach (var coord in match)
                {
                    if (!grid.TryGetCell(coord, out var cell))
                        continue;

                    if (cell.Tile.GetParam(TileParamEnum.IceLayerCount) <= 0 &&
                        cell.Tile.GetParam(TileParamEnum.ChainLayerCount) <= 0)
                    {
                        if (_sameTileSetMatch.TryGetValue(cell.Tile.Kind, out var hashSet))
                            hashSet.Add(coord);
                        else
                            _sameTileSetMatch.Add(cell.Tile.Kind, new HashSet<Coords> { coord });
                    }

                    var hit = new Hit<Cell>(
                        immuneTilesForMatch,
                        cell,
                        M3Constants.BusyTimeSimple,
                        damageSource,
                        matchIds: matchIds,
                        sourceKind: cell.Tile == null ? TileKinds.None : cell.Tile.Kind,
                        coords: cell.Coords,
                        sameTileMatchSet: _sameTileSetMatch);
                    q.AppendHit(0, hit);

                    var adjacentDamageShape = match.GetMatchAdjacentShape(coord);
                    var adjacentHits =
                        hit.GetAdjacentHits(cell.Tile, grid, damageSource, adjacentDamageShape, hitSourceUid);

                    if (adjacentHits != null)
                        foreach (var adjacentHit in adjacentHits)
                            q.AppendHit(0, adjacentHit);

                    // If tile has the ZeroGravity flag, then it means that we should not wait for it to frozen, because
                    // it will not be falling
                    if (cell.HasTile() && !cell.Tile.IsAnyOf(TileState.ZeroGravity))
                        matchIds.Add(cell.Tile.Id);
                }
            }

            _matchesHitSourceUid.Clear();

            return q;
        }

        /// <summary>
        ///     Return the match hit source unique id. Overlapping matches will return the same unique id.
        /// </summary>
        /// <param name="match"></param>
        private int GetMatchHitSourceUid(Match match)
        {
            foreach (var (otherMatch, value) in _matchesHitSourceUid)
            {
                if (!match.Equals(otherMatch) && match.AnyOverlap(otherMatch))
                    return value;
            }

            _matchesHitSourceUid[match] = _matchHitSourceUid++;
            return _matchesHitSourceUid[match];
        }

        private bool DamageCells(Grid grid, TileHitReactionHandler reactionHandler, SimulationInputParams inputParams,
            GoalsSystem goalSystem, Queue q, Queue cellsToDamage,
            SpecialTileCalculationResult specialTileCalculationResult)
        {
            var isTilePopped = ApplyDamageToCells(grid, reactionHandler, inputParams, goalSystem, q, cellsToDamage,
                specialTileCalculationResult);

            HandleAnimals(grid, reactionHandler, inputParams, goalSystem, q, cellsToDamage);
            return isTilePopped;
        }

        private void HandleAnimals(Grid grid, TileHitReactionHandler reactionHandler, SimulationInputParams inputParams,
            GoalsSystem goalSystem, Queue queue, Queue cellsToDamage)
        {
            if (_hiddenAnimals.Count == 0)
                return;

            var collectedAnimals = 0;
            _hiddenAnimals.DeterministicShuffle();
            foreach (var hiddenAnimal in _hiddenAnimals)
            {
                var animalCoords = hiddenAnimal.Coords;
                var foundCellToRunTo = TryToPutAnimalInRandomCellAround(grid, animalCoords);

                if (!foundCellToRunTo)
                {
                    TempAnimalsSpreadCacheCoords.Clear();
                    TempAnimalsSpreadCacheCoords.Add(hiddenAnimal.Coords);
                    var isGrowing = true;
                    while (isGrowing)
                    {
                        isGrowing = false;
                        foreach (var otherHiddenAnimal in _hiddenAnimals)
                        {
                            if (otherHiddenAnimal.Coords == hiddenAnimal.Coords) continue;

                            for (var i = 0; i < TempAnimalsSpreadCacheCoords.Count; i++)
                            {
                                if (TempAnimalsSpreadCacheCoords.Contains(otherHiddenAnimal.Coords)) continue;

                                if (!IsNeighbourCell(grid, TempAnimalsSpreadCacheCoords[i], otherHiddenAnimal.Coords))
                                    continue;
                                TempAnimalsSpreadCacheCoords.Add(otherHiddenAnimal.Coords);
                                isGrowing = true;
                                break;
                            }
                        }
                    }

                    for (var i = 1; i < TempAnimalsSpreadCacheCoords.Count; i++)
                    {
                        foundCellToRunTo = TryToPutAnimalInRandomCellAround(grid, TempAnimalsSpreadCacheCoords[i]);
                        if (foundCellToRunTo) break;
                    }
                }

                if (foundCellToRunTo) continue;

                _reactionHandler.RegisterTileShouldReactOnDie(grid, _handler, null, animalCoords,
                    hiddenAnimal.TileId, TileAsset.Frame, TileSpeciality.Frame, TileState.AnimalMod,
                    TileKinds.None, hiddenAnimal.Tile);

                collectedAnimals++;
                if (_goalSystem.GetLeftGoalCount(GoalType.Animal) == collectedAnimals)
                    RemoveAllFrames(grid, reactionHandler, inputParams, goalSystem, queue, cellsToDamage,
                        hiddenAnimal.HitWaitParams);
            }

            _hiddenAnimals.Clear();
        }

        private static bool IsNeighbourCell(Grid grid, Coords a, Coords b)
        {
            if (grid.TryGetCell(a, out var cellA) && grid.TryGetCell(b, out _))
                return !cellA.HasWallTo(b) && a.IsUnitDistanceFrom(b);

            return false;
        }

        private bool TryToPutAnimalInRandomCellAround(Grid grid, Coords from)
        {
            var dx = RandomSystem.Next(3) - 1; // -1, 0, 1
            var dy = dx == 0
                ? RandomSystem.Next(2) * 2 - 1 // -1, 1
                : 0;

            for (var i = 0; i < 4; i++)
            {
                if (TryToPutAnimalInCell(grid, new Coords(from.X + dx, from.Y + dy), from)) return true;

                // rotate vector 90 degrees.
                var tmp = dx;
                dx = -dy;
                dy = tmp;
            }

            return TryToPutAnimalInCell(grid, from, from);
        }

        private bool TryToPutAnimalInCell(Grid grid, Coords toCoords, Coords fromCoords)
        {
            if (!grid.TryGetCell(toCoords, out var candidateCell)) return false;
            if (toCoords != fromCoords && candidateCell.HasWallTo(fromCoords)) return false;
            var candidateTile = candidateCell.Tile;
            if (candidateTile is not { Speciality: TileSpeciality.Frame }) //if no tile then skip
                return false;

            if (candidateTile.IsAnyOf(TileState.AnimalMod)) //if already busy then skip
                return false;

            candidateTile.Add(TileState.AnimalMod);
            _handler.AddAction(new ActionAddModifier(candidateCell.Coords, fromCoords,
                candidateTile,
                TileState.AnimalMod));
            return true;
        }

        private bool ApplyDamageToCells(Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, Queue queue, Queue cellsToDamageQueue,
            SpecialTileCalculationResult specialTileCalculationResult)
        {
            var actions = cellsToDamageQueue.GetCurrentPreHitActions();
            if (actions != null)
                foreach (var action in actions)
                    action.Execute(inputParams, _handler, goalSystem,
                        grid, cellsToDamageQueue, _settleTileSystem);

            var isTilePopped = false;
            var hits = cellsToDamageQueue.GetCurrentHits();
           
            while (hits is { Count: > 0 })
            {
                hits.Sort((hit, hit1) =>
                {
                    foreach (var damageSource in _damageSourcesToProcessAtLast)
                    {
                        var hitIsSource = (hit.GetHitParams().DamageSource & damageSource) != 0;
                        var hit1IsSource = (hit1.GetHitParams().DamageSource & damageSource) != 0;

                        if (hitIsSource != hit1IsSource)
                            return hitIsSource ? 1 : -1;

                        if (hitIsSource)
                            return hit.HitId.CompareTo(hit1.HitId);
                    }

                    var hitIsLineBreaker = (hit.GetHitParams().DamageSource & DamageSource.LineBreakerArrow) != 0;
                    var hit1IsLineBreaker = (hit1.GetHitParams().DamageSource & DamageSource.LineBreakerArrow) != 0;
                    var hitDistanceComparison = hit.GetHitParams().HitDistance.CompareTo(hit1.GetHitParams().HitDistance);
                    if (hitIsLineBreaker && hit1IsLineBreaker && hitDistanceComparison != 0)
                        return hitDistanceComparison;

                    return hit.HitId.CompareTo(hit1.HitId);
                });

                var hitCounters = new Dictionary<TileSpeciality, int>();
                foreach (var hit in hits)
                {
                    var cell = (Cell)hit.GetTarget();

                    var isTargetingHit = (hit.GetHitParams().DamageSource & DamageSource.TargetingMechanics) != 0;

                    var isAfterFirstHitSuperDiscoBall = (hit.GetHitParams().DamageSource & DamageSource.Whirlpool) != 0 &&
                                                        hit.GetHitParams().WhirlpoolInfo.WaveIndex > 0;
                    
                    var isGondolaHit = (hit.GetHitParams().DamageSource & DamageSource.Gondola) != 0;

                    if (isTargetingHit || isAfterFirstHitSuperDiscoBall)
                    {
                        if (_targetingHits.Contains(hit))
                        {
                            // This means we are processing the hit so we need to remove it from the list
                            // and allow the hit to be processed
                            _targetingHits.Remove(hit);
                        }
                        else
                        {
                            if (!goalSystem.HasCompletedGoals())
                            {
                                // Otherwise, the hit should be saved for later processing
                                _targetingHits.Add(hit);
                                continue;
                            }
                        }
                    }

                    if (isTargetingHit || isGondolaHit)
                    {
                        var sourceCoords = hit.Coords;
                        var damageSource = hit.DamageSource;
                        
                        if (damageSource == DamageSource.Gondola)
                        {
                            damageSource = hit.PreviousDamageSource != DamageSource.None ? hit.PreviousDamageSource : DamageSource.Propeller;
                        }
                        
                        cell = _mechanicTargetingManager.SelectTarget(damageSource, sourceCoords, includeSourceCoords: true);

                        if (cell.HasTile())
                        {
                            var tile = cell.Tile;
                            var maxHits = tile.GetMaxHits(cell, grid, goalSystem, reactionHandler, hit.DamageSource);

                            if (maxHits >= 0)
                            {
                                hitCounters.TryGetValue(tile.Speciality, out var currentHits);

                                if (currentHits >= maxHits)
                                {
                                    // Select a new cell while ignoring this tile type
                                    cell = _mechanicTargetingManager.SelectTarget(damageSource, sourceCoords,
                                        tile.Speciality);
                                }

                                hitCounters[tile.Speciality] = currentHits + 1;
                            }
                        }

                        if ((hit.DamageSource & DamageSource.PropellerCombo) != 0)
                        {
                            // Since damage source is PropellerCombo, then we assume that hit.BoostInfo is PropellerInfo
                            var propellerInfo = (PropellerInfo) hit.BoostInfo;
                            var tileSpeciality = propellerInfo.TileSpeciality;
                            var tileAsset = propellerInfo.TileAsset;
                            var uniqueId = propellerInfo.PropellerId;

                            cell = _mechanicTargetingManager.SelectComboTarget(DamageSource.PropellerCombo,
                                tileSpeciality);

                            TileFactory.CreateTile(grid.TilesSpawnedCount++, tileAsset,
                                new TileOrigin(Creator.Item, cell));

                            _handler.DeferredTargetSelected(uniqueId, cell);
                        }

                        if ((hit.DamageSource & DamageSource.Propeller) != 0)
                        {
                            // Since damage source is Propeller, then we assume that hit.BoostInfo is PropellerInfo
                            var uniqueId = ((PropellerInfo) hit.BoostInfo).PropellerId;
                            _handler.DeferredTargetSelected(uniqueId, cell);
                        }

                        if ((hit.DamageSource & DamageSource.Skunk) != 0)
                        {
                            // Since damage source is Skunk, then we assume that hit.BoostInfo is SkunkHitInfo
                            var uniqueId = ((SkunkHitInfo) hit.BoostInfo).SkunkHitId;

                            //For skunk, we exclude skunk coords from the target selection since skunk is not removed
                            if (cell.Coords == sourceCoords)
                            {
                                cell = _mechanicTargetingManager.SelectTarget(damageSource, sourceCoords);
                            }
                            _handler.DeferredTargetSelected(uniqueId, cell);
                        }
                        
                        if ((hit.DamageSource & DamageSource.FireWorks) != 0)
                        {
                            // Since damage source is FireWorks, then we assume that hit.BoostInfo is FireWorksInfo
                            var uniqueId = ((FireWorksInfo) hit.BoostInfo).FireWorksId;
                            _handler.DeferredTargetSelected(uniqueId, cell);
                        }
                    }

                    var mainCell = cell?.GetMainCellReference(out _);

                    if (!ShouldHitBeFiltered(cell, hit, _hitFilter)) continue;

                    isTilePopped |= TryApplySingleCellDamage(cell, hit, grid, reactionHandler,
                        inputParams, goalSystem,
                        queue, cellsToDamageQueue, specialTileCalculationResult);

                    ReportHitToCells(grid, mainCell, cell);
                }

                hits = cellsToDamageQueue.GetCurrentHits();
            }

            return isTilePopped;
        }

        /// <summary>
        ///     Checks whether the hit should be filtered or not. This is mostly applicable for mechanics that occupy more
        ///     than 1 cell (like Toad, Squid, Bowling Pin, etc), for which we have basically 2 rules:
        ///     1. We apply 1 damage per cell per damage source (like Bowling Pin when it's opened or Toad) -- for example,
        ///     a bomb will remove 4 bowling pins
        ///     2. We apply 1 damage per damage source (like Bowling Pin when it's closed, Squid or Bush) -- for example,
        ///     a bomb will only take the Bowling curtain out, but will not remove any bowling pins
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="hit"></param>
        /// <param name="hitFilter"></param>
        /// <returns></returns>
        private bool ShouldHitBeFiltered(Cell cell, Hit hit, HashSet<HitIdentifier> hitFilter)
        {
            if (hit.HitSourceUid == -1) return true;

            Coords? targetCoords = cell.Coords;

            var mainCell = cell.GetMainCellReference(out _);
            var tileId = mainCell.Tile?.Id ?? -1;


            if (!mainCell.HasTile() && hit.DamageSource == DamageSource.TukTuk)
            {
                return true;
            }

            if (!mainCell.HasTile() && !mainCell.IsAnyOf(CellState.Ivy))
                if (mainCell.HitSourceUid == hit.HitSourceUid)
                    return false;

            if (!mainCell.HasTile() && cell.HasMultiSizeCellReferenceWithMultiSizeTile())
            {
                // In the case of multi-size tiles, mainCell.Tile could have been killed by a previous hit from a
                // particular sourceUid. So, in that case, we should also filter the hit to not affect the cell
                // state. For example, when a bomb is triggered next to Soda with grass under it. In that case, Soda 
                // will be killed, but the bomb should not remove the grass under it
                foreach (var hitId in hitFilter)
                    if (hitId.HitSourceUid == hit.HitSourceUid &&
                        hitId.DamageSource == hit.DamageSource &&
                        (!hitId.TargetCoords.HasValue || hitId.TargetCoords.Value.Equals(mainCell.Coords)))
                        return false;
            }
            else
            {
                if (mainCell.HasTile())
                {
                    var tileSpeciality = mainCell.Tile.Speciality;
                    switch (tileSpeciality)
                    {
                        case TileSpeciality.Gondola:
                            targetCoords = null;
                            break;
                        case TileSpeciality.Toad:
                            // Handles 1 damage per affected cell except when it's covered with Ivy
                            if (cell.IsAnyOf(CellState.Ivy))
                                // Setting targetCoords to null will make all further hits from same hitSourceId to be filtered,
                                // no matter the cell coordinate that is being affected
                                targetCoords = null;

                            break;
                        case TileSpeciality.Bowling:
                            // Handles 1 damage if Bowling Pin is closed. However, it handles 1 damage per affected cell when
                            // Bowling Pin is opened (meaning, it has no curtain)
                            if (mainCell.Tile.GetParam(TileParamEnum.BowlingOpened) == 0)
                                // Setting targetCoords to null will make all further hits from same hitSourceId to be filtered,
                                // no matter the cell coordinate that is being affected
                                targetCoords = null;
                            break;
                        default:
                            // By default, we register the hit on the main cell, which in practice will filter other hits
                            // from the same source and source unique id for the same tile
                            targetCoords = mainCell.Coords;
                            break;
                    }
                }

                var hitIdentifier = new HitIdentifier
                {
                    DamageSource = hit.DamageSource,
                    HitSourceUid = hit.HitSourceUid,
                    TileId = tileId,
                    TargetCoords = targetCoords
                };

                var result = !hitFilter.Contains(hitIdentifier);
                hitFilter.Add(hitIdentifier);

                return result;
            }

            return true;
        }

        /// <summary>
        ///     Report hit to cell(s). This is currently used for Propeller, in order to have the latest state
        ///     on cell's damage (that's why it's done AFTER making the correspondent damage
        ///     For multi-cell tiles, we need to update all the related coords
        /// </summary>
        private static void ReportHitToCells(Grid grid, Cell mainCell, Cell cell)
        {
            var cells = mainCell?.ReferencedCells;
            if (cells?.Count > 0)
            {
                foreach (var auxCell in cells)
                    if (auxCell != null)
                        grid.ReportHitToCell(auxCell);
            }
            else
            {
                if (cell != null)
                    grid.ReportHitToCell(cell);
            }
        }

        /// <summary>
        ///     Apply one hit damage to cell and to tile inside cell.
        /// </summary>
        private bool TryApplySingleCellDamage(Cell cell, Hit hit, Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Queue queue,
            Queue cellsToDamageQueue, SpecialTileCalculationResult specialTileCalculationResult)
        {
#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterAppliedHit("ApplySingleCellDamage", hit, queue.UniqueUid);
#endif

            var hitParams = hit.GetHitParams();
            switch (hitParams.DamageSource)
            {
                case DamageSource.EndGame:
                    _handler.AddAction(new ActionEndTurnCheckpoint());
                    break;
                case DamageSource.Propeller:
                {
                    var propellerInfo = (PropellerInfo)hit.BoostInfo;
                    _handler.AddAction(new ActionPropellerExplosion(propellerInfo.PropellerId, cell, hit.Coords));
                    break;
                }
                case DamageSource.FireWorks:
                {
                    var fireWorksInfo = (FireWorksInfo)hit.BoostInfo;
                    _handler.AddAction(new ActionFireWorksExplosion(fireWorksInfo.FireWorksId, cell, hit.Coords));
                    break;
                }
                case DamageSource.Skunk:
                {
                    var skunkInfo = (SkunkHitInfo)hit.BoostInfo;
                    _handler.AddAction(new ActionSkunkExplosion(skunkInfo.SkunkHitId, cell, hit.Coords));
                    break;
                }
            }


            // Multi-size cell overlay blocks any damage. Multi-size cell overlay needs to be destroyed first (and it's damage is indirect).
            if (cell.HasMultiSizeCellReferenceWithCellOverlay())
                return false;

            var directDamageApplied = TryApplyDirectDamageToCell(inputParams, grid, cell, hitParams);

            // directDamageApplied will be true if cell is empty with Grass or if it's not empty with Ivy,
            // so the Tile should not receive damage and process finishes. -VK
            var mainCell = cell.GetMainCellReference(out var coordsOffset);
            var tile = mainCell.Tile;

            if (tile is not null && hit.CanApplyDamageToTile())
                if (!directDamageApplied)
                {
                    // At this point cell already had a chance to receive direct damage, so if this line of code has
                    // been reached then the Tile should receive damage now. At the same time we handle multi-size tiles
                    // by evaluating reference to main cell (main cell shouldn't be evaluated before the DirectCell
                    // damage step completion). -VK
                    var canBeDamaged = tile.CanBeDamagedBy(hitParams.DamageSource, hit.SourceKind);

                    if (!canBeDamaged)
                        return false;

                    if (tile.IsDead)
                        return false;

                    var tileIsImmuneToHit = hit.ImmuneTiles != null && hit.ImmuneTiles.Contains(tile);
                    if (tileIsImmuneToHit)
                        return false;

                    ApplySingleDamageToTile(tile, mainCell, cell, coordsOffset, hit, hitParams,
                        grid, reactionHandler, inputParams, goalSystem, queue, cellsToDamageQueue,
                        specialTileCalculationResult);
                }

            // Additional effects should still run, even if cell is empty with Grass or if it's not empty with Ivy
            if (hit.AdditionalEffect == AdditionalEffect.None) return true;

            var additionalQueue = HandleAdditionalHitEffects(grid, inputParams, goalSystem, cell, hit);

            if (additionalQueue is not { HasTilesAndCellsToDamage: true }) return true;
            ApplyDamageToCells(grid, reactionHandler, inputParams, goalSystem, queue, additionalQueue,
                specialTileCalculationResult);
            cellsToDamageQueue.Append(additionalQueue);

            return true;
        }

        /// <summary>
        ///     Direct damage to cell when tile is null or when cell overlay receives damage first (for example, when Ivy is
        ///     present).
        /// </summary>
        /// <remarks> Is damage logic should skip Tile hit stage.</remarks>
        private bool TryApplyDirectDamageToCell(SimulationInputParams inputParams, Grid grid, Cell cell, HitWaitParams hitWaitParams)
        {
            var damageSource = hitWaitParams.DamageSource;
            cell = cell.GetMainCellReference(out _, true);
            var tile = cell.Tile;
            var coords = cell.Coords;

            if ((damageSource & DamageSource.AdjacentGeneral) != 0)
                if (cell.Tile is null && !cell.CanReceiveAdjacentDamageWhenCellIsEmpty)
                    return false;

            if (cell.CanCellReceiveDirectDamage(damageSource, grid))
            {
                if (TryApplyTntHit(cell))
                    return true;

                if (TryApplyCellStateHit(inputParams, grid, cell, _reactionHandler, _handler, _goalSystem,
                        true, false, hitWaitParams))
                    return true;


                if ((tile is not null || (damageSource & DamageSource.Adjacent) != 0) &&
                    tile?.Speciality != TileSpeciality.ColorBomb) return false;

                if (TryApplyCellStateHit(inputParams, grid, cell, _reactionHandler, _handler, _goalSystem,
                        false, true, hitWaitParams))
                    return true;
            }
            else if (cell.CanCellReceiveDropItemDamage(damageSource))
            {
                if (!damageSource.IsAnyOf(DamageSource.PowerUp | DamageSource.UsableBoost |
                                          DamageSource.Whirlpool | DamageSource.Dynamite |
                                          DamageSource.Skunk | DamageSource.FireWorks))
                {
                    return false;
                }

                var hasCellState = cell.IsAnyOf(CellState.BackOne | CellState.BackDouble | CellState.Petal |
                                                CellState.DestructibleWall);

                if (!hasCellState) return false;

                if (TryApplyCellStateHit(inputParams, grid, cell, _reactionHandler, _handler, _goalSystem, false, true, hitWaitParams))
                    return true;
            }

            return false;
        }

        private static bool TryApplyTntHit(Cell cell)
        {
            // TNT works as a shield for cell, damage should not hit any tile in it.
            // TNT itself receives damage in different way (search for 'tntCount--' to know more).
            return cell.IsAnyOf(CellState.Tnt);
        }

        /// <summary>
        ///     Reduce cell overlay bg hit points.
        /// </summary>
        /// <remarks>
        ///     Bg state of cell may be Ivy or Background (grass),
        ///     they are mutually exclusive
        ///     because both states are using one field for hp value - BackgroundCount. -VK
        /// </remarks>
        public static bool TryApplyCellStateHit(SimulationInputParams inputParams, Grid grid, Cell cell, TileHitReactionHandler reactionHandler, IRootSimulationHandler handler,
            GoalsSystem goalsSystem,
            bool allowIvyHit, bool allowBackgroundHit, HitWaitParams hitWaitParams)
        {
            if (inputParams.InitialLoop)
                return false;

            if (cell.HasMultiSizeCellReferenceWithCellOverlay()) return false;
            var isIvyHit = allowIvyHit && cell.IsAnyOf(CellState.Ivy);
            var isBgHit = allowBackgroundHit && (cell.IsBackState() || cell.IsPetal());
            var isWallHit = allowBackgroundHit && cell.IsAnyOf(CellState.DestructibleWall);

            if (isBgHit)
            {
                if (cell.BackgroundCount <= 0) return false;
                var mainCell = cell.GetMainCellReference(out _);
                if (cell.HasMultiSizeCellReferenceWithMultiSizeTile() && mainCell.HasTile() && !mainCell.Tile.IsDead)
                    return false;

                if (cell.HasTile() && (cell.Tile.GetParam(TileParamEnum.IceLayerCount) > 0 ||
                                       cell.Tile.GetParam(TileParamEnum.ChainLayerCount) > 0))
                    return false;

                if (cell.IsAnyOf(CellState.IceBar | CellState.MetalBar) &&
                    hitWaitParams.DamageSource is DamageSource.Whirlpool or DamageSource.Dynamite)
                {
                    return false;
                }
            }

            if (isIvyHit)
                if (cell.IvyCount <= 0)
                    return false;

            if (isWallHit)
                if (!cell.HasDestructibleWall())
                    return false;

            if (!isIvyHit && !isBgHit && !isWallHit) return false;
            // If damage is coming from Tap or Swap, do not hit the cell. Cell hit is handled by other hits already
            if ((hitWaitParams.DamageSource & DamageSource.TapOrSwap) != 0) return false;

            TileCollectorsHandler.NotifyCollectorsOnCellOverlayDie(grid, handler, cell.Coords, cell.State, hitWaitParams);
            var affectedGoal = goalsSystem.ReduceGoalOnCellBackgroundDamageIfNeeded(cell);
            handler.AddAction(new ActionRemoveBackgroundHp(cell, affectedGoal, isIvyHit, hitWaitParams));

            var hpLeft = 0;

            if (isBgHit)
            {
                cell.BackgroundCount--;
                hpLeft = cell.BackgroundCount;
                cell.UpdateCellBackgroundState();
            }

            if (isWallHit)
            {
                foreach (var destructibleWall in cell.DestructibleWalls.DestructibleWall)
                {
                    if ((hitWaitParams.DamageSource & DamageSource.TukTuk) != 0)
                    {
                        destructibleWall.Count = 0;
                    }
                    else
                    {
                        destructibleWall.Count = destructibleWall.Count > 0 ? --destructibleWall.Count : 0;
                    }
                }

                var count = cell.GetDestructibleWallCount();
                if (count == 0)
                {
                    cell.Remove(CellState.DestructibleWall);
                    goalsSystem.TryReduceGoalIfNeeded(GoalType.DestructibleWall);
                }
                handler.AddAction(new ActionRemoveDestructibleWall(cell.Coords, count, hitWaitParams));
            }

            if (!isIvyHit) return true;
            {
                if (cell.HasMultiSizeCellReference())
                {
                    var mainCell = cell.GetMainCellReference(out _);
                    foreach (var referencedCell in mainCell.ReferencedCells)
                    {
                        if (!referencedCell.Equals(cell))
                        {
                            TileCollectorsHandler.NotifyCollectorsOnCellOverlayDie(grid, handler,
                                referencedCell.Coords, referencedCell.State, hitWaitParams);
                            goalsSystem.ReduceGoalOnCellBackgroundDamageIfNeeded(referencedCell);
                        }

                        referencedCell.IvyCount = 0;
                        referencedCell.UpdateCellBackgroundState();
                    }
                }
                else
                {
                    cell.IvyCount = 0;
                    hpLeft = 0;
                    cell.UpdateCellBackgroundState();
                }
            }

            return true;
        }

        private void ApplySingleDamageToTile(Tile tile, Cell mainCell, Cell cell, Coords coordsOffset, Hit hit,
            HitWaitParams hitWaitParams, Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Queue queue,
            Queue cellsToDamageQueue, SpecialTileCalculationResult specialTileCalculationResult)
        {
            var coords = mainCell.Coords;
            int? skin = null;
            
            if (TryApplyAdjacentTileDamage(grid, inputParams, tile, mainCell, cell, coords, ref coordsOffset, hit,
                hitWaitParams,
                reactionHandler, goalSystem, ref skin, cellsToDamageQueue, queue))
                return;
            
            var isACollectableTile = false;
            var stateRemoved = tile.ReduceHitPoints();
            if (tile.OnBeforeDieReaction())
                if (tile.IsNoneOf(TileState.SandMod))
                {
                    var destroyCoord = specialTileCalculationResult?.GetSourceCoordIfAny(mainCell.Coords) ??
                                       mainCell.Coords;
                    isACollectableTile = reactionHandler.RegisterTileShouldReactOnDie(grid, _handler, hitWaitParams, destroyCoord, tile.Id,
                        tile.Asset, tile.Speciality, tile.State, tile.Kind, tile, skin, coordsOffset);
                }

            if (tile.IsDead)
            {
                if (stateRemoved != TileState.None)
                {
                    var affectedGoal = _goalSystem.ReduceGoalOnModRemoveIfNeeded(stateRemoved);
                    _handler.AddAction(new ActionRemoveState(tile.Id, coords,
                        stateRemoved, hitWaitParams, affectedGoal));
                }

                if (tile.Kind == TileKinds.Undefined) DefineTileKindOnDie(tile, inputParams.UsedKinds);

                switch (tile.Asset)
                {
                    case TileAsset.Sheep or TileAsset.Monkey:
                        // Special case for some tiles like sheep or monkey, which should play long Destroy animation in cell.
                        // TODO: create more generic system for additional busy time on tile destroy -VK
                        mainCell.IsBusy += 8;
                        queue.AddBusyCell(mainCell);
                        break;
                    case TileAsset.Toad or TileAsset.MagicHat or TileAsset.Gondola or TileAsset.FireWorks or TileAsset.SlotMachine:
                        MakeBusyCellGrid(queue, mainCell, 15);
                        break;
                    case TileAsset.Squid or TileAsset.Bowling or TileAsset.Bush or TileAsset.Safe or
                        TileAsset.IceBar or TileAsset.GiantPinata or TileAsset.Shelf or TileAsset.MetalBar or
                        TileAsset.DynamiteBox or TileAsset.JellyFish or TileAsset.GoldenScarab or TileAsset.Pouch 
                        or TileAsset.BigMonkey:
                        MakeBusyCellGrid(queue, mainCell, 5);
                        break;
                }

                var forceSkipDestroyAnim = goalSystem.GetLeftGoalCount(tile.Kind.ToGoalType()) > 0 ||
                                           goalSystem.AreBeingCollectedByGameEvent(tile.Kind) ||
                                           (specialTileCalculationResult?.HashFormationCoord(coords) ?? false)
                                           || (hit.DamageSource & DamageSource.ForceSkipDestroyAnim) != 0
                                           || tile.IsBoost;

                if (goalSystem.AreBeingCollectedByGameEvent(tile.Kind) &&
                    goalSystem.GetLeftGoalCount(tile.Kind.ToGoalType()) <= 0)
                    forceSkipDestroyAnim = false;

                if (isACollectableTile) forceSkipDestroyAnim = true;

                var isLbHitByLb = mainCell.HasTile() && mainCell.Tile.Speciality is TileSpeciality.ColumnBreaker or TileSpeciality.RowBreaker or TileSpeciality.Bomb or TileSpeciality.Propeller;

                var hitCellResult = HandleTileDeath(grid, inputParams, goalSystem,
                    queue, mainCell, tile, hit.ImmuneTiles,
                    hit.BusyWait, hitWaitParams, forceSkipDestroyAnim);

                if (!mainCell.HasTile() && hitWaitParams != null &&
                    tile.IsAffectingCellState(hitWaitParams.DamageSource))
                    TryApplyCellStateHit(inputParams, grid, mainCell, _reactionHandler,
                        _handler, _goalSystem, false, true, hitWaitParams);

                if (hitCellResult is not { HasTilesAndCellsToDamage: true }) return;
                if (!isLbHitByLb)
                    ApplyDamageToCells(grid, reactionHandler, inputParams, goalSystem, queue, hitCellResult,
                        specialTileCalculationResult);
                cellsToDamageQueue.Append(hitCellResult);
            }
            else
            {
                _handler.AddAction(new ActionRemoveHp(coords, tile, hitWaitParams));
                if (stateRemoved == TileState.None) return;
                // State will be removed in LogicalActionRemoveHp before this action,
                // but LogicalActionRemoveState is still required (even if it will not do anything),
                // because it is used as notification for state removal,
                // which is used, for example, to check if sand mod has been removed during this turn. -VK
                //
                // Probably it will be better to separate state removal from LogicalActionRemoveHP (for atomic actions).
                var affectedGoal = _goalSystem.ReduceGoalOnModRemoveIfNeeded(stateRemoved);
                _handler.AddAction(new ActionRemoveState(tile.Id, coords, stateRemoved, hitWaitParams,
                    affectedGoal));
            }
        }
        
        private static void MakeBusyCellGrid(Queue q, Cell mainCell, int busyTime)
        {
            var cells = mainCell?.ReferencedCells;
            if (!(cells?.Count > 0)) return;
            foreach (var auxCell in cells)
                if (auxCell != null)
                {
                    auxCell.IsBusy += busyTime;
                    q.AddBusyCell(auxCell);
                }
        }

        private bool TryApplyAdjacentTileDamage(Grid grid, SimulationInputParams inputParams, Tile tile, Cell mainCell,
            Cell cell,
            Coords coords, ref Coords coordsOffset, Hit hit, HitWaitParams hitWaitParams,
            TileHitReactionHandler reactionHandler, GoalsSystem goalsSystem, ref int? skin, Queue cellsToDamageQueue, Queue queue)
        {
            if (inputParams.InitialLoop)
                return false;

            if (tile.GetParam(TileParamEnum.IceLayerCount) > 0 || tile.GetParam(TileParamEnum.ChainLayerCount) > 0)
                return false;

            if (tile.IsAnyOf(TileState.SandMod))
                return false;
            
            var simulationContext = new SimulationContext(grid, inputParams, _handler, this,
                SpawnSystem, _settleTileSystem, queue, reactionHandler, _goalSystem, cellsToDamageQueue);
            
            var hitContext = new HitContext(mainCell, cell, coords, coordsOffset, hit, hitWaitParams, skin);
            
            var result = tile.TryApplyAdjacentDamage(simulationContext, hitContext);
            coordsOffset = hitContext.CoordsOffset;
            skin = hitContext.Skin;
    
            return result;
        }

        private Queue HandleAdditionalHitEffects(Grid grid, SimulationInputParams inputParams, GoalsSystem goalSystem,
            Cell cell, Hit hit)
        {
            Queue result = null;

            var additionalHitEffects = hit.AdditionalEffect;
            var immuneTiles = hit.ImmuneTiles;
            var hitParams = hit.GetHitParams();

            switch (additionalHitEffects)
            {
                case AdditionalEffect.RowClearer:
                    result = SpecialTileSystem.PopRowBreaker(immuneTiles, _handler, grid,
                        inputParams, goalSystem, cell, hitParams);
                    break;
                case AdditionalEffect.ColumnClearer:
                    result = SpecialTileSystem.PopColumnBreaker(immuneTiles, _handler, grid, goalSystem,
                        cell, hitParams);
                    break;
                case AdditionalEffect.SquareClearer:
                    result = SpecialTileSystem.PopSquareClearer(
                        _handler,
                        grid,
                        inputParams,
                        goalSystem,
                        cell,
                        null,
                        M3Constants.DefaultBombRadius,
                        hitParams);
                    break;
            }

            return result;
        }

        private Queue HandleTileDeath(Grid grid, SimulationInputParams inputParams, GoalsSystem goalSystem,
            Queue q, Cell cell, Tile tile, HashSet<Tile> immuneTiles,
            int busyTime,
            HitWaitParams hitWaitParams, bool forceSkipDestroyAnim)
        {
            if (hitWaitParams.DamageSource == DamageSource.EndGame)
            {
                _handler.AddAction(new ActionEndTurnCheckpoint());
            }
            else if (!goalSystem.HasCompletedGoals())
            {
                _levelAnalyticsReporter?.RegisterItemDestroyedOn(tile);
            }

            if (!forceSkipDestroyAnim)
            {
                if (hitWaitParams.DamageSource == DamageSource.AutoMatch)
                    _handler.AddAction(new ActionNotifyNearCellAboutMatch(cell.Coords, hitWaitParams));

                _handler.AddAction(new ActionVisualizeTileDestroy(cell.Coords, tile, hitWaitParams));
            }

            if (tile.IsAnyOf(TileState.InTransition))
                _handler.FinalHandleMovementAction(tile, cell.Coords);

            Queue result = null;
            var tileSpeciality = tile.Speciality;

            // For line breakers, we will be handling the remove action AFTER we process the Rocket, because Rocket needs
            // to lock its targets and the action should not be blocked by the Remove action
            switch (tileSpeciality)
            {
                case TileSpeciality.RowBreaker:
                    result = SpecialTileSystem.PopRowBreaker(immuneTiles, _handler, grid,
                        inputParams, goalSystem, cell, hitWaitParams);
                    result?.ClearAndBusyCell(inputParams, grid, goalSystem, cell,
                        _handler, null, M3Constants.BusyTimeLineBreaker);

                    break;
                case TileSpeciality.ColumnBreaker:
                    result = SpecialTileSystem.PopColumnBreaker(immuneTiles, _handler, grid, goalSystem,
                        cell, hitWaitParams);

                    result?.ClearAndBusyCell(inputParams, grid, goalSystem, cell,
                        _handler, hitWaitParams, M3Constants.BusyTimeLineBreaker);

                    break;
                case TileSpeciality.Bomb:
                    result = SpecialTileSystem.PopSquareClearer(
                        _handler,
                        grid,
                        inputParams,
                        goalSystem,
                        cell,
                        null,
                        M3Constants.DefaultBombRadius,
                        hitWaitParams);
                    result?.ClearAndBusyCell(inputParams, grid, goalSystem, cell, _handler,
                        hitWaitParams, M3Constants.BusyTimeBomb);
                    break;
                case TileSpeciality.ColorBomb:
                    var topTileKinds = grid.GetTopTileKinds();
                    result = SpecialTileSystem.RemoveAllColorTiles(null, inputParams, _handler,
                        grid, _goalSystem, _settleTileSystem, cell, topTileKinds);
                    result?.ClearAndBusyCell(inputParams, grid, goalSystem, cell, _handler,
                        hitWaitParams, M3Constants.BusyTimeRemoveAllColorTiles);
                    break;
                case TileSpeciality.DynamiteBox:
                    result = SpecialTileSystem.DynamiteBlastAllTiles(_handler, grid, cell);
                    break;
            }

            _handler.AddAction(new ActionRemoveTile(cell.Coords, tile, hitWaitParams));

            switch (tileSpeciality)
            {
                case TileSpeciality.DropItem:
#if BBB_LOG
                    M3Debug.LogError("[PopSystem]:\n Trying to remove TileSpeciality." +
                                     tileSpeciality.ToString<TileSpeciality>() + " in HitCell");
#endif
                    break;

                case TileSpeciality.ColorBomb:
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Bomb:
                    break;

                case TileSpeciality.Propeller:

                    result = new Queue("SmallCross");

                    var noRealDamage = tile.Origin.CreatorType == Creator.ColorBombCombo ||
                                       hitWaitParams.DamageSource == DamageSource.RemoveColorTiles;
                    var smallCross = SpecialTileSystem.PopSmallCross(
                        immuneTiles,
                        inputParams,
                        _handler,
                        grid,
                        goalSystem,
                        cell,
                        noRealDamage);

                    result.Append(smallCross);

                    var removeRandomTileQueue = SpecialTileSystem.RemoveWithPropellerAttack(immuneTiles,
                        _handler, _settleTileSystem, SpawnSystem, tile.Id, cell.Coords, hitWaitParams);


                    //Propeller Superboost should be done after cross damage and propeller flying
                    _handler.AddAction(new ActionTryIncrementSuperBoost(cell, null, hitWaitParams));

                    result.ClearAndBusyCell(inputParams, grid, goalSystem, cell,
                        _handler, hitWaitParams,
                        M3Constants.BusyTimeSmallCross);

                    if (removeRandomTileQueue != null) result.Append(removeRandomTileQueue);

                    break;
                default:
                {
                    cell.HardRemoveTile(busyTime, hitWaitParams);
                    if (tile.Kind == TileKinds.Undefined) DefineTileKindOnDie(tile, inputParams.UsedKinds);

                    q?.AddBusyCell(cell);

                    break;
                }
            }

            if (cell != null && cell.HasTile() && cell.Tile.Speciality != TileSpeciality.Propeller)
            {
                // If a booster is triggered by another booster, then make sure we are adding progress to SuperBoost
                _handler.AddAction(new ActionTryIncrementSuperBoost(cell, null, hitWaitParams));
            }

            return result;
        }

        /// <summary>
        ///     Damage handler for 'end' tiles and cells.
        ///     Executed at the last step of m3 simulation loop,
        ///     after all other damage types (such as matches, boosters and combos) already applied.
        /// </summary>
        /// <remarks>
        ///     If end hit is Tile hit, then most usually it is Blinking tile,
        ///     Otherwise, if it's Cell hit, then it is target of Skunk random target damage. -VK
        /// </remarks>
        public bool DamageEndTileAndCell(Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Queue q, Queue cellsToDamage)
        {
            var hit = cellsToDamage.GetEndHit(grid);
            if (hit == null) return false;

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterAppliedHit("Apply End Hit", hit, q.UniqueUid);
#endif

            Cell cell = null;
            var isTileHit = false;
            if (hit is Hit<Tile>)
            {
                foreach (var c in grid.Cells)
                    if (c.Tile == ((Hit<Tile>)hit).Target)
                    {
                        cell = c;
                        isTileHit = true;
                        break;
                    }
            }
            else
            {
                cell = ((Hit<Cell>)hit).Target;
            }

            if (cell == null) return false;

            if (isTileHit)
            {
                var tile = cell.Tile;
                var forceSkipDestroyAnim = goalSystem.GetLeftGoalCount(tile.Kind.ToGoalType()) > 0
                                           || goalSystem.AreBeingCollectedByGameEvent(tile.Kind)
                                           || (hit.DamageSource & DamageSource.ForceSkipDestroyAnim) != 0
                                           || tile.IsBoost;

                KillTile(grid, reactionHandler, inputParams, goalSystem, q, cellsToDamage, cell, hit.GetHitParams(),
                    forceSkipDestroyAnim);
            }
            else
            {
                TryApplySingleCellDamage(cell, hit, grid, reactionHandler, inputParams, goalSystem, q, cellsToDamage,
                    null);

                var mainCell = cell.GetMainCellReference(out _);
                ReportHitToCells(grid, mainCell, cell);
            }

            return true;
        }

        public void KillTile(Grid grid, TileHitReactionHandler reactionHandler, SimulationInputParams inputParams,
            GoalsSystem goalSystem, Queue q, Queue cellsToDamage, Cell cell, HitWaitParams hitWaitParams,
            bool forceSkipDestroyAnim)
        {
            var tile = cell.Tile;
            tile.MarkAsDead();
            var newDamageCells = HandleTileDeath(
                grid,
                inputParams,
                goalSystem,
                q,
                cell,
                tile,
                null,
                M3Constants.BusyTime,
                hitWaitParams,
                forceSkipDestroyAnim);

            if (newDamageCells is not { HasTilesAndCellsToDamage: true }) return;
            DamageCells(grid, reactionHandler, inputParams,
                goalSystem, q, newDamageCells, null);

            // Adding delayed cells to DamageCells:
            cellsToDamage.Append(newDamageCells);
        }

        private void DefineTileKindOnDie(Tile tile, List<TileKinds> usedKinds)
        {
            tile.SetKind(usedKinds.DeterministicRandomInSelf());
            _handler.OnTileKindDefined(tile.Id, tile.Kind);
        }

        private void RemoveAllFrames(Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, Queue queue, Queue cellsToDamage, HitWaitParams hitWaitParams)
        {
            foreach (var cell in grid.Cells)
            {
                if (cell.Tile == null || cell.Tile.Speciality != TileSpeciality.Frame) continue;

                _handler.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, 0) }, hitWaitParams));
                KillTile(grid, reactionHandler, inputParams, goalSystem, queue, cellsToDamage, cell, hitWaitParams,
                    false);
            }
        }

        public bool HasTargetingHits()
        {
            return _targetingHits.Count > 0;
        }

        public void ProcessTargetingHits(Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Queue queue)
        {
            foreach (var hit in _targetingHits)
            {
                queue.AppendHit(0, hit);
            }

            DamageCells(grid, reactionHandler, inputParams, goalSystem, queue, queue, null);
        }
    }
}