using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Logic
{
    public class AddTilePreHitAction : PreHitActionBase
    {
        private readonly Coords _targetCellCoords;
        private readonly Tile _newTile;

        public AddTilePreHitAction(Coords targetCellCoords, Tile newTile)
        {
            _targetCellCoords = targetCellCoords;
            _newTile = newTile;
        }

        public override void Execute(SimulationInputParams inputParams, IRootSimulationHandler events,
            GoalsSystem goalSystem, Grid grid, Queue queue, SettleTilesSystem settleTilesSystem)
        {
            if (grid.TryGetCell(_targetCellCoords, out var targetCell))
            {
                queue.ClearAndBusyCell(inputParams, grid, goalSystem, targetCell, events, null);
                targetCell.AddTile(_newTile);

                var coords = targetCell.Coords;
                events.AddAction(new ActionAddTile(coords, _newTile));  
            } 
        }
    }
}