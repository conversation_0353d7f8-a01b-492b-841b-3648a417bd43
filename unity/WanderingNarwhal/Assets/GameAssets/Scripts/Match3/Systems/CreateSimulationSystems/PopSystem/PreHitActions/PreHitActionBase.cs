using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Logic
{
    public abstract class PreHitActionBase
    {
        public abstract void Execute(SimulationInputParams simulationInputParams, IRootSimulationHandler handler,
            GoalsSystem goalsSystem, Grid grid,
            Queue cellsToDamageQueue, SettleTilesSystem settleTilesSystem);
    }
}