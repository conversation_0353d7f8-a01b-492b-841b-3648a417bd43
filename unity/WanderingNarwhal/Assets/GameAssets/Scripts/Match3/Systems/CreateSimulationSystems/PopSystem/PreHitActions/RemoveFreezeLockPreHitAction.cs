using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Logic
{
    public class RemoveFreezeLockPreHitAction : PreHitActionBase
    {
        private readonly int _lockId;


        public RemoveFreezeLockPreHitAction(int lockId)
        {
            _lockId = lockId;
        }
        
        public override void Execute(SimulationInputParams simulationInputParams, IRootSimulationHandler handler, 
            GoalsSystem goalsSystem,
            Grid grid, Queue cellsToDamageQueue, SettleTilesSystem settleTilesSystem)
        {
            settleTilesSystem.RemoveFreezeLock(_lockId);
        }
    }
}