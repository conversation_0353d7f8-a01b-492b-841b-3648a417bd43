using System.Text;

namespace BBB.Match3.Logic
{
    public sealed partial class Queue
    {
        public int DebugBusyCellsCount => _busyCells.Count;
        public int DebugTilesToBlowUpCount => _tilesToBlowUpInTheEnd.Count;

        public string ToConsole()
        {
            var builder = new StringBuilder();

            builder.Append("Queue:").AppendLine()
                .Append("HasSmtInside ").AppendLine(HasSomething.ToString())
                .Append("DelayWithHits ").AppendLine(DelayWithHitsToConsole())
                .Append("EndTilesToBlow ").AppendLine(EndTilesToBlowToConsole())
                .AppendLine(BusyCellsToConsole());

            return builder.ToString();
        }
        
        public override string ToString() => ToConsole();

        private string EndTilesToBlowToConsole()
        {
            if (!HasEndTilesToBlow)
            {
                return "No tiles to blow";
            }

            if (_tilesToBlowUpInTheEnd == null) return string.Empty;
            
            var builder = new StringBuilder();

            foreach (var tileToBlow in _tilesToBlowUpInTheEnd)
            {
                builder.Append(tileToBlow).Append(' ');
            }

            return builder.ToString();
        }


        private string DelayWithHitsToConsole()
        {
            if (!HasCellsToDamage)
            {
                return "No cells to damage";
            }

            var builder = new StringBuilder();
            foreach (var delayWithHits in _delaysWithHits)
            {
                builder.Append("Delay ").Append(delayWithHits.Item1).Append(" ")
                    .Append(delayWithHits.Item2).Append('\n');
            }

            return builder.ToString();
        }


        private string BusyCellsToConsole()
        {
            if (_busyCells == null || _busyCells.Count == 0)
            {
                return "No busy cells";
            }

            var builder = new StringBuilder();
            foreach (var busyCell in _busyCells)
            {
                builder.Append(busyCell)
                    .Append(" busyTime ")
                    .Append(busyCell.IsBusy)
                    .Append(", ");
            }

            if (builder.Length > 2)
            {
                builder.Length -= 2;
            }

            return builder.ToString();
        }

    }
}