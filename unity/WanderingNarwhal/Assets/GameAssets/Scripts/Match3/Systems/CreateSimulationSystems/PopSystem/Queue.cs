using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BebopBee.Core.Collections;

namespace BBB.Match3.Logic
{
    public sealed partial class Queue
    {
        private static int _highestUnusedUid;

        private static int GetUnusedUid
        {
            get
            {
                var result = _highestUnusedUid;
                _highestUnusedUid++;
                return result;
            }
        }

        /// <summary>
        /// Unique instance id for debug.
        /// </summary>
        public int UniqueUid { get; }


        private SortedNumberedMultiMap<Hit> _delaysWithHits;
        private HashSet<Hit<Tile>> _tilesToBlowUpInTheEnd;
        private Queue<Hit<Cell>> _cellsToHitInTheEnd;
        private HashSet<Cell> _busyCells;
        private SortedNumberedMultiMap<PreHitActionBase> _preHitActions;

        public bool HasTilesAndCellsToDamage => HasCellsToDamage;
        public bool HasEndTilesToBlow => _tilesToBlowUpInTheEnd.NotNullOrEmpty() || (!(_cellsToHitInTheEnd is null) && _cellsToHitInTheEnd.Count > 0);


        private bool HasCellsToDamage => _delaysWithHits is { NotEmpty: true };

        public bool HasSomething =>
            HasTilesAndCellsToDamage 
            || _busyCells.NotNullOrEmpty()
            || _preHitActions is { NotEmpty: true };

        public Queue(
            string context = null,
            SortedNumberedMultiMap<Hit> delaysWithHits = null,
            HashSet<Hit<Tile>> tilesToDamageOneInTurn = null,
            HashSet<Hit<Tile>> tilesToBlowUpInTheEnd = null,
            HashSet<Cell> busyCells = null,
            HashSet<Cell> frozenCells = null,
            SortedNumberedMultiMap<PreHitActionBase> preHitActions = null)
        {
            _delaysWithHits = delaysWithHits;
            _tilesToBlowUpInTheEnd = tilesToBlowUpInTheEnd;
            _cellsToHitInTheEnd = null;
            _busyCells = busyCells;
            _preHitActions = preHitActions;
            UniqueUid = GetUnusedUid;
#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterNewQueue(context.IsNullOrEmpty() ? "Class auto initialized member" : context , this.UniqueUid);
            if (delaysWithHits != null)
            {
                foreach (var kv in delaysWithHits)
                {
                    foreach (var hit in kv.Value.HitList)
                    {
                        PopQueueMonitor.RegisterDelayWithHit("Add Delayed hit in Queue ctor", kv.Key, hit, UniqueUid);
                    }
                }
            }

            if (tilesToDamageOneInTurn != null)
            {
                foreach (var h in tilesToDamageOneInTurn)
                {
                    PopQueueMonitor.RegisterTileHitOneInTurn("Add Hit One In Turn in Queue ctor", h, UniqueUid);
                }
            }

            if (tilesToBlowUpInTheEnd != null)
            {
                foreach (var h in tilesToBlowUpInTheEnd)
                {
                    PopQueueMonitor.RegisterTileToBlowUpAtEnd("Add End tile Hit in Queue ctor", h, UniqueUid);
                }
            }

            if (busyCells != null)
            {
                foreach (var c in busyCells)
                {
                    PopQueueMonitor.RegisterBusyCell("Add Busy cell in Queue ctor", c, UniqueUid);
                }
            }

            if (frozenCells != null)
            {
                foreach (var c in frozenCells)
                {
                    PopQueueMonitor.RegisterFrozenCell("Add Frozen cell in Queue ctor", c, UniqueUid);
                }
            }
#endif
        }

        public void Append(Queue q)
        {
            if (q == null) return;

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterQueueStateBeginChange("Append queue <" + q.UniqueUid + ">", this.UniqueUid);
#endif

            AppendDelaysWithHits(q._delaysWithHits);
            AppendHitsForEndTiles(q._tilesToBlowUpInTheEnd);
            AppendHitsForEndCells(q._cellsToHitInTheEnd);
            AppendBusyCells(q._busyCells);
            AppendPreHitActions(q._preHitActions);
        }

        private void AppendPreHitActions(SortedNumberedMultiMap<PreHitActionBase> preHitActions)
        {
            if (_preHitActions == null)
            {
                _preHitActions = preHitActions;
            }
            else if(preHitActions != null)
            { 
                _preHitActions.Append(preHitActions);
            }
        }

        private void AppendDelaysWithHits(SortedNumberedMultiMap<Hit> newDelaysWithHits)
        {
            if (newDelaysWithHits == null) 
                return;

#if M3_QUEUE_DEBUG
                foreach (var dwh in newDelaysWithHits)
                {
                    var prefix = "AppendDelay_"+dwh.Key+"_WithHit";
                    foreach (var h in dwh.Value.HitList)
                    {
                        bool isAlreadyPresent = false;
                        if (_delaysWithHits != null && _delaysWithHits.ContainsKey(dwh.Key))
                        {
                            foreach (var hit in _delaysWithHits[dwh.Key].HitList)
                            {
                                foreach (var newHit in dwh.Value.HitList)
                                {
                                    if (hit.Equals(newHit))
                                    {
                                        isAlreadyPresent = true;
                                        break;
                                    }
                                }

                                if (isAlreadyPresent) break;
                            }
                        }

                        if (isAlreadyPresent)
                        {
                            PopQueueMonitor.RegisterQueueStateBeginChange("AppendDelayHit, but it is already present: '" + dwh.Key + "' hit at "+h.Coords, UniqueUid);
                        }
                        else
                        {
                            PopQueueMonitor.RegisterDelayWithHit(prefix, dwh.Key, h, UniqueUid);
                        }
                    }
                }
#endif

            if (_delaysWithHits == null)
            {
                _delaysWithHits = new SortedNumberedMultiMap<Hit>(newDelaysWithHits);
            }
            else
            {
                foreach (var tuple in newDelaysWithHits)
                {
                    var newDelay = tuple.Item1;
                    var hitsList = tuple.Item2;
                    
                    foreach(var hit in hitsList)
                        _delaysWithHits.Add(newDelay, hit);
                }
            }
        }

        public void AddHitForEndCell(Hit<Cell> hit)
        {
            if (_cellsToHitInTheEnd == null)
            {
                _cellsToHitInTheEnd = new Queue<Hit<Cell>>();
            }
            _cellsToHitInTheEnd.Enqueue(hit);

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterCellHitAtEnd("AddEndHitCell", hit, UniqueUid);
#endif
        }

        public void AppendHitsForEndCells(Queue<Hit<Cell>> hits)
        {
            if (hits == null) return;

            if (_cellsToHitInTheEnd == null)
            {
                _cellsToHitInTheEnd = hits;
            }
            else
            {
                foreach (var h in hits)
                {
                    _cellsToHitInTheEnd.Enqueue(h);
                }
            }

#if M3_QUEUE_DEBUG
            foreach (var h in hits)
            {
                PopQueueMonitor.RegisterCellHitAtEnd("AppendEndCells", h, UniqueUid);
            }
#endif
        }

        public void AppendSingleHitForEndTiles(Hit<Tile> hit)
        {
            if (hit == null)
                return;
            
            if (_tilesToBlowUpInTheEnd == null)
            {
                _tilesToBlowUpInTheEnd = new HashSet<Hit<Tile>>();
            }

            _tilesToBlowUpInTheEnd.Add(hit);

        }
        

        public void AppendHitsForEndTiles(HashSet<Hit<Tile>> hits)
        {
            if (hits == null) return;

            if (_tilesToBlowUpInTheEnd == null)
            {
                _tilesToBlowUpInTheEnd = hits;
            }
            else
            {

                _tilesToBlowUpInTheEnd.UnionWith(hits);
            }

#if M3_QUEUE_DEBUG
            foreach (var h in hits)
            {
                PopQueueMonitor.RegisterTileToBlowUpAtEnd("AppendHitEndTiles", h, UniqueUid);
            }
#endif
        }

        public void AddBusyCell(Cell cell)
        {
            if (_busyCells == null)
            {
                _busyCells = new HashSet<Cell> { cell };
            }
            else
            {
                _busyCells.Add(cell);
            }

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterBusyCell("AddBusyCell", cell, UniqueUid);
#endif
        }

        private void AppendBusyCells(HashSet<Cell> cells)
        {
            if (cells == null) return;

            if (_busyCells == null)
            {
                _busyCells = cells;
            }
            else
            {
                _busyCells.UnionWith(cells);
            }

#if M3_QUEUE_DEBUG
            foreach (var c in cells)
            {
                PopQueueMonitor.RegisterBusyCell("AppendBusyCell", c, UniqueUid);
            }
#endif
        }

        public IEnumerable<PreHitActionBase> GetCurrentPreHitActions()
        {
            if (_preHitActions == null)
                return null;

            _preHitActions.GetFromNumber(0, out var list);
            _preHitActions.Remove(0);
            return list;
        }

        /// Get hits with zero delay.
        /// </summary>
        public List<Hit> GetCurrentHits()
        {
#if M3_QUEUE_DEBUG
            var expectedCount = _delaysWithHits != null && _delaysWithHits.ContainsKey(0) ? _delaysWithHits[0].HitList.Count : 0;
            PopQueueMonitor.RegisterRemoveZeroDelayHits("GetZeroHits", expectedCount, UniqueUid);
#endif

            if (!HasCellsToDamage) return null;

            if (!_delaysWithHits.GetFromNumber(0, out var hits)) return null;
            
            _delaysWithHits.Remove(0);
            return hits;

        }

        public Hit GetEndHit(Grid grid)
        {
            if (_tilesToBlowUpInTheEnd is { Count: > 0 })
            {
                Hit<Tile> hit = null;

                foreach (var tileHit in _tilesToBlowUpInTheEnd)
                {
                    hit = tileHit;
                    break;
                }
 
                _tilesToBlowUpInTheEnd.Remove(hit);
                var tile = hit?.Target;
                
                if (tile != null)
                {
                    foreach (var cell in grid.Cells)
                    {
                        if (cell.Tile != tile) continue;
#if M3_QUEUE_DEBUG
                        PopQueueMonitor.RegisterRemoveEndTileHit("GetEndTileHit", hit.Coords, UniqueUid);
#endif
                        return hit;
                    }
                }
            }

            if (_cellsToHitInTheEnd is { Count: > 0 })
            {
                var cellHit = _cellsToHitInTheEnd.Dequeue();
                var cell = cellHit.Target;
                if (cell is not null || (cellHit.DamageSource & DamageSource.Skunk) != 0)
                {
#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterRemoveEndCellHit("GetEndCellHit", cellHit.Coords, UniqueUid);
#endif
                    return cellHit;
                }
            }

            return default;
        }

        public void AddPreHitAction(int delay, PreHitActionBase action)
        {
            _preHitActions ??= new SortedNumberedMultiMap<PreHitActionBase>();

            _preHitActions.Add(delay, action);
        }

        public void AppendHit(int delay, Hit hit)
        {
            _delaysWithHits ??= new SortedNumberedMultiMap<Hit>();

            //do not allow to spawn two hits
            if (_delaysWithHits.ContainsAt(delay, hit))
                return;
            
            _delaysWithHits.Add(delay, hit);

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterDelayWithHit("AddHitWithDelay", delay, hit, UniqueUid);
#endif
        }

        public void Shift()
        {
#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterQueueStateBeginChange("Shift", UniqueUid);
#endif

            _preHitActions?.Shift();
            _delaysWithHits?.Shift();

            // Reducing delay until cell stops being busy:
            if (!_busyCells.NotNullOrEmpty()) return;
            
            for (var i = _busyCells.Count - 1; i >= 0; i--)
            {
                Cell busyCell = null;
                var currentIndex = 0;

                foreach (var cell in _busyCells)
                {
                    if (currentIndex == i)
                    {
                        busyCell = cell;
                        break;
                    }
                    currentIndex++;
                }

                if (busyCell == null) continue;
                
                busyCell.IsBusy--;

                if (busyCell.IsBusy > 0) continue;

                _busyCells.Remove(busyCell);

                if (busyCell.IsAnyOf(CellState.InvinvibleWhileBusy))
                {
                    busyCell.Remove(CellState.InvinvibleWhileBusy);
                }

#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterDecreaseBusyCell("BusyCell shift", busyCell, busyCell.IsBusy, UniqueUid);
#endif
            }
        }

        public void Clear()
        {
            _delaysWithHits?.Clear();
            _tilesToBlowUpInTheEnd?.Clear();
            _busyCells?.Clear();
            _preHitActions?.Clear();

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterQueueClear(UniqueUid);
#endif
        }
    }
}
