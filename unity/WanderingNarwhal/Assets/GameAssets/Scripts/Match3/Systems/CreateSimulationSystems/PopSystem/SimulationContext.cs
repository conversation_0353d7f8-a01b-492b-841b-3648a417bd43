using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes
{
    public class SimulationContext
    {
        public Grid Grid { get; }
        public SimulationInputParams InputParams { get; }
        public IRootSimulationHandler Handler { get; }
        public PopSystem PopSystem { get; }
        public M3SpawnSystem SpawnSystem { get; }
        public SettleTilesSystem SettleTileSystem { get; }
        public Queue Queue { get; }
        public TileHitReactionHandler ReactionHandler { get; }
        public GoalsSystem GoalSystem { get; }
        public Queue CellsToDamageQueue { get; }

        public SimulationContext(Grid grid, SimulationInputParams inputParams,
            IRootSimulationHandler handler, PopSystem popSystem,
            M3SpawnSystem spawnSystem, SettleTilesSystem settleTileSystem, Queue queue,
            TileHitReactionHandler reactionHandler, GoalsSystem goalSystem, Queue cellsToDamageQueue)
        {
            Grid = grid;
            InputParams = inputParams;
            Handler = handler;
            PopSystem = popSystem;
            SpawnSystem = spawnSystem;
            SettleTileSystem = settleTileSystem;
            Queue = queue;
            ReactionHandler = reactionHandler;
            GoalSystem = goalSystem;
            CellsToDamageQueue = cellsToDamageQueue;
        }
    }

    public class HitContext
    {
        public Cell MainCell { get; }
        public Cell Cell { get; }
        public Coords Coords { get; }
        public Coords CoordsOffset { get; set; }
        public Hit Hit { get; }
        public HitWaitParams HitWaitParams { get; }
        public int? Skin { get; set; }

        public HitContext(Cell mainCell, Cell cell, Coords coords, Coords coordsOffset,
            Hit hit, HitWaitParams hitWaitParams, int? skin)
        {
            MainCell = mainCell;
            Cell = cell;
            Coords = coords;
            CoordsOffset = coordsOffset;
            Hit = hit;
            HitWaitParams = hitWaitParams;
            Skin = skin;
        }
    }
}