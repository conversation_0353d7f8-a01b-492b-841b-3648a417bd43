using System;
using System.Collections.Generic;
using BBB.CellTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class RoutingCellSystem
    {   
        public static void RoutePaths(Grid grid, Dictionary<Coords, Path> cellsAndDirections)
        {
            
#if M3_PROFILE
            Profiler.BeginSample("RoutePaths");
#endif
            cellsAndDirections.Clear();

            grid.RefrehsAllCellsMultisizeCaches();

            // Marking diagonal:
            foreach (var cell in grid.Cells)
            {
                RouteCell(grid, cellsAndDirections, cell);
            }
            
#if M3_PROFILE
            Profiler.EndSample();
#endif
        }

        private static void RouteCell(Grid grid, Dictionary<Coords, Path> cellsAndDirections, Cell cell)
        {
            var path = new Path(PathState.Normal);
            path.ActivateAllDirections();

            if (SouthDirBlocked(cell, grid))
                path.DeactivateDirection(CardinalDirections.S);

            if (DiagDirBlocked(cell, grid, CardinalDirections.SE))
                path.DeactivateDirection(CardinalDirections.SE);

            if (DiagDirBlocked(cell, grid, CardinalDirections.SW))
                path.DeactivateDirection(CardinalDirections.SW);

            cellsAndDirections.Add(cell.Coords, path);
        }

        public static void CollectCoordsInCellPath(Grid grid, Cell cell, HashSet<Coords> affectedCoords, ref Dictionary<Coords, Path> cachedPath)
        {
            cachedPath.Clear();
            RouteCell(grid, cachedPath, cell);
            while (cachedPath.Count > 0)
            {
                KeyValuePair<Coords,Path> currentPath = default;

                var enumerator = cachedPath.GetEnumerator();
                if (enumerator.MoveNext())
                {
                    currentPath = enumerator.Current;
                }
                
                cachedPath.Remove(currentPath.Key);
                if (!affectedCoords.Add(currentPath.Key))
                    continue;

                switch (currentPath.Value.State)
                {
                    case PathState.Blocked:
                        break;
                    case PathState.Normal:
                        if ((currentPath.Value.Directions & CardinalDirections.S) == CardinalDirections.S)
                        {
                            var southCoords = currentPath.Key.GoSouthDirectionExtruded(grid, out _);
                            if (!cachedPath.ContainsKey(southCoords))
                            {
                                RouteCell(grid, cachedPath, grid.GetCell(southCoords));
                            }
                        }

                        if ((currentPath.Value.Directions & CardinalDirections.SE) == CardinalDirections.SE)
                        {
                            var southEastCoords = currentPath.Key.GoSingleCardinalDirection(CardinalDirections.SE);
                            if (!cachedPath.ContainsKey(southEastCoords))
                            {
                                RouteCell(grid, cachedPath, grid.GetCell(southEastCoords));
                            }
                        }

                        if ((currentPath.Value.Directions & CardinalDirections.SW) == CardinalDirections.SW)
                        {
                            var southWestCoords = currentPath.Key.GoSingleCardinalDirection(CardinalDirections.SW);
                            if (!cachedPath.ContainsKey(southWestCoords))
                            {
                                RouteCell(grid, cachedPath, grid.GetCell(southWestCoords));
                            }
                        }

                        break;
                    default:
                        throw new NotImplementedException($"Behavior for {currentPath.Value.State} wasn't implemented!");
                }
            }
        }

        private static bool SouthDirBlocked(Cell cell, Grid grid)
        {
            if (cell.HasMultiSizeCellReference()) return true;

            var southDir = CardinalDirections.S;
            var southCoords = cell.Coords.GoSouthDirectionExtruded(grid, out int _);
            
            if (IsCellHardBlocked(grid, southCoords, out var southCell))
                return true;

            if (IsCellSoftBlocked(southCell))
                return true;
            
            if (cell.HasAnyWall(southDir))
                return true;
            
            if(southCell.HasAnyWall(southDir.Reversed()))
                return true;
            
            return false;
        }

        private static bool DiagDirBlocked(Cell cell, Grid grid, CardinalDirections direction)
        {
            if (cell.HasMultiSizeCellReference()) return true;

            var diagCoords = cell.Coords.GoSingleCardinalDirection(direction);
            if (IsCellHardBlocked(grid, diagCoords, out var diagCell))
                return true;

            if (IsCellSoftBlocked(diagCell))
                return true;
            
            direction.DecomposeToHorVer(out var sideDir, out var southDir);

            var southCoord = cell.Coords.GoSingleCardinalDirection(southDir);
            var sideCoord = cell.Coords.GoSingleCardinalDirection(sideDir);

            bool hasTopVerWall = grid.HasWallBetween(cell.Coords, sideCoord);
            bool hasMainHorWall = grid.HasWallBetween(cell.Coords, southCoord);
            bool hasBottomVerWall = grid.HasWallBetween(southCoord, diagCoords);
            bool hasSideHorWall = grid.HasWallBetween(sideCoord, diagCoords);

            if (hasMainHorWall)
            {
                if (hasTopVerWall || hasSideHorWall)
                    return true;
            }

            if (hasBottomVerWall)
            {
                if (hasSideHorWall || hasTopVerWall)
                    return true;
            }

            bool southCellHardBlocked = IsCellHardBlocked(grid, southCoord, out var southCell);
            
            if (southCellHardBlocked)
            {
                if (hasTopVerWall || hasSideHorWall)
                    return true;
            }

            bool sideCellHardBlocked = IsCellHardBlocked(grid, sideCoord, out var sideCell);

            if (sideCellHardBlocked)
            {
                if (hasMainHorWall || hasBottomVerWall)
                    return true;
            }

            if (diagCell.IsAnyOf(CellState.Spawner))
                return true;

            return false;
        }

        private static bool IsCellHardBlocked(Grid grid, Coords coords, out Cell cell)
        {
            if (!grid.TryGetCell(coords, out cell)) return true;
            if (cell.IsAnyOf(CellState.NotAcceptingTiles)) return true;
            if (cell.HasMultiSizeCellReference()) return true;

            return false;
        }

        private static bool IsCellSoftBlocked(Cell cell)
        {
            var tile = cell.Tile;
            if (!ReferenceEquals(tile, null) && tile.IsAnyOf(TileState.ZeroGravity))
            {
                return true;
            }

            return false;
        }
    }

    public struct Path
    {
        public PathState State;
        public CardinalDirections Directions;

        public Path(PathState state)
        {
            State = state;
            Directions = CardinalDirections.None;
        }

        public void ActivateAllDirections()
        {
            Directions = CardinalDirections.S | CardinalDirections.SE | CardinalDirections.SW;
        }

        public void DeactivateDirection(CardinalDirections directions)
        {
            Directions &= ~directions;

            if (Directions == CardinalDirections.None && State == PathState.Normal)
                State = PathState.Blocked;
        }

        public override string ToString()
        {
            return State + " " + Directions;
        }
    }

    public enum PathState { Blocked, Normal }
}
