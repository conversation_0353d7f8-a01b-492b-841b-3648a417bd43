using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.UI.Level.Input;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static partial class SearchMatchesSystem
    {
        public static IPlayerInput ToPlayerInput(PossibleMove possibleMove)
        {
            if (possibleMove.Type == PossibleMoveType.DoubleTap)
            {
                return new PlayerInputDoubleTap(possibleMove.FirstCell.Coords);
            }

            return new PlayerInputSwap(new EasyTouchInputController.CoordsPair(
                possibleMove.FirstCell.Coords,
                possibleMove.SecondCell.Coords));
        }

        public static bool AreNoMatches(Grid grid)
        {
            // Going all rows:
            for (int i = 0; i < grid.Height; i++)
            {
                if (HasAnyMatchesInLine(grid, CardinalDirections.E, new Coords(0, i), grid.Width))
                {
                    return false;
                }
            }

            // Going all columns:
            for (int i = 0; i < grid.Width; i++)
            {
                if (HasAnyMatchesInLine(grid, CardinalDirections.N, new Coords(i, 0), grid.Height))
                {
                    return false;
                }
            }

            if (HasAnySquareMatch(grid))
                return false;

            return true;
        }

        public static HashSet<Match> FindAllMatches(Grid grid)
        {
            HashSet<Match> matchesOutput = null;
            
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.BeginSample("FindAllMatches");
#endif
            // Going all rows:
            for (int i = 0; i < grid.Height; i++)
            {
                FindMatchesInLine(grid, ref matchesOutput, CardinalDirections.E, new Coords(0, i), grid.Width);
            }

            // Going all columns:
            for (int i = 0; i < grid.Width; i++)
            {
                FindMatchesInLine(grid, ref matchesOutput, CardinalDirections.N, new Coords(i, 0), grid.Height);
            }

            FindSquareMatches(grid, ref matchesOutput);


#if M3_PROFILE
            UnityEngine.Profiling.Profiler.EndSample();
#endif
            return matchesOutput;
        }

        private static bool HasAnyMatchesInLine(Grid grid, CardinalDirections cardinalDirections, Coords coords, int size)
        {
            Cell checkCell = null;
            var length = 0;

            var workingCoords = coords;
            for (int i = 0; i <= size; i++)
            {
                workingCoords.X = coords.X;
                workingCoords.Y = coords.Y;
                
                switch (cardinalDirections)
                {
                    case CardinalDirections.E: workingCoords.X += i; break;
                    case CardinalDirections.W: workingCoords.X -= i; break;
                    case CardinalDirections.N: workingCoords.Y += i; break;
                    case CardinalDirections.S: workingCoords.Y -= i; break;
                }
                
                Cell cell;
                if (grid.TryGetCell(workingCoords, out cell))
                {
                    if (cell.IsBaseCellMatchable())
                    {
                        var tile = cell.Tile;
                        if (tile.IsMatchable())
                        {
                            if (checkCell != null)
                            {
                                if (checkCell.Tile.IsMatchable())
                                {
                                    if (checkCell.Tile.Kind == tile.Kind)
                                    {
                                        length++;
                                        continue;
                                    }
                                }
                            }
                            else
                            {
                                checkCell = cell;
                                length++;
                                continue;
                            }
                        }
                        else
                        {
                            cell = null;
                        }
                    }
                    else
                    {
                        cell = null;
                    }
                }

                if(checkCell == null) continue;

                if (length >= M3Constants.MinMatchLength)
                {
                    return true;
                }

                checkCell = cell;
                length = cell == null ? 0 : 1;
            }

            return false;
        }

        private static bool IsMatchableKind(Grid grid, Coords coords, TileKinds kind)
        {
            return grid.TryGetCell(coords, out Cell cell) && cell.IsBaseCellMatchable() &&
                   !ReferenceEquals(cell.Tile, null) && cell.Tile.IsMatchable() && cell.Tile.Kind == kind;
        }

        private static bool TryGetMatchAroundInLine(Grid grid, CardinalDirections cardinalDirections, Coords coords, out Match match)
        {
            if (!grid.TryGetCell(coords, out Cell checkCell) || !checkCell.IsBaseCellMatchable() ||
                !checkCell.Tile.IsMatchable())
            {
                match = default;
                return false;
            }

            TileKinds checkKind = checkCell.Tile.Kind;
            Coords startCoord = coords;
            var length = 1;

            //go one direction
            var workingCoords = coords.GoSingleCardinalDirection(cardinalDirections);
            while (IsMatchableKind(grid, workingCoords, checkKind))
            {
                length++;
                if (workingCoords.X < startCoord.X || workingCoords.Y < startCoord.Y)
                    startCoord = workingCoords;
                workingCoords = workingCoords.GoSingleCardinalDirection(cardinalDirections);
            }

            //go opposite direction
            workingCoords = coords.GoSingleCardinalDirection(cardinalDirections, -1);
            while (IsMatchableKind(grid, workingCoords, checkKind))
            {
                length++;
                if (workingCoords.X < startCoord.X || workingCoords.Y < startCoord.Y)
                    startCoord = workingCoords;
                workingCoords = workingCoords.GoSingleCardinalDirection(cardinalDirections, -1);
            }
            return TryToCreateMatch(grid.GetCell(startCoord), length, cardinalDirections, out match);
        }

        private static void FindMatchesInLine(Grid grid, ref HashSet<Match> matchesOutput, CardinalDirections cardinalDirections, Coords coords, int size)
        {
            Cell checkCell = null;
            var length = 0;

            var workingCoords = coords;
            for (int i = 0; i <= size; i++)
            {
                workingCoords.X = coords.X;
                workingCoords.Y = coords.Y;
                
                switch (cardinalDirections)
                {
                    case CardinalDirections.E: workingCoords.X += i; break;
                    case CardinalDirections.W: workingCoords.X -= i; break;
                    case CardinalDirections.N: workingCoords.Y += i; break;
                    case CardinalDirections.S: workingCoords.Y -= i; break;
                }

                if (grid.TryGetCell(workingCoords, out var cell))
                {
                    if (cell.IsBaseCellMatchable())
                    {
                        var tile = cell.Tile;
                        if (tile.IsMatchable())
                        {
                            if (checkCell != null)
                            {
                                if (checkCell.Tile.IsMatchable())
                                {
                                    if (checkCell.Tile.Kind == tile.Kind)
                                    {
                                        length++;
                                        continue;
                                    }
                                }
                            }
                            else
                            {
                                checkCell = cell;
                                length++;
                                continue;
                            }
                        }
                        else
                        {
                            cell = null;
                        }
                    }
                    else
                    {
                        cell = null;
                    }
                }

                if(checkCell == null) continue;

                if (TryToCreateMatch(checkCell, length, cardinalDirections, out var match))
                {
                    if(matchesOutput == null) matchesOutput = new HashSet<Match>();
                    
                    matchesOutput.Add(match);
                }
                checkCell = cell;
                length = cell == null ? 0 : 1;
            }
        }

        private static void CreateSquareMatch(Cell cell, out Match match)
        {
            match = new Match(cell.Coords, 4, CardinalDirections.None, cell.Tile.Kind, MatchType.Square);
        }

        private static bool TryToCreateMatch(Cell cell, int length, CardinalDirections direction, out Match match)
        {
            
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.BeginSample("TryToCreateMatch");
#endif
            if (length < M3Constants.MinMatchLength)
            {
                match = default(Match);
#if M3_PROFILE
                UnityEngine.Profiling.Profiler.EndSample();
#endif
                return false;
            }

            match = new Match(cell.Coords, length, direction, cell.Tile.Kind);
            
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.EndSample();
#endif
            return true;
        }

        public static void FindMatchesForCells(Grid grid, Cell firstCell, Cell secondCell, ref HashSet<Match> matches)
        {
            FindMatchForCell(grid, firstCell, ref matches);
            FindMatchForCell(grid, secondCell, ref matches);
        }

        public static void FindMatchForCell(Grid grid, Cell cell, ref HashSet<Match> matches)
        {
            if (!cell.IsBaseCellMatchable() || !cell.Tile.IsMatchable()) return;
            var coords = cell.Coords;

            if (TryGetMatchAroundInLine(grid, CardinalDirections.E, coords, out Match horizontalMatch))
                matches.Add(horizontalMatch);
            if (TryGetMatchAroundInLine(grid, CardinalDirections.N, coords, out Match verticalMatch))
                matches.Add(verticalMatch);
            FindSquareMatchesForCell(cell, grid, matches);
        }

        public static bool AreAnyBoost(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                var tile = cell.Tile;
                if (ReferenceEquals(tile, null))
                    continue;

                if (!cell.IsDirectlyInteractable)
                    continue;
                
                if (cell.Tile.IsBoost)
                    return true;
            }

            return false;
        }

        private static void FindSquareMatches(Grid grid, ref HashSet<Match> matchesOutput)
        {
            var width = grid.Width;
            var height = grid.Height;
            for (int x = 0; x < width; x++)
            for (int y = 0; y < height; y++)
            {
                var workingCoords = new Coords(x,y);
                if (FindSquareMatchAt(workingCoords, grid, out var workingCell))
                {
                    CreateSquareMatch(workingCell, out var match);
                    if(matchesOutput == null) 
                        matchesOutput = new HashSet<Match>();
                    
                    matchesOutput.Add(match);
                }
            }
        }

        private static bool FindSquareMatchAt(Coords coords, Grid grid, out Cell cell)
        {
            if (grid.TryGetCell(coords, out cell) 
                && cell.IsBaseCellMatchable() && 
                cell.Tile.IsMatchable())
            {
                bool allNeighboursOfSquareAreOfSameKind = true;
                for (int i = 1; i <= 3; i++)
                {
                    var neighbourCoords = MatchHelper.SquareCoordsFromIndex(i, coords);
                    if (grid.TryGetCell(neighbourCoords, out var neighbourCell)
                        && neighbourCell.IsBaseCellMatchable() 
                        && neighbourCell.Tile.IsMatchable())
                    {
                        if (cell.Tile.Kind != neighbourCell.Tile.Kind)
                        {
                            allNeighboursOfSquareAreOfSameKind = false;
                            break;
                        }
                    }
                    else
                    {
                        allNeighboursOfSquareAreOfSameKind = false;
                        break;
                    }

                }

                return allNeighboursOfSquareAreOfSameKind;
            }

            cell = null;
            return false;
        }

        private static void FindSquareMatchesForCell(Cell cell, Grid grid, ICollection<Match> matchesOutput)
        {
            var coords = cell.Coords;
            for (int i = 0; i <= 3; i++)
            {
                var neighbourCoords = MatchHelper.SquareCoordsFromIndex(i, coords);
                var deltaCoords = neighbourCoords - coords;
                var oppositeNeighbourCoords = coords - deltaCoords;
                if (FindSquareMatchAt(oppositeNeighbourCoords, grid, out var cellMatchFoundFor))
                {
                    CreateSquareMatch(cellMatchFoundFor, out var match);
                    matchesOutput.Add(match);
                }
            }  
        }

        private static bool HasAnySquareMatch(Grid grid)
        {
            var width = grid.Width;
            var height = grid.Height;
            for (int x = 0; x < width; x++)
            for (int y = 0; y < height; y++)
            {
                var workingCoords = new Coords(x,y);
                if (FindSquareMatchAt(workingCoords, grid, out var workingCell))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
