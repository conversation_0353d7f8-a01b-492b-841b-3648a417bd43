using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class TileCollectorsHandler
    {
        public static readonly List<TileHitReactionHandler.TileReactRecord> DieReactionsCache = new(5);

        public static bool IsTileCollector(Coords from, Grid grid)
        {
            if (!grid.TryGetCell(from, out var cell))
            {
                return false;
            }

            if (cell.IsAnyOf(CellState.Tnt))
            {
                return true;
            }

            return cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk;
        }

        public static void HandleTileCollectorsDeath(Grid grid, IRootSimulationHandler events,
            TileHitReactionHandler reactionHandler, Queue queue, GoalsSystem goalSystem)
        {
            for (var i = DieReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = DieReactionsCache[i];
                var from = item.TileCoords;
                if (!grid.TryGetCell(from, out var cell))
                {
                    continue;
                }

                if (cell.IsAnyOf(CellState.Tnt))
                {
                    TntCellLayer.HandleTnt(cell, grid, from, events, goalSystem, item.HitWaitParams);
                }
                else if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk)
                {
                    var busyCells = TukTukTile.HandleTukTukDamage(cell, grid, events, queue, item.HitWaitParams);
                    reactionHandler.NewBusyCells ??= new List<Cell>(busyCells.Count);
                    reactionHandler.NewBusyCells.AddRange(busyCells);
                }

                DieReactionsCache.RemoveAt(i);
            }
        }

        public static bool NotifyCollectorsOnTileDie(Grid grid, IRootSimulationHandler events, Coords coords,
            TileAsset tileAsset, TileKinds tileKinds, HitWaitParams hitWaitParams)
        {
            Cell tntCell = null;
            Cell tuktukCell = null;
            var minTntCount = int.MaxValue;
            var minTuktukCount = int.MaxValue;

            var isACollectableTile = false;

            foreach (var cell in grid.Cells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && tileAsset == TileAsset.Simple
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    if (count > 0 && (count < minTuktukCount ||
                                      (count == minTuktukCount && (tuktukCell == null ||
                                                                   cell.Coords.Y > tuktukCell.Coords.Y ||
                                                                   (cell.Coords.Y == tuktukCell.Coords.Y &&
                                                                    cell.Coords.X < tuktukCell.Coords.X)))))
                    {
                        tuktukCell = cell;
                        minTuktukCount = count;
                        isACollectableTile = true;
                    }
                }

                if (cell.IsAnyOf(CellState.Tnt) && cell.TntCount > 0 &&
                    TntCellLayer.IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds))
                {
                    if (cell.TntCount < minTntCount ||
                        (cell.TntCount == minTntCount && (tntCell == null ||
                                                          cell.Coords.Y > tntCell.Coords.Y ||
                                                          (cell.Coords.Y == tntCell.Coords.Y &&
                                                           cell.Coords.X < tntCell.Coords.X))))
                    {
                        tntCell = cell;
                        minTntCount = cell.TntCount;
                        isACollectableTile = true;
                    }
                }
            }

            if (tntCell != null)
            {
                TntCellLayer.NotifyTnt(events, tntCell, coords, hitWaitParams);
            }

            if (tuktukCell != null)
            {
                TukTukTile.NotifyTukTuk(events, tuktukCell, coords, hitWaitParams);
            }

            return isACollectableTile;
        }

        public static void NotifyCollectorsOnCellOverlayDie(Grid grid, IRootSimulationHandler handler,
            Coords coords, CellState cellState, HitWaitParams hitWaitParams)
        {
            foreach (var cell in grid.Cells)
            {
                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                switch (cell.TntTarget)
                {
                    case TntTargetType.Grass:
                    {
                        if ((cellState & (CellState.BackOne | CellState.BackDouble | CellState.Petal)) == 0) continue;

                        break;
                    }
                    case TntTargetType.Ivy:
                    {
                        if ((cellState & CellState.Ivy) == 0) continue;

                        break;
                    }
                    default:
                        continue;
                }

                cell.TntCount--;
                if (cell.TntCount <= 0)
                {
                    AddToDieReactionCache(cell);
                }

                handler.AddAction(new ActionReduceTntCount(cell.Coords, coords, cell.SizeX, cell.SizeY, hitWaitParams));
            }
        }

        public static int GetTilesLeftToCollect(Grid grid, TileKinds tileKinds)
        {
            const TileAsset tileAsset = TileAsset.Simple;
            var tntCount = 0;
            var tuktukCount = 0;
            foreach (var cell in grid.Cells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    tuktukCount += count;
                }

                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                if (!TntCellLayer.IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds)) continue;
                tntCount += cell.TntCount;
            }

            return Math.Max(tntCount, tuktukCount);
        }

        public static void AddToDieReactionCache(Cell cell, HitWaitParams hitWaitParams = null)
        {
            DieReactionsCache.Add(new TileHitReactionHandler.TileReactRecord
            {
                TileCoords = cell.Coords,
                HitWaitParams = hitWaitParams
            });
        }
    }
}