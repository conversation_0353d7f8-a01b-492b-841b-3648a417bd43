using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class TileHitReactionHandler
    {
        public struct TileReactRecord
        {
            public HitWaitParams HitWaitParams;
            public int TileId;
            public Coords TileCoords;
            public TileSpeciality Speciality;
            public TileState State;
            public TileKinds Kind;
            public int? Skin;
            public Coords CoordsOffset;
            public Tile Tile;
        }
        
        public readonly List<TileReactRecord> DieReactionsCache = new(50);
        
        private readonly List<TileReactRecord> _adjacentHitReactionsCache = new(50);

        private int _lastTurn;

        public bool IsReactionCacheEmpty => DieReactionsCache.Count == 0 && _adjacentHitReactionsCache.Count == 0 &&
                                            TileSpawnHandler.DelayedSpawnReactions.Count == 0 && 
                                            TileCollectorsHandler.DieReactionsCache.Count == 0;

        public List<Cell> NewBusyCells { get; set; }
        

        public bool RegisterTileShouldReactOnDie(Grid grid, IRootSimulationHandler events, HitWaitParams hitWaitParams,
            Coords coords, int tileId,
            TileAsset tileAsset, TileSpeciality tileSpeciality, TileState tileState, TileKinds kind, Tile tile,
            int? skin = null, Coords coordsOffset = default)
        {
           var isACollectableTile = TileCollectorsHandler.NotifyCollectorsOnTileDie(grid, events, coords,
               tileAsset, kind, hitWaitParams);
           
            DieReactionsCache.Add(new TileReactRecord
            {
                HitWaitParams = hitWaitParams,
                TileCoords = coords,
                TileId = tileId,
                Speciality = tileSpeciality,
                State = tileState,
                Kind = kind,
                Skin = skin,
                CoordsOffset = coordsOffset,
                Tile = tile
            });
            return isACollectableTile;
        }

        public void RegisterTileShouldReactOnAdjacentHit(Coords coords, HitWaitParams hitWaitParams, int tileId,
            TileSpeciality tileSpeciality,
            TileState tileState, Tile tile, int? skin = null, Coords coordsOffset = default)
        {
            _adjacentHitReactionsCache.Add(new TileReactRecord
            {
                HitWaitParams = hitWaitParams,
                TileCoords = coords,
                TileId = tileId,
                Speciality = tileSpeciality,
                State = tileState,
                Kind = TileKinds.None,
                Skin = skin,
                CoordsOffset = coordsOffset,
                Tile = tile
            });
        }
        
        public void HandleScheduledReactions(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem, Queue queue, bool handleTileCollectorsDeath)
        {
            HandleOnDieReactions(grid, events, inputParams, goalSystem, popSystem);

            if (handleTileCollectorsDeath)
            {
                TileCollectorsHandler.HandleTileCollectorsDeath(grid, events, this, queue, goalSystem);
            }

            HandleOnAdjacentHitReactions(grid, events, inputParams, goalSystem, popSystem);

            GoalCollectHandler.OnAfterHandleStepEnd(grid, events, this, goalSystem);
        }

        public void HandleDelayedReactions(Grid grid, IRootSimulationHandler events, SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem, int currentTurn)
        {
            if (currentTurn <= _lastTurn) return;

            _lastTurn = currentTurn;

            TileSpawnHandler.HandleDelaySpawnedReactions(grid, events, this, inputParams, goalSystem, popSystem);
        }

        private void HandleOnDieReactions(Grid grid, IRootSimulationHandler events, SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem)
        {
            for (var i = DieReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = DieReactionsCache[i];
                var hitParams = item.HitWaitParams;
                var tileId = item.TileId;
                var from = item.TileCoords;
                var state = item.State;
                var speciality = item.Speciality;
                var kind = item.Kind;
                var tile = item.Tile;
                var skin = item.Skin;
                var coordsOffset = item.CoordsOffset;

                if (TileCollectorsHandler.IsTileCollector(from, grid))
                {
                    continue;
                }

                var reactionType = GetOnDieReactionFromSourceTile(tile);

                HandleReaction(reactionType, grid, events, inputParams, goalSystem, popSystem,
                    from, hitParams, tileId, speciality, tile, state, kind, skin, coordsOffset);

                DieReactionsCache.RemoveAt(i);
            }
        }

        private void HandleOnAdjacentHitReactions(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem)
        {
            for (var i = _adjacentHitReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = _adjacentHitReactionsCache[i];
                var hitParams = item.HitWaitParams;
                var tileId = item.TileId;
                var from = item.TileCoords;
                var sourceState = item.State;
                var speciality = item.Speciality;
                var kind = item.Kind;
                var tile = item.Tile;
                
                var reactionType = GetOnAdjacentHitReactionFromSourceTile(tile);
                
                HandleReaction(reactionType, grid, events, inputParams, goalSystem, popSystem, from, hitParams, tileId, speciality,
                    item.Tile, sourceState, kind, item.Skin, item.CoordsOffset);
                
                _adjacentHitReactionsCache.RemoveAt(i);
            }
        }
        
        public int GetPendingGoalCollectionReactionsForGoal(GoalType goal)
        {
            var result = 0;
            foreach (var item in DieReactionsCache)
            {
                var reaction = GetOnDieReactionFromSourceTile(item.Tile);
                if (reaction != ReactionType.CollectGoal)
                {
                    continue;
                }

                if (GoalState.IsTileGridBasedGoalRelatedItem(item.Speciality, item.State, item.Kind, goal))
                {
                    result++;
                }
            }

            return result;
        }

        private void HandleReaction(ReactionType reaction, Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams, GoalsSystem goalSystem, PopSystem popSystem,
            Coords from, HitWaitParams hitWaitParams, int tileId,TileSpeciality tileSpeciality,
            Tile tile, TileState state, TileKinds kind, int? skin, Coords coordsOffset)
        {
            switch (reaction)
            {
                case ReactionType.SpawnTileAtRandomPosition:
                {
                    TileSpawnHandler.HandleSpawnTileAtRandomPosition(grid, goalSystem, this, from, tileId, state, tile);

                    if (tile != null && tile.HasReactionAndGoalCollection())
                    {
                        GoalCollectHandler.HandleCollectGoal(grid, goalSystem, popSystem, this, inputParams,
                            events, hitWaitParams, from, tileId, tileSpeciality, state, kind, skin,
                            coordsOffset);
                    }
                    break;
                }

                case ReactionType.SpawnSquareTiles:
                {
                    CellSpawnHandler.HandleSpawnSquareTile(goalSystem, grid, events, from, tile, hitWaitParams);
                    if (tile != null && tile.HasReactionAndGoalCollection())
                    {
                        GoalCollectHandler.HandleCollectGoal(grid, goalSystem, popSystem, this, inputParams,
                            events, hitWaitParams, from, tileId, tileSpeciality, state, kind, skin,
                            coordsOffset);
                    }
                    break;
                }

                case ReactionType.CollectGoal:
                {
                    GoalCollectHandler.HandleCollectGoal(grid, goalSystem, popSystem, this, inputParams,
                        events, hitWaitParams, from, tileId, tileSpeciality, state, kind, skin,
                        coordsOffset);
                    break;
                }
                case ReactionType.None:
                {
                    break;
                }
            }
        }
        
        private static ReactionType GetOnDieReactionFromSourceTile(Tile tile)
        {
            if (tile != null)
            {
                if (tile.SpawnsTileAtRandomPosition())
                {
                    return ReactionType.SpawnTileAtRandomPosition;
                }
                
                if (tile.SpawnsCellBackgrounds())
                {
                    return ReactionType.SpawnSquareTiles;
                }
                
                return tile.HasDieReaction() ? ReactionType.CollectGoal : ReactionType.None;
            }

            return ReactionType.None;
        }

        private static ReactionType GetOnAdjacentHitReactionFromSourceTile(Tile tile)
        {
            return tile.HasDieReaction() ? ReactionType.None : ReactionType.CollectGoal;
        }

        private enum ReactionType
        {
            None,
            SpawnTileAtRandomPosition,
            CollectGoal,
            SpawnSquareTiles
        }
    }
}