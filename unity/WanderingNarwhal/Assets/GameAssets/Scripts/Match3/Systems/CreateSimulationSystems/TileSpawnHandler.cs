using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;
using GameAssets.Scripts.Match3.Settings;

public class TileSpawnHandler
{
    private const int TileMaxSpawnCount = 4;
    
    public struct DelayedSpawnRecord
    {
        public Coords From;
        public Tile Tile;
        public TileAsset TileAsset;
        public int Delay;
        public int SourceTileId;
        public GoalType GoalType;
        public Tile SourceTile;
    }

    private const int MaxOverdueDelay = 50;
    private const int SpawnedTileBusyTime = 2;

    private static readonly Func<Cell, bool> IsCellWithRegularTileOrEmptyFunc = IsCellWithRegularTileOrEmpty;

    public static readonly List<DelayedSpawnRecord> DelayedSpawnReactions = new(20);

    public static TileState SpawnedTilesOnCurrentStep = TileState.None;

    public static void HandleSpawnTileAtRandomPosition(Grid grid, GoalsSystem goalSystem,
        TileHitReactionHandler reactionHandler, Coords from, int sourceTileId, TileState sourceState, Tile tile)
    {
        if ((sourceState & TileState.HiveMod) != 0)
        {
            if (HiveTile.CanHitBeehive(from, grid, goalSystem, reactionHandler) <= 0)
            {
                return;
            }
        }

        // All spawn reactions must be delayed, since target cell may take some steps to be available
        // Actual add new tile to the grid is happening in TryDoSpawnTileAtPosition.
        SpawnedTilesOnCurrentStep |= sourceState;

        var count = tile.GetParam(TileParamEnum.TileCreateCountForReaction);
        for (var i = 0; i < count; i++)
        {
            // At this stage we don't have exact target cell for spawning, so we assign what default cell[0].
            var originCell = grid.Cells[0];
            var (newTile, tileAsset, goalType) = tile.CreateTileFromReaction(grid.TilesSpawnedCount++, originCell, i);

            if (tileAsset == TileAsset.Undefined)
            {
                continue;
            }

            //Adding delay 1 for tiles with ShouldDelaySimulationOnReaction(SlotMachine and Fireworks) true to prevent targeting falling tiles 
            var delay = tile.ShouldDelaySimulationOnReaction ? 1 : 0;
            DelayedSpawnReactions.Add(new DelayedSpawnRecord
            {
                From = from,
                SourceTileId = sourceTileId,
                Tile = newTile,
                Delay = delay,
                TileAsset = tileAsset,
                GoalType = goalType,
                SourceTile = tile
            });
        }
    }

    public static void HandleDelaySpawnedReactions(Grid grid, IRootSimulationHandler events,
        TileHitReactionHandler reactionHandler,
        SimulationInputParams inputParams, GoalsSystem goalSystem, PopSystem popSystem)
    {
        if (DelayedSpawnReactions.Count == 0) return;

        var potentialCellList = grid.GetPrioritizedMatchingCells(IsCellWithRegularTileOrEmptyFunc);

        for (var i = DelayedSpawnReactions.Count - 1; i >= 0; i--)
        {
            var item = DelayedSpawnReactions[i];
            var delay = item.Delay - 1;

            if (delay <= 0)
            {
                if (potentialCellList.Count > 0)
                {
                    var potentialCell = potentialCellList[^1];
                    potentialCellList.RemoveAt(potentialCellList.Count - 1);

                    if (TryDoSpawnTileAtPosition(grid, events, inputParams, reactionHandler, item.SourceTile, item.Tile,
                            item.SourceTileId, item.From, item.TileAsset, item.GoalType, goalSystem, potentialCell))
                    {
                        DelayedSpawnReactions.RemoveAt(i);
                        continue;
                    }
                }

                if (delay > -MaxOverdueDelay)
                {
                    item.Delay = delay;
                    DelayedSpawnReactions[i] = item;
                }
                else
                {
                    DelayedSpawnReactions.RemoveAt(i);
                    GoalCollectHandler.HandleCollectGoal(grid, goalSystem, popSystem, reactionHandler, inputParams,
                        events, null, item.From, item.Tile.Id, item.Tile.Speciality,
                        item.Tile.State, item.Tile.Kind, null);
                }
            }
            else
            {
                item.Delay = delay;
                DelayedSpawnReactions[i] = item;
            }
        }
    }

    private static bool TryDoSpawnTileAtPosition(Grid grid, IRootSimulationHandler events,
        SimulationInputParams inputParams, TileHitReactionHandler reactionHandler, Tile sourceTile,
        Tile tile, int sourceTileId, Coords from, TileAsset tileAsset, GoalType goalType,
        GoalsSystem goalsSystem, Cell randomTileCell)
    {
        if (randomTileCell == null ||
            !randomTileCell.Tile.IsNull() && (randomTileCell.Tile.State & TileState.InTransition) != 0)
            return false;

        var spawnTileAction =
            new ActionSpawnTileFromAnotherTile(sourceTileId, from, randomTileCell.Coords,
                sourceTile, tile, tileAsset);
        events.AddAction(spawnTileAction);

        if (randomTileCell.Tile != null)
        {
            if (randomTileCell.Tile.Kind == TileKinds.Undefined)
            {
                randomTileCell.Tile.SetKind(inputParams.UsedKinds.DeterministicRandomInSelf());
                events.OnTileKindDefined(randomTileCell.Tile.Id, randomTileCell.Tile.Kind);
            }

            reactionHandler.RegisterTileShouldReactOnDie(grid, events, null, randomTileCell.Coords,
                randomTileCell.Tile.Id, randomTileCell.Tile.Asset, randomTileCell.Tile.Speciality,
                randomTileCell.Tile.State, randomTileCell.Tile.Kind, randomTileCell.Tile);

            randomTileCell.ReplaceTile(tile);
            events.AddAction(new ActionReplace(randomTileCell.Coords, tile, goalType));
        }
        else
        {
            randomTileCell.AddTile(tile);
            events.AddAction(new ActionSpawn(randomTileCell.Coords, tile, null, goalType));
        }

        if (goalType != GoalType.None)
        {
            goalsSystem.TryAddGoalIfNeeded(goalType);
        }

        randomTileCell.IsBusy += SpawnedTileBusyTime;

        reactionHandler.NewBusyCells ??= new List<Cell>(3);
        reactionHandler.NewBusyCells.Add(randomTileCell);

        return true;
    }

    private static bool IsCellWithRegularTileOrEmpty(Cell cell)
    {
        if (cell == null || cell.IsAnyOf(CellState.Ivy | CellState.Water | CellState.FlagEnd) ||
            cell.HasMultiSizeCellReference())
        {
            return false;
        }

        if (cell.Tile.IsNull())
        {
            return cell.CanAcceptTile();
        }

        return cell.Tile.Asset == TileAsset.Simple && cell.Tile.State == TileState.None;
    }

    public static void HandlePowerUpSpawnOutcome(Tile tile, SimulationContext simulationContext,
        HitContext hitContext, PowerUpSpawnOutcomeConfiguration.RewardConfiguration rewardConfiguration)
    {
        if (tile.GetParam(TileParamEnum.TileCreateCountForReaction) == 0)
        {
            var tileList = GetWeightedRandomOutcome(TileMaxSpawnCount, rewardConfiguration);
            var tilesToSpawn = PackTileAssetsDynamic(tileList);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                (TileParamEnum.TileToSpawnFromReaction, tilesToSpawn),
                (TileParamEnum.TileCreateCountForReaction, tileList.Count)
            };

            foreach (var (param, value) in tileParamList)
            {
                tile.SetParam(param, value);
            }

            simulationContext.Handler.AddAction(new ActionChangeTileParam(tile.Id, hitContext.Cell.Coords,
                tileParamList, hitContext.Hit.GetHitParams()));
        }
    }

    private static List<TileAsset> GetWeightedRandomOutcome(int tileMaxSpawnCount,
        PowerUpSpawnOutcomeConfiguration.RewardConfiguration configuration)
    {
        var spawnCount = Math.Min(configuration.MaxSpawnCount, tileMaxSpawnCount);

        var selectedRewards = new List<TileAsset>(spawnCount);

        var workingRewards = configuration.AllowSameType
            ? configuration.RewardConfigurations
            : new Dictionary<TileAsset, float>(configuration.RewardConfigurations);

        var totalWeight = 0f;
        if (configuration.AllowSameType)
        {
            foreach (var weight in workingRewards.Values)
            {
                totalWeight += weight;
            }
        }

        for (var i = 0; i < spawnCount && workingRewards.Count > 0; i++)
        {
            if (!configuration.AllowSameType)
            {
                totalWeight = 0f;
                foreach (var weight in workingRewards.Values)
                {
                    totalWeight += weight;
                }
            }

            var randomValue = RandomSystem.Next() * totalWeight;
            var weightSum = 0f;
            var selectedReward = TileAsset.Undefined;

            foreach (var kvp in workingRewards)
            {
                weightSum += kvp.Value;
                if (randomValue <= weightSum)
                {
                    selectedReward = kvp.Key;
                    break;
                }
            }

            if (selectedReward == TileAsset.Undefined)
            {
                selectedReward = workingRewards.Keys.DeterministicRandomInSelf();
            }

            selectedRewards.Add(selectedReward);

            if (!configuration.AllowSameType)
            {
                workingRewards.Remove(selectedReward);
            }
        }

        return selectedRewards;
    }

    /// <summary>
    /// Packs a list of TileAssets into an integer for dynamic tile creation.
    /// This compression is necessary because TileParam can only store integers,
    /// while the number of assets can vary. Allows storing up to 4 TileAssets
    /// (8 bits each) within a single 32-bit integer.
    /// </summary>
    private static int PackTileAssetsDynamic(List<TileAsset> assets)
    {
        var packed = 0;
        for (var i = 0; i < assets.Count; i++)
        {
            packed |= ((int)assets[i] & 0xFF) << (8 * (assets.Count - 1 - i));
        }

        return packed;
    }
    
    public static List<TileAsset> UnpackTileAssetsDynamic(Tile tile)
    {
        var tileAssetList = tile.GetParam(TileParamEnum.TileToSpawnFromReaction);
        var tileCount = tile.GetParam(TileParamEnum.TileCreateCountForReaction);
        
        var assets = new List<TileAsset>();
        for (var i = 0; i < tileCount; i++)
        {
            assets.Add((TileAsset)((tileAssetList >> (8 * (tileCount - 1 - i))) & 0xFF));
        }

        return assets;
    }
}