using BBB.Match3.Systems.CreateSimulationSystems;

namespace BBB.Match3.Systems
{
    public class AssistParamsUpdated : IResettableEvent
    {
        public AssistParams AssistParams { get; set; }
        public AssistState AssistProgressAchieved { get; set; }
        public AssistState OriginalAssistState { get; set; }
        public void Reset()
        {
            AssistParams = default;
            AssistProgressAchieved = default;
            OriginalAssistState = default;
        }
    }
}