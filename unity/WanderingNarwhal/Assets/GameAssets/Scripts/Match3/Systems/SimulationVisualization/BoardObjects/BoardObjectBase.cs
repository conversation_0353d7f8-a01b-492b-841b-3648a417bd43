using BBB.Match3.Renderer;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public abstract class BoardObjectBase
    {

        public int Id { get; }

        protected Vector2 StartPosition;
        public Vector2 Position { get; protected set; }
        
        public bool Deleted { get; private set; }
        
        public virtual bool CanBlockRect => false;


        protected BoardObjectBase(int id)
        {
            Id = id;
        }
        
        public void Tick(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if (!Deleted)
                Update(deltaTime, proxy);
            
            if (!Deleted)
                UpdateView(proxy, false);
        }
        public abstract void Update(float deltaTime, PlaySimulationActionProxy proxy);

        public virtual void UpdateView(PlaySimulationActionProxy proxy, bool isSpawnView){}

        public virtual void SpawnView(RendererContainers rendererContainers){}
        public virtual void AfterDeleted(PlaySimulationActionProxy proxy){}
        protected virtual void ReleaseView(PlaySimulationActionProxy proxy){}
        
        public virtual bool Overlaps(Vector2 worldPos)
        {
            return false;
        }

        public void Delete(PlaySimulationActionProxy proxy)
        {
            Deleted = true;
            ReleaseView(proxy);
        }
    }
}