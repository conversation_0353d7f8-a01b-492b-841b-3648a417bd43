using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems
{
    struct FutureTargetLock
    {
        public Coords Coords;
        public int TileId;
        public int Priority;
    }
        
    public class BoardObjectFactory
    {
        private int _boardObjectIdCounter;
        private readonly M3Settings _settings;
        private readonly TileTickPlayer _tileTickPlayer;
        private readonly Dictionary<Coords, FreeFallTarget> _freeFallTargetPriority = new();
        private readonly List<FutureTargetLock> _futureTargetLocks = new();
        private readonly HashSet<int> _spawnedBombIds = new();

        public BoardObjectFactory(IContext context)
        {
            _tileTickPlayer = context.Resolve<TileTickPlayer>();
            _settings = context.Resolve<M3Settings>();
        }
        
        public void CreateRocket(int rocketId, List<RocketTarget> rocketTargets, Vector2 direction, Vector2 startPosition)
        {
            var rocket = new Rocket(rocketId, rocketTargets, direction, startPosition, _settings, _boardObjectIdCounter++); 
            _tileTickPlayer.AddObject(rocket);
        }
        
        public void CreateTukTuk(int tuktukId, int tuktukColor, List<TukTukTarget> tuktukTargets, Vector2 direction,Vector2 startPosition)
        {
            var tuktuk = new TukTuk(tuktukId, tuktukColor, tuktukTargets, direction, startPosition, _settings, _boardObjectIdCounter++); 
            _tileTickPlayer.AddObject(tuktuk);
        }
        
        public void CreateVerticalBooster(int boosterId, List<DirectionalBoosterTarget> boosterTargets, Vector2 direction,
            Vector2 startPosition)
        {
            startPosition.y = 0;
            
            var verticalBooster = new DirectionalBooster(boosterId, boosterTargets, 
                direction, startPosition, _settings.VerticalBoosterSpeed, 
                _settings.VerticalBoosterAcceleration, _settings.VerticalBoosterAnticipationDelay, _boardObjectIdCounter++, FxType.VerticalBooster); 
            _tileTickPlayer.AddObject(verticalBooster);
        }
        
        public void CreateHorizontalBooster(int boosterId, List<DirectionalBoosterTarget> boosterTargets, Vector2 direction,
            Vector2 startPosition)
        {
            startPosition.x = _settings.HorizontalBoosterOffset;
            
            var horizontalBooster = new DirectionalBooster(boosterId, boosterTargets, 
                direction, startPosition, _settings.HorizontalBoosterSpeed, 
                _settings.HorizontalBoosterAcceleration, 0, _boardObjectIdCounter++, FxType.HorizontalBooster); 
            _tileTickPlayer.AddObject(horizontalBooster);
        }
        
        public StaticInvisibleOccupier CreateStaticOccupier(float lifeTime, Vector2 position)
        {
            var occupier = new StaticInvisibleOccupier(lifeTime, position, _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(occupier);
            return occupier;
        }
        
        public void MarkBombAsSpawned(int bombId)
        {
            _spawnedBombIds.Add(bombId);
        }

        public bool WasBombSpawned(int bombId)
        {
            return _spawnedBombIds.Contains(bombId);
        }


        public void CreateOccupiersOver(float lifeTime, Coords bottomLeft, Coords topRight)
        {
            for (int x = bottomLeft.X; x <= topRight.X; x++)
            for (int y = bottomLeft.Y; y <= topRight.Y; y++)
            {
                var pos = new Vector2(x, y);
                CreateStaticOccupier(lifeTime, pos);
            }
        }
        
        public void CreateOccupiersOver(float lifeTime, List<Coords> coordsList)
        {
            foreach (var pos in coordsList)
            {
                CreateStaticOccupier(lifeTime, new Vector2(pos.X, pos.Y));
            }
        }
        
        public void CreatePropellerTarget(Coords targetCoords, int propellerId)
        {
            var propellerTarget = new PropellerTarget(propellerId,
                targetCoords.ToUnityVector2(),  _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(propellerTarget);
        }
        
        public void CreateSkunkTarget(Coords targetCoords, int skunkId)
        {
            var skunkTarget = new SkunkTarget(skunkId,
                targetCoords.ToUnityVector2(), _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(skunkTarget);
        }
        
        public void CreateFireWorksTarget(Coords targetCoords, int fireWorksId)
        {
            var fireWorksTarget = new FireWorksTarget(fireWorksId,
                targetCoords.ToUnityVector2(),  _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(fireWorksTarget);
        }

        public RocketTarget CreateRocketTarget(int rocketId, Coords coord, Action<Coords> afterDeletedCallback)
        {
            var target = new RocketTarget(rocketId, coord, _boardObjectIdCounter++, afterDeletedCallback);
            _tileTickPlayer.AddObject(target);
            return target;
        }
        
        public TukTukTarget CreateTukTukTarget(int tuktukId, Coords coord, Action<Coords> afterDeletedCallback)
        {
            var target = new TukTukTarget(tuktukId, coord, _boardObjectIdCounter++, afterDeletedCallback);
            _tileTickPlayer.AddObject(target);
            return target;
        }
        
        public DirectionalBoosterTarget CreateDirectionBoosterTarget(int boosterId, Coords coords)
        {
            var target = new DirectionalBoosterTarget(boosterId, coords, _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(target);
            return target;
        }

        public BoltTarget CreateBoltTarget(int boltId, Coords coord, float activationDelay = 0f)
        {
            var target = new BoltTarget(boltId, coord, _boardObjectIdCounter++, activationDelay);
            _tileTickPlayer.AddObject(target);
            return target;
        }

        public void CreateWhirlpoolTarget(int whirlpoolId, Coords coords, float lifeTime,
            Action<Coords> afterDeletedCallback)
        {
            var target = new WhirlpoolTarget(whirlpoolId, coords, lifeTime, _boardObjectIdCounter++, afterDeletedCallback);
            _tileTickPlayer.AddObject(target);
        }
        
        public void CreateBombTarget(int bombId, Coords coords, float lifeTime)
        {
            var target = new BombTarget(bombId, coords, lifeTime, _boardObjectIdCounter++);
            _tileTickPlayer.AddObject(target);
        }

        public FreeFallTarget GetFreeFallTarget(Coords coords)
        {
            return _freeFallTargetPriority[coords];
        }

        public void ClearFreeFallTargetPriority()
        {
            _futureTargetLocks.Clear();
            _freeFallTargetPriority.Clear();
            _spawnedBombIds.Clear();
        }
        
        public void BuildFreeFallTargetPriority(IEnumerable<Match3ActionBase> allActions)
        {
            foreach (var action in allActions)
            {
                var targetPriorityAction = action as IFreeFallTargetPriority;
                if (targetPriorityAction == null) continue;

                if (!_freeFallTargetPriority.ContainsKey(targetPriorityAction.Coords))
                {
                    _freeFallTargetPriority.Add(targetPriorityAction.Coords, new FreeFallTarget());
                }
                
                _freeFallTargetPriority[targetPriorityAction.Coords].AddToPriorityQueue(targetPriorityAction.TileId);
                AddFutureTargetLock(targetPriorityAction.Coords, targetPriorityAction.TileId, action.Priority);
            }
        }

        public bool AreThereFutureTargetLocks(Coords coords, int priority)
        {
            foreach (var futureTargetLock in _futureTargetLocks)
            {
                if (futureTargetLock.Coords.Equals(coords) && futureTargetLock.Priority < priority)
                {
                    return true;
                }
            }
            return false;
        }

        public void AddFutureTargetLock(Coords coords, int tileId, int priority)
        {
            _futureTargetLocks.RemoveAll(_ => _.TileId == tileId);
            _futureTargetLocks.Add(new FutureTargetLock
            {
                Coords = coords,
                TileId = tileId,
                Priority = priority
            });
        }

        public void RemoveFutureTargetLock(Coords coords, int tileId)
        {
            _futureTargetLocks.RemoveAll(_ => _.TileId == tileId && _.Coords.Equals(coords));
        }

        public void NotifyTileRemoved(int tileId)
        {
            _futureTargetLocks.RemoveAll(_ => _.TileId == tileId);
            foreach (var freeFallTarget in _freeFallTargetPriority)
            {
                freeFallTarget.Value.NotifyTileRemoved(tileId);
            }
        }
    }
}