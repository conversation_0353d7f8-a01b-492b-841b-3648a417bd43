namespace BBB.Match3.Systems
{
    public class BoltTarget : BoardObjectBase
    {
        public Coords Coords { get; }
        public int BoltId { get; }

        public bool WaitConditionStarted;
        private readonly bool _autoDestroyable;
        private float _activationDelay;

        // BoltTarget is used from BoltWaitCondition as a way to prevent Match3 Actions to continue until this object
        // is destroyed.
        // Lifetime of BoltTarget can be handled from outside (for example, to coordinate damage on all tiles at the
        // same time for regular bolt+tile combo) or is can be auto-destroyed (for example, in the case of bolt+propeller
        // combo, where each propeller flight action needs to be unblocked 1 by 1) 
        public BoltTarget(int boltId, Coords coords, int id, float activationDelay) : base(id)
        {
            BoltId = boltId;
            Coords = coords;
            Position = coords.ToUnityVector2();
            StartPosition = Position;
            WaitConditionStarted = false;
            _autoDestroyable = activationDelay > 0;
            _activationDelay = activationDelay;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if (!_autoDestroyable) return;
            
            if (!WaitConditionStarted) return;
            
            _activationDelay -= deltaTime;
            if (_activationDelay <= 0)
            {
                Delete(proxy);
            }
        }
    }
}