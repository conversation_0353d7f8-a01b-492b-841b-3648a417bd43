namespace BBB.Match3.Systems
{
    public class BombTarget : BoardObjectBase
    {
        public Coords Coords { get; }
        public int BombId { get; }
        private float _timer;
        
        public BombTarget(int bombId, Coords coords, float lifeTime, int id) : base(id)
        {
            BombId = bombId;
            Coords = coords;
            
            _timer = lifeTime;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if (_timer > 0f)
            {
                _timer -= deltaTime;

                if (_timer <= 0f)
                {
                    Delete(proxy);
                }
            }
        }
    }
}