using System.Collections.Generic;
using BBB.Match3.Renderer;
using BBB.MMVibrations.Plugins;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class DirectionalBooster : BoardObjectBase
    {
        private const float LifeTime = 4f;
        public readonly int BoosterId;
        private readonly List<DirectionalBoosterTarget> _directionalBoosterTargets;
        private readonly Vector2 _direction;
        private readonly M3Settings _settings;
        private readonly FxType _fxType;
        private DirectionalBoosterEffect _view;
        private float _timer;
        private float _speed;
        private readonly float _acceleration;
        private readonly float _anticipationDelay;
        private bool _anticipationAnimationCompleted;
        private Coords _currentCoords;

        public DirectionalBooster(int boosterId, List<DirectionalBoosterTarget> directionalBoosterTargets, 
            Vector2 direction, Vector2 startPosition, float speed, float acceleration, float anticipationDelay, int id, FxType fxType) : base(id)
        {
            BoosterId = boosterId;
            _directionalBoosterTargets = directionalBoosterTargets;
            _direction = direction;
            Position = startPosition;
            StartPosition = startPosition;
            _currentCoords = Coords.OutOfGrid;
            _timer = LifeTime;
            _speed = speed;
            _acceleration = acceleration;
            _fxType = fxType;
            _anticipationDelay = anticipationDelay;
            if (_anticipationDelay == 0)
            {
                _anticipationAnimationCompleted = true;
            }
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if(_anticipationAnimationCompleted == false) return;
            
            _speed += Mathf.Abs(_acceleration * deltaTime);
            Position += _speed * deltaTime * _direction;
            var currentCoords = new Coords(Position);
            if (proxy.GameController.Grid.TryGetCell(currentCoords, out var cell) && _currentCoords != currentCoords)
            {
                proxy.Vibrations.PlayHaptic(ImpactPreset.LightImpact);
                _currentCoords = currentCoords;
            }

            if (_timer > 0f)
            {
                _timer -= deltaTime;

                if (_timer <= 0f)
                {
                    Delete(proxy);
                }
                else
                {
                    foreach (var target in _directionalBoosterTargets)
                    {
                        if (target.Deleted)
                            continue;
                        
                        if (OverlappedOrPassed(target.Coords, proxy))
                        {
                            target.Delete(proxy);
                        }
                    }
                }
            }
        }

        public override void SpawnView(RendererContainers rendererContainers)
        {
            _view = rendererContainers.SpawnFx<DirectionalBoosterEffect>(_fxType);
        }
        
        public override void UpdateView(PlaySimulationActionProxy proxy, bool isSpawnView)
        {
            if (_view == null) return;
            Rx.Invoke(_anticipationDelay, _ =>
            {
                _anticipationAnimationCompleted = true;
            });

            if (isSpawnView)
            { 
                _view.transform.localPosition = proxy.GridController.ToLocalPosition(StartPosition);
            }
            else if (_anticipationAnimationCompleted)
            {
                _view.transform.localPosition =  proxy.GridController.ToLocalPosition(Position);
            }
        }
        

        protected override void ReleaseView(PlaySimulationActionProxy playSimulationActionProxy)
        {
            if (_view != null && _view.gameObject != null)
            {
                _view.gameObject.Release();
            }

            _view = null;
        }

        public override void AfterDeleted(PlaySimulationActionProxy proxy)
        {
            foreach (var target in _directionalBoosterTargets)
            {
                if (target.Deleted)
                    continue;
                        
                target.Delete(proxy);
            }

            proxy.TileTickPlayer.AddToTrashBin(this);
        }

        public override bool Overlaps(Vector2 worldPos)
        {
            return _view.OverlapPoint(worldPos);
        }

        private bool Passed(Vector2 hitGridPos)
        {
            var dir = _direction;
            var pos = Position;

            var dirToTarget = hitGridPos-StartPosition;

            var dotProduct = Vector2.Dot(dir, dirToTarget);
            
            //if vectors are perpendicular, dot product will be 0
            //if they are collinear but face opposite directions, dot product will be negative
            //we need to make sure this rocket affects only tiles on its side, so we need to
            //guarantee dirToTarget and dir will have same direction

            if (dotProduct > 0f || dirToTarget.magnitude == 0)
            {
                var xAxisDir = dir.x != 0f;
                var nonZeroDirComponent = xAxisDir ? dir.x : dir.y;
                var borderComponentValue = xAxisDir ? hitGridPos.x : hitGridPos.y;
                var posComponentValue = xAxisDir ? pos.x : pos.y;

                return nonZeroDirComponent > 0f ? 
                    posComponentValue >= borderComponentValue : 
                    posComponentValue <= borderComponentValue;
            }

            return false;
        }

        public bool OverlappedOrPassed(Coords coords, PlaySimulationActionProxy proxy)
        {
            if (!_anticipationAnimationCompleted) return false;
            var hitGridPos = coords.ToUnityVector2();
            var dirToRocket = Position - hitGridPos;
            dirToRocket.Normalize();
            var collisionOffset = dirToRocket * proxy.Settings.TileCollisionRadius;
            var hitWorldPos = proxy.GridController.ToDisplacedWorldPosition(hitGridPos + collisionOffset);
            return Overlaps(hitWorldPos) || Passed(hitGridPos);
        }
    }
}