using System;

namespace BBB.Match3.Systems
{
    public class DirectionalBoosterTarget : BoardObjectBase
    {
        private int BoosterId { get; }
        public Coords Coords { get; }
        private Action<Coords> _afterDeletedCallback;

        public override bool CanBlockRect => true;

        public DirectionalBoosterTarget (int boosterId, Coords coords, int id) : base(id)
        {
            BoosterId = boosterId;
            Coords = coords;
            Position = coords.ToUnityVector2();
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            
        }
    }
}