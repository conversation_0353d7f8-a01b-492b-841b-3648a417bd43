using UnityEngine;

namespace BBB.Match3.Systems
{
    public class FireWorksTarget : BoardObjectBase
    {
        public int FireWorksId { get; }

        public override bool CanBlockRect => false;

        public FireWorksTarget(int fireWorksId, Vector2 startPosition, int id) : base(id)
        {
            Position = startPosition;
            StartPosition = startPosition;
            FireWorksId = fireWorksId;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
           
        }
    }
}