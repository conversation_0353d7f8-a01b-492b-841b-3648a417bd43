using System.Collections.Generic;

namespace BBB.Match3.Systems
{
    public class FreeFallTarget
    {
        private readonly LinkedList<int> _freeFallPath = new LinkedList<int>();

        public void AddToPriorityQueue(int tileId)
        {
            _freeFallPath.AddLast(tileId);
        }
        
        public bool TryMoveToCell(int tileId)
        {
            if (_freeFallPath.Count <= 0) return false;

            var freeFallTileId = _freeFallPath.First.Value;
            if (freeFallTileId != tileId) return false;
            
            _freeFallPath.RemoveFirst();
            
            return true;
        }

        public void NotifyTileRemoved(int tileId)
        {
            _freeFallPath.Remove(tileId);
        }
    }
}