using UnityEngine;

namespace BBB.Match3.Systems
{
    public class PropellerTarget : BoardObjectBase
    {
        public int PropellerId => _propellerId;
        public override bool CanBlockRect => false;
        
        private readonly int _propellerId;

        public PropellerTarget(int propellerId, Vector2 startPosition, int id) : base(id)
        {
            Position = startPosition;
            StartPosition = startPosition;
            _propellerId = propellerId;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
           
        }
    }
}