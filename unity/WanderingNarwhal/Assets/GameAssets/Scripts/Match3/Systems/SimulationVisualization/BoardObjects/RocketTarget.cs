using System;

namespace BBB.Match3.Systems
{
    public class RocketTarget : BoardObjectBase
    {
        public int LineBreakerId { get; }
        public Coords Coords { get; }
        private Action<Coords> _afterDeletedCallback;

        public override bool CanBlockRect => false;

        public RocketTarget(int lineBreakerId, Coords coords, int id, Action<Coords> afterDeletedCallback) : base(id)
        {
            LineBreakerId = lineBreakerId;
            Coords = coords;
            Position = coords.ToUnityVector2();

            _afterDeletedCallback = afterDeletedCallback;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            
        }
    }
}