using UnityEngine;

namespace BBB.Match3.Systems
{
    public class SkunkTarget : BoardObjectBase
    {
        public int SkunkId { get; }

        public override bool CanBlockRect => false;

        public SkunkTarget(int skunkId, Vector2 startPosition, int id) : base(id)
        {
            Position = startPosition;
            StartPosition = startPosition;
            SkunkId = skunkId;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
        }
    }
}