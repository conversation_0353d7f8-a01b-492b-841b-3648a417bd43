using UnityEngine;

namespace BBB.Match3.Systems
{
    public class StaticInvisibleOccupier : BoardObjectBase
    {
        private float _timer = 0f;

        public override bool CanBlockRect => true;

        public StaticInvisibleOccupier(float lifeTime, Vector2 position, int id) : base(id)
        {
            _timer = lifeTime;
            Position = position;
            StartPosition = position;
        }
        
        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if (_timer > 0f)
            {
                _timer -= deltaTime;

                if (_timer <= 0f)
                {
                    Delete(proxy);
                }
            }
        }
    }
}