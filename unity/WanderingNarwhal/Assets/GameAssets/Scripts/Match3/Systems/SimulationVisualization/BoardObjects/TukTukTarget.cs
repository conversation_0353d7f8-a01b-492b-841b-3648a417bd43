using System;

namespace BBB.Match3.Systems
{
    public class TukTukTarget : BoardObjectBase
    {
        public int TukTukId { get; }
        public Coords Coords { get; }
        private Action<Coords> _afterDeletedCallback;

        public override bool CanBlockRect => false;

        public TukTukTarget(int tukTukId, Coords coords, int id, Action<Coords> afterDeletedCallback) : base(id)
        {
            TukTukId = tukTukId;
            Coords = coords;
            Position = coords.ToUnityVector2();
            _afterDeletedCallback = afterDeletedCallback;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            
        }

        public override void AfterDeleted(PlaySimulationActionProxy proxy)
        {
            _afterDeletedCallback?.Invoke(Coords);
            _afterDeletedCallback = null;
        }
    }
}