using System;

namespace BBB.Match3.Systems
{
    public class WhirlpoolTarget : BoardObjectBase
    {
        private readonly int _whirlPoolId;
        private readonly Coords _coords;
        private float _timer;

        public Coords Coords => _coords;
        public int WhirlpoolId => _whirlPoolId;

        private Action<Coords> _afterDeletedCallback;

        public WhirlpoolTarget(int whirlPoolId, Coords coords, float lifeTime, int id,
            Action<Coords> afterDeletedCallback) : base(id)
        {
            _whirlPoolId = whirlPoolId;
            _coords = coords;
            _timer = lifeTime;

            _afterDeletedCallback = afterDeletedCallback;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            if (_timer > 0f)
            {
                _timer -= deltaTime;

                if (_timer <= 0f)
                {
                    Delete(proxy);
                }
            }
        }

        public override void AfterDeleted(PlaySimulationActionProxy proxy)
        {
            _afterDeletedCallback?.Invoke(_coords);
            _afterDeletedCallback = null;
        }
    }
}