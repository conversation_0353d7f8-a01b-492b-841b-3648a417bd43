namespace BBB.Match3.Systems
{
    public interface ITickPlayerProxy
    {
        bool Occupied(Coords coords);
        bool CoordsAreBlocked(Coords coords);
        bool TryMoveTo(int tileId, Coords coords);
        void RemoveFromWaitingCoords(Coords coords);
        void AddToWaitingCoords(Coords coords, Coords waitingForCoords);
        bool IsWaitingForCoords(Coords coords, Coords waitingForCoords, Coords? originalCoords = null);
    }
}