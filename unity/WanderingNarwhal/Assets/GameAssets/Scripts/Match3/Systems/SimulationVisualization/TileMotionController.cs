using System;
using System.Collections.Generic;
using BBB.Core;
using BebopBee.Core;
using Bebopbee.Core.Utility;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public interface IPositionUpdatable
    {
        void UpdatePositionDependentProperties();
    }

    public interface IFadable
    {
        void ActivateFadeIn(float start, float end);
    }

    public class TileMotionController
    {
        private readonly IPositionUpdatable _positionUpdatable;
        private readonly IFadable _fadable;
        private M3Settings _settings;
        private PendingMove _pendingMove;

        public TileMotionController(IPositionUpdatable positionUpdatable, IFadable fadable)
        {
            _positionUpdatable = positionUpdatable;
            _fadable = fadable;
            ResetPendingMove();
        }

        public void ApplySettings(M3Settings settings)
        {
            _settings = settings;
        }

        public TileTransformMode Mode
        {
            get => _mode;
            private set { _mode = value; }
        }

        private TileTransformMode _mode;
        private TileTransformMode _previousFrameMode;

        private Vector2 _position;
        private Vector2 _previousPosition;

        public int TileId => _tileId;

        public Vector2 TargetPosition => _settleTarget?.ToUnityVector2() ?? Position;

        public Vector2 Position
        {
            get => _position;
            private set
            {
                _position = value;
                _positionUpdatable.UpdatePositionDependentProperties();
            }
        }

        public Coords Coords => new Coords((int)(Position.x), (int)(Position.y));

        private Coords? _startFallCoords;
        private Coords? _settleTarget;
        private Coords? _currentlyOnCoords;

        public Coords CurrentlyOnCoords
        {
            get => _currentlyOnCoords ?? Coords;
            private set
            {
                if (_freeFallPath.Count > 0 && !_freeFallPath.Contains(value))
                {
                    BDebug.LogError(LogCat.General,
                        $"ERROR current coords are NOT in free fall path: tileId={_tileId} Position={Position} currentlyOnCoords={_currentlyOnCoords} coords={Coords} value={value}");
                }

                _currentlyOnCoords = value;
            }
        }

        private float _acceleratingTimer = 0f;
        private float _verticalSpeed = 0f;
        private readonly List<Coords> _freeFallPath = new();
        private int _tileId = -1;
        private Action _onSettleCallback;
        private float _blockedTimer;

        private int _lastCheckpointIndex;
        private Tweener _cellReleaseTweener;
        private Action _cellReleaseCallback;

        public void SetPosAndFreeze(Vector2 pos)
        {
            Mode = TileTransformMode.Frozen;
            ResetVelocity();
            Position = pos;
            _freeFallPath.Clear();
            ResetPendingMove();
            CurrentlyOnCoords = new Coords(pos);
            _lastCheckpointIndex = 0;

            _onSettleCallback?.Invoke();
            _onSettleCallback = null;
            _cellReleaseCallback?.Invoke();
            _cellReleaseCallback = null;
            if (_cellReleaseTweener != null && _cellReleaseTweener.IsActive())
                _cellReleaseTweener.Kill();
        }

        public void QuadLerpPos(Vector2 first, Vector2 second, float factor)
        {
            Position = Vector2.Lerp(first, second, Mathf.Sqrt(factor));
        }

        public void HandleSwapStart()
        {
            Mode = TileTransformMode.SwapControlled;
        }

        public void SetToZeroGravity()
        {
            Mode = TileTransformMode.ZeroGravity;
        }

        public void MakeFreeFallToTarget(List<Coords> path, Coords target, int tileId,
            Action onSettled, Action cellReleaseCallback = null)
        {
            _tileId = tileId;

            if (path == null || path.Count == 0)
            {
                BDebug.LogError(LogCat.General, "Trying to set " + (path == null ? "null" : "empty") + " path");
                _startFallCoords = target;
            }
            else
            {
                _startFallCoords = path[0];
                _freeFallPath.AddRange(path);
            }

            if (!_currentlyOnCoords.HasValue) CurrentlyOnCoords = Coords;

            if (_verticalSpeed == 0f)
                _verticalSpeed = _settings.TileFallingSettings.StartFallingSpeed;

            _settleTarget = target;
            Mode = TileTransformMode.FreeFall;
            _onSettleCallback = onSettled;
            _cellReleaseCallback = cellReleaseCallback;
        }

        private Vector2 GetCandidatePosition(float deltaTime, out Coords expectedNextCoord)
        {
            expectedNextCoord = Coords.OutOfGrid;

            var distanceTraveled = -_verticalSpeed * deltaTime;
            var lastCheckpoint = Position;
            var startingIndex = _currentlyOnCoords.HasValue && _freeFallPath.Contains(_currentlyOnCoords.Value)
                ? _freeFallPath.IndexOf(_currentlyOnCoords.Value, _lastCheckpointIndex)
                : 0;
            var maxIndex = _freeFallPath.Count - 1;

            for (var i = startingIndex; i <= maxIndex; i++)
            {
                var coords = _freeFallPath[i];
                var checkpoint = coords.ToUnityVector2();
                if (lastCheckpoint.y < checkpoint.y)
                {
                    // If we are lower then the checkpoint, continue to next checkpoint
                    continue;
                }

                var distanceToNextCheckpoint = Vector2.Distance(lastCheckpoint, checkpoint);
                lastCheckpoint = Vector2.MoveTowards(lastCheckpoint, checkpoint, distanceTraveled);
                distanceTraveled -= distanceToNextCheckpoint;

                // Did we reach our destination?
                var reachedDestination = distanceTraveled <= 0f;

                // Is this the last allowed checkpoint?
                var isLastAllowedCheckpoint = i == maxIndex;

                if (reachedDestination || isLastAllowedCheckpoint)
                {
                    expectedNextCoord = _freeFallPath[i];
                    break;
                }
            }

            if (distanceTraveled > 0 && maxIndex == _freeFallPath.Count - 1)
            {
                // Travelled so far that we reached the end of the way
                expectedNextCoord = _freeFallPath[maxIndex];
                return expectedNextCoord.ToUnityVector2();
            }

            return lastCheckpoint;
        }

        private float GetCurrentVelocity(float deltaTime)
        {
            if (_startFallCoords.HasValue)
            {
                var y = Position.y;
                _acceleratingTimer += deltaTime;
                var timeRatio = _acceleratingTimer / _settings.TileFallingSettings.TimeToReachMaxSpeed;
                timeRatio = Mathf.Clamp01(timeRatio);

                if (timeRatio >= 1f)
                    return _settings.TileFallingSettings.MaxFallingSpeed;

                var speedChangeRatio = _settings.TileFallingSettings.StartToMaxSpeedCurve.Evaluate(timeRatio);
                var currentVelocity = Mathf.Lerp(_settings.TileFallingSettings.StartFallingSpeed,
                    _settings.TileFallingSettings.MaxFallingSpeed, speedChangeRatio);

                return currentVelocity;
            }

            return _settings.TileFallingSettings.StartFallingSpeed;
        }

        public void Clean()
        {
            _mode = TileTransformMode.Frozen;
            _previousFrameMode = TileTransformMode.Frozen;
            _previousPosition = Vector2.zero;

            Position = Vector2.zero;
            _startFallCoords = null;
            _settleTarget = null;
            _acceleratingTimer = 0f;
            _verticalSpeed = 0f;
            _freeFallPath.Clear();
            ResetPendingMove();
            _tileId = -1;
            _currentlyOnCoords = null;
            _blockedTimer = 0f;
            _lastCheckpointIndex = 0;

            _onSettleCallback?.Invoke();
            _onSettleCallback = null;
            _cellReleaseCallback?.Invoke();
            _cellReleaseCallback = null;
            if (_cellReleaseTweener != null && _cellReleaseTweener.IsActive())
                _cellReleaseTweener.Kill();
        }

        private void ResetVelocity()
        {
            _verticalSpeed = _settings.TileFallingSettings.StartFallingSpeed;
            _acceleratingTimer = 0f;
        }

        public TileTransformUpdateResult Update(float deltaTime, MotionControlProxy proxy)
        {
            _previousPosition = Position;

            switch (_mode)
            {
                case TileTransformMode.Blocked:
                case TileTransformMode.FreeFall:
                {
#if BBB_LOG
                    if (_mode == TileTransformMode.Blocked)
                    {
                        const float blockedLimit = 7f;

                        var previousTimer = _blockedTimer;
                        _blockedTimer += deltaTime;

                        if (blockedLimit > previousTimer && _blockedTimer > blockedLimit)
                        {
                            BDebug.LogError(LogCat.Match3, $"TimeMotionController with id {_tileId} BLOCKED FOR TOO LONG at position {Position}");
                        }
                    }
                    else
                    {
                        _blockedTimer = 0f;
                    }
#endif

                    if (_startFallCoords.HasValue && _settleTarget.HasValue && _settings != null)
                    {
                        // If current coords are blocked (an static occupier has been created on this coords), do not fall. 
                        if (_currentlyOnCoords.HasValue &&
                            proxy.TickPlayerProxy.CoordsAreBlocked(_currentlyOnCoords.Value))
                        {
                            break;
                        }

                        var settleTargetCoords = _settleTarget.Value;
                        var candidatePosition = GetCandidatePosition(deltaTime, out Coords candidateCoords);

                        if (FloatUtility.IsEqual(Position.x, candidatePosition.x) &&
                            FloatUtility.IsEqual(Position.y, candidatePosition.y))
                            break;

                        if (candidatePosition.y > settleTargetCoords.Y)
                        {
                            // If candidate coords is not the same as the coords we are in, we should check if the tile
                            // can be moved (visually) to its next position. There are 2 scenarios here where the tile
                            // will continue to fall:
                            // 1. If the coords we want to go to is not occupied
                            // 2. If the coords we want to go to is occupied, but we detect there's a circular dependency
                            //    between coords (meaning, candidateCoords is waiting for _currentlyOnCoords to not be 
                            //    occupied and at the same time, _currentlyOnCoords is waiting (indirectly) for candidateCoords
                            //    to not be occupied. See IsWaitingForCoords method description for more context
                            if (HasPendingMove())
                            {
                                _pendingMove.FrameToWait--;
                                if (_pendingMove.FrameToWait <= 0)
                                {
                                    MoveTile(proxy, _pendingMove.CandidatePosition, _pendingMove.CandidateCoords);
                                }
                            }
                            else if (candidateCoords.Equals(_currentlyOnCoords) ||
                                (!proxy.TickPlayerProxy.Occupied(candidateCoords) || proxy.TickPlayerProxy.IsWaitingForCoords(candidateCoords, _currentlyOnCoords.Value))
                                && proxy.TickPlayerProxy.TryMoveTo(_tileId, candidateCoords))
                            {
                                _verticalSpeed = GetCurrentVelocity(deltaTime);
                                if (Mode == TileTransformMode.Blocked)
                                {
                                    // If we are in Blocked mode, we should wait for a frame before moving
                                    _pendingMove.CandidateCoords = candidateCoords;
                                    _pendingMove.CandidatePosition = candidatePosition;
                                }
                                else
                                {
                                    MoveTile(proxy, candidatePosition, candidateCoords);
                                }
                            }
                            else
                            {
                                if (Mode != TileTransformMode.Blocked)
                                {
                                    proxy.TickPlayerProxy.AddToWaitingCoords(_currentlyOnCoords.Value, candidateCoords);
                                    ResetVelocity();
                                    Position = new Vector2(CurrentlyOnCoords.X, CurrentlyOnCoords.Y);
                                    Mode = TileTransformMode.Blocked;
                                }
                            }
                        }
                        else
                        {
                            SetPosAndFreeze(new Vector2(settleTargetCoords.X, settleTargetCoords.Y));
                        }
                    }
                    else
                    {
                        BDebug.LogError(LogCat.General, "Settle target is supposed to not be null in FreeFall mode");
                    }

                    break;
                }
            }

            var result = TileTransformUpdateResult.None;

            if (_previousFrameMode == TileTransformMode.FreeFall && Mode is TileTransformMode.Blocked or TileTransformMode.Frozen)
            {
                result = TileTransformUpdateResult.Settle;
            }
            else if (_previousFrameMode is TileTransformMode.Blocked or TileTransformMode.Frozen && Mode == TileTransformMode.FreeFall)
            {
                result = TileTransformUpdateResult.JustStartedFreeFall;
                if (_settings != null)
                {
                    _cellReleaseTweener = Rx.Invoke(_settings.TileFallingSettings.TimeToReleaseCell, _ => _cellReleaseCallback?.Invoke());
                }
                else
                {
                    BDebug.LogError(LogCat.General, "_motionSettings is null");
                    _cellReleaseCallback?.Invoke();
                }
            }

            _previousFrameMode = _mode;
            return result;
        }

        private bool HasPendingMove()
        {
            return _pendingMove.CandidateCoords != Coords.OutOfGrid;
        }

        private void MoveTile(MotionControlProxy proxy, Vector2 candidatePosition, Coords candidateCoords)
        {
            if (_currentlyOnCoords != null)
            {
                proxy.TickPlayerProxy.RemoveFromWaitingCoords(_currentlyOnCoords.Value);
            }
            Position = candidatePosition;
            CurrentlyOnCoords = candidateCoords;
            Mode = TileTransformMode.FreeFall;

            // Reset pending move since we successfully moved the tile
            ResetPendingMove();
        }

        private void ResetPendingMove()
        {
            _pendingMove.CandidateCoords = Coords.OutOfGrid;
            _pendingMove.CandidatePosition = Vector2.zero;
            _pendingMove.FrameToWait = 2;
        }

        private struct PendingMove
        {
            public Vector2 CandidatePosition;
            public Coords CandidateCoords;
            public int FrameToWait;
        }
    }
}