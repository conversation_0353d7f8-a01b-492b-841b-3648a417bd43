using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Renderer;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class TileTickPlayer : IContextInitializable, ITickPlayerProxy
    {
        private TileController _tileController;
        private IUpdateDispatcher _updateDispatcher;
        private RendererContainers _rendererContainers;
        private BoardObjectFactory _factory;
        public BoardObjectFactory BoardObjectFactory => _factory;

        private readonly List<BoardObjectBase> _boardObjects = new List<BoardObjectBase>();

        //additional cache list needed to remove deleted objects from the main list
        private readonly List<BoardObjectBase> _tempObjects = new List<BoardObjectBase>();
        private PlaySimulationActionProxy _proxy;
        private MotionControlProxy _motionProxy;
        private readonly HashSet<BoardObjectBase> _trashBin = new HashSet<BoardObjectBase>();
        private readonly Dictionary<Coords, Coords> _waitingForCoords = new Dictionary<Coords, Coords>();
        private readonly CoordsBoardObjectPredicate _coordsPredicate = new ();

        public void InitializeByContext(IContext context)
        {
            _tileController = context.Resolve<TileController>();
            _rendererContainers = context.Resolve<RendererContainers>();
            _factory = new BoardObjectFactory(context);
            _proxy = new PlaySimulationActionProxy(context);
            _motionProxy = new MotionControlProxy
            {
                FxRenderer = context.Resolve<FxRenderer>(),
                TickPlayerProxy = this
            };
            _updateDispatcher = context.Resolve<IUpdateDispatcher>();
        }

        public void Launch()
        {
            Stop();
            _updateDispatcher.OnFixedUpdate += OnFixedUpdateHandler;
        }

        public void Stop()
        {
            _updateDispatcher.OnFixedUpdate -= OnFixedUpdateHandler;
            foreach (var obj in _boardObjects)
                obj.Delete(_proxy);

            _boardObjects.Clear();
            _tempObjects.Clear();
            _trashBin.Clear();
            _waitingForCoords.Clear();
        }

        public void AddObject(BoardObjectBase boardObject)
        {
            boardObject.SpawnView(_rendererContainers);
            boardObject.UpdateView(_proxy, true);
            _boardObjects.Add(boardObject);
        }

        public bool AnyActiveObjectSatisfiesPredicate(IBoardObjectPredicate boardObjectPredicate)
        {
            foreach (var obj in _boardObjects)
            {
                boardObjectPredicate.BoardObject = obj;
                if (boardObjectPredicate.Evaluate() && !obj.Deleted)
                {
                    return true;
                }
            }

            return false;
        }

        public bool AnyTrashBinObjectSatisfiesPredicate(IBoardObjectPredicate boardObjectPredicate)
        {
            foreach (var obj in _trashBin)
            {
                boardObjectPredicate.BoardObject = obj;
                if (boardObjectPredicate.Evaluate())
                {
                    return true;
                }
            }

            return false;
        }

        public void OnFixedUpdateHandler()
        {
            var deltaTime = Time.fixedDeltaTime;

            _tempObjects.Clear();

            foreach (var boardObject in _boardObjects)
            {
                boardObject.Tick(deltaTime, _proxy);
                _tempObjects.Add(boardObject);
            }
            var orderedTileViews = _tileController.GetAllTileViewsOrdered();
            foreach (var tileView in orderedTileViews)
            {
                if (!tileView.isActiveAndEnabled)
                    continue;

                var tileTransform = tileView.TileMotionController;
                var updateResult = tileTransform.Update(deltaTime, _motionProxy);

                switch (updateResult)
                {
                    case TileTransformUpdateResult.JustStartedFreeFall:
                    {
                        tileView.Animator.Fall();
                        break;
                    }
                    case TileTransformUpdateResult.Settle:
                    {
                        tileView.Animator.Settle();
                        // SK: this (or similar solution) is required for the most bottom row
                        tileView.SetAlpha(1f);
                        tileView.transform.localScale = Vector3.one;
                        break;
                    }
                }
            }

            _boardObjects.Clear();
            foreach (var obj in _tempObjects)
            {
                if (obj.Deleted)
                {
                    obj.AfterDeleted(_proxy);
                }
                else
                {
                    _boardObjects.Add(obj);
                }
            }
        }

        public bool Occupied(Coords coords)
        {
            foreach (var tileView in _tileController.GetAllTileViews())
            {
                if (!tileView.isActiveAndEnabled)
                    continue;

                var currentPos = tileView.TileMotionController.CurrentlyOnCoords;

                if (currentPos.X == coords.X && currentPos.Y == coords.Y)
                    return true;
            }

            return CoordsAreBlocked(coords);
        }

        public bool CoordsAreBlocked(Coords coords)
        {
            _coordsPredicate.Coords = coords;
            return AnyActiveObjectSatisfiesPredicate(_coordsPredicate);
        }

        public bool TryMoveTo(int tileId, Coords coords)
        {
            var freeFallTarget = BoardObjectFactory.GetFreeFallTarget(coords);
            return freeFallTarget != null && freeFallTarget.TryMoveToCell(tileId);
        }

        public void RemoveFromWaitingCoords(Coords coords)
        {
            _waitingForCoords.Remove(coords);
        }

        public void AddToWaitingCoords(Coords coords, Coords waitingForCoords)
        {
            if (!_waitingForCoords.TryAdd(coords, waitingForCoords))
            {
                BDebug.LogError(LogCat.Match3, $"Tile at {coords} is already waiting for {waitingForCoords}. " +
                    "leading to deadlocks if the same tile is waiting for multiple tiles.");
            }
        }

        /// <summary>
        /// This method is used to detect deadlocks on falling tiles. For example, if tile A is blocked by tile B, while
        /// tile B is blocked tile C, and tile C is blocked by tile A.
        /// </summary>
        /// <returns>True if 'coords' is waiting for 'waitingForCoords'. False otherwise</returns>
        public bool IsWaitingForCoords(Coords coords, Coords waitingForCoords, Coords? originalCoords = null)
        {
            while (true)
            {
                if (!_waitingForCoords.ContainsKey(coords) || originalCoords.HasValue && originalCoords.Value.Equals(coords)) return false;

                var waitingFor = _waitingForCoords[coords];
                if (waitingFor.Equals(waitingForCoords)) return true;

                var coords1 = coords;
                coords = waitingFor;
                originalCoords ??= coords1;
            }
        }

        public bool NotFrozen(int tileId)
        {
            var tileView = _tileController.GetTileViewById(tileId, false);
            return tileView != null && tileView.TileMotionController.Mode != TileTransformMode.Frozen;
        }

        public bool NotFrozen(Coords[] tileCoords)
        {
            if (tileCoords == null)
                return false;

            var notFrozen = false;
            foreach (var coord in tileCoords)
            {
                var tileView = _tileController.GetTileViewByCoord(coord, false);
                notFrozen |= tileView != null && tileView.TileMotionController.Mode != TileTransformMode.Frozen;
            }

            return notFrozen;
        }

        public void DeleteObjects(Predicate<BoardObjectBase> predicate)
        {
            foreach (var obj in _boardObjects)
            {
                if (predicate(obj))
                {
                    obj.Delete(_proxy);
                }
            }
        }

        public bool AllFrozen()
        {
            var tileViews = _tileController.GetAllTileViews();

            foreach (var tileView in tileViews)
            {
                if (tileView.TileMotionController.Mode != TileTransformMode.Frozen)
                    return false;
            }

            return true;
        }

        public void AddToTrashBin(BoardObjectBase obj)
        {
            _trashBin.Add(obj);
        }
    }
}
